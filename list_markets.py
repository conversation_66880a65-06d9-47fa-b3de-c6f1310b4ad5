#!/usr/bin/env python
"""
Market Listing Tool for Quotex

This script connects to the Quotex API and lists all available markets,
with a focus on OTC markets like usdars_otc and usdbrl_otc.
It displays information about each market including whether it's open
and its payout percentage.
"""

import os
import sys
import asyncio
import configparser
from datetime import datetime
from quotexapi.stable_api import Quotex

# ASCII art banner
print("=" * 60)
print("QUOTEX MARKET LISTING TOOL")
print("=" * 60)

# Load credentials from config.ini
config = configparser.RawConfigParser()
config_path = os.path.join('settings', 'config.ini')
if os.path.exists(config_path):
    config.read(config_path)
    email = config.get('settings', 'email', fallback=None)
    password = config.get('settings', 'password', fallback=None)
else:
    email = None
    password = None

# Initialize Quotex client
client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

async def list_all_markets():
    """List all available markets with detailed information"""
    print("\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully. Retrieving markets...\n")

        # Get all assets data (includes open/closed status and payout info)
        all_data = client.get_payment()

        # Get all asset codes
        codes_asset = await client.get_all_assets()

        # Create a mapping from display name to code
        asset_codes = {}
        for code, name in codes_asset.items():
            asset_codes[name] = code

        # Print all assets
        print("-" * 80)
        print(f"{'Market Name':<30} {'Market Code':<20} {'Status':<10} {'Payout 1M':<10} {'Payout 5M':<10}")
        print("-" * 80)

        # Count statistics
        total_markets = 0
        open_markets = 0
        closed_markets = 0
        otc_markets = 0
        open_otc_markets = 0

        # Sort assets and display them
        for asset_name in sorted(all_data.keys()):
            asset_data = all_data[asset_name]
            code = asset_codes.get(asset_name, "Unknown")
            status = "🟢 Open" if asset_data["open"] else "🔴 Closed"
            profit_1m = f"{asset_data['profit']['1M']}%" if '1M' in asset_data['profit'] else "N/A"
            profit_5m = f"{asset_data['profit']['5M']}%" if '5M' in asset_data['profit'] else "N/A"

            # Update statistics
            total_markets += 1
            if asset_data["open"]:
                open_markets += 1
            else:
                closed_markets += 1
            
            is_otc = "_otc" in asset_name.lower()
            if is_otc:
                otc_markets += 1
                if asset_data["open"]:
                    open_otc_markets += 1

            # Highlight OTC markets
            if is_otc:
                print(f"\033[1m{asset_name:<30}\033[0m {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")
            else:
                print(f"{asset_name:<30} {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")

        print("-" * 80)
        print(f"Total Markets: {total_markets} (Open: {open_markets}, Closed: {closed_markets})")
        print(f"OTC Markets: {otc_markets} (Open: {open_otc_markets}, Closed: {otc_markets - open_otc_markets})")
        print("-" * 80)

        # Search functionality
        while True:
            search_term = input("\nSearch for a specific market (or press Enter to exit): ").strip().upper()
            if not search_term:
                break

            found = False
            print("\nSearch results:")
            print("-" * 80)
            print(f"{'Market Name':<30} {'Market Code':<20} {'Status':<10} {'Payout 1M':<10} {'Payout 5M':<10}")
            print("-" * 80)

            for asset_name in sorted(all_data.keys()):
                if search_term in asset_name.upper():
                    asset_data = all_data[asset_name]
                    code = asset_codes.get(asset_name, "Unknown")
                    status = "🟢 Open" if asset_data["open"] else "🔴 Closed"
                    profit_1m = f"{asset_data['profit']['1M']}%" if '1M' in asset_data['profit'] else "N/A"
                    profit_5m = f"{asset_data['profit']['5M']}%" if '5M' in asset_data['profit'] else "N/A"
                    
                    is_otc = "_otc" in asset_name.lower()
                    if is_otc:
                        print(f"\033[1m{asset_name:<30}\033[0m {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")
                    else:
                        print(f"{asset_name:<30} {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")
                    found = True

            if not found:
                print(f"No markets found matching '{search_term}'")
            print("-" * 80)

        # Check specific markets of interest
        markets_of_interest = ["USDARS_otc", "USDBRL_otc"]
        print("\nChecking specific markets of interest:")
        print("-" * 80)
        print(f"{'Market Name':<30} {'Status':<10} {'Payout 1M':<10} {'Payout 5M':<10}")
        print("-" * 80)
        
        for market in markets_of_interest:
            if market in all_data:
                asset_data = all_data[market]
                status = "🟢 Open" if asset_data["open"] else "🔴 Closed"
                profit_1m = f"{asset_data['profit']['1M']}%" if '1M' in asset_data['profit'] else "N/A"
                profit_5m = f"{asset_data['profit']['5M']}%" if '5M' in asset_data['profit'] else "N/A"
                print(f"{market:<30} {status:<10} {profit_1m:<10} {profit_5m:<10}")
            else:
                print(f"{market:<30} Not available")
        print("-" * 80)

    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def main():
    try:
        await list_all_markets()
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")
    finally:
        # Ensure client is closed
        try:
            await client.close()
        except:
            pass

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
