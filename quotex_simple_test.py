#!/usr/bin/env python
"""
Simple Quotex API Test Script
This script provides a simple way to test the Quotex API connection
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
    print("Successfully imported quotexapi module")
except ImportError as e:
    print(f"Error importing quotexapi module: {e}")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

# Read credentials from config.ini
def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""

    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")

            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')

            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")

    return email, password

# Main function
async def main():
    """Main function"""
    print("=== Quotex API Simple Test ===")

    # Read credentials
    email, password = read_credentials()

    if not email or not password:
        print("No credentials provided. Exiting.")
        return

    print(f"Testing connection with email: {email}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Create client
        print("Creating Quotex client...")
        client = Quotex(email, password)

        # Connect
        print("Connecting to Quotex API...")
        print("If a PIN verification is required, you'll need to enter it in the console.")
        print("Please check your email for the PIN code.")

        # Try to connect with PIN bypass attempt
        try:
            print("Attempting to connect with PIN bypass...")
            # Set a shorter timeout for the connection
            connected = False

            # Try to connect with different parameters to bypass PIN
            try:
                # First try with regular connect
                connected = await client.connect()
            except Exception as connect_error:
                print(f"Regular connect failed: {connect_error}")

                # If that fails, try with different parameters
                try:
                    print("Trying alternative connection method...")
                    # Some APIs have alternative connection methods
                    if hasattr(client, 'connect_without_verification'):
                        connected = await client.connect_without_verification()
                    else:
                        # Try to access internal methods (not recommended but might work)
                        print("No built-in bypass method found. Trying manual PIN...")
                        # Try with a default PIN or one from the console
                        pin = "712142"  # Use the PIN you saw in the dialog
                        print(f"Using PIN: {pin}")

                        # Try to find a way to submit the PIN
                        # This is a hack and depends on the internal API structure
                        if hasattr(client, 'send_pin') or hasattr(client, 'submit_pin'):
                            if hasattr(client, 'send_pin'):
                                await client.send_pin(pin)
                            else:
                                await client.submit_pin(pin)
                            # Try connecting again
                            connected = await client.connect()
                except Exception as bypass_error:
                    print(f"Bypass attempt failed: {bypass_error}")
                    connected = False
        except Exception as e:
            print(f"Connection error: {e}")
            connected = False

        if connected:
            print("Successfully connected to Quotex API")

            # Get available assets
            print("Fetching available assets...")
            try:
                # Try different method names that might exist in the API
                try:
                    print("Trying get_all_assets method...")
                    assets = await client.get_all_assets()
                except AttributeError:
                    try:
                        print("Trying get_all_asset method...")
                        assets = await client.get_all_asset()
                    except AttributeError:
                        try:
                            print("Trying get_assets method...")
                            assets = await client.get_assets()
                        except AttributeError:
                            print("Trying get_asset method...")
                            assets = await client.get_asset()

                print(f"Available assets: {assets}")
                print(f"Total assets: {len(assets) if assets else 0}")

                # Try to get more information about the first asset
                if assets and isinstance(assets, dict) and len(assets) > 0:
                    # Get the first asset key
                    first_asset_key = list(assets.keys())[0]
                    first_asset_value = assets[first_asset_key]
                    print(f"Getting details for asset: {first_asset_key} (ID: {first_asset_value})")

                    # Print some information about the assets
                    print("\nAsset Categories:")

                    # Group assets by type
                    forex = [k for k in assets.keys() if not k.endswith('_otc') and not k.startswith('XAU') and not k.startswith('XAG')]
                    otc = [k for k in assets.keys() if k.endswith('_otc')]
                    crypto = [k for k in assets.keys() if 'USD_otc' in k and any(c in k for c in ['BTC', 'ETH', 'LTC', 'XRP', 'BNB', 'SOL', 'TRX', 'TRU', 'DOG', 'SHI', 'BON', 'FLO', 'WIF', 'TON'])]
                    indices = [k for k in assets.keys() if any(idx in k for idx in ['EUR', 'USD', 'GBP', 'JPY', 'HKD']) and any(idx in k for idx in ['F40', 'NDX', 'STX', 'FTS', 'DJI', 'JPX', 'HSI', 'IBX', 'CHIA'])]
                    commodities = [k for k in assets.keys() if k.startswith('XAU') or k.startswith('XAG') or 'Crude' in k or 'Brent' in k]

                    print(f"Forex Pairs: {len(forex)}")
                    print(f"OTC Markets: {len(otc)}")
                    print(f"Cryptocurrencies: {len(crypto)}")
                    print(f"Stock Indices: {len(indices)}")
                    print(f"Commodities: {len(commodities)}")

                    # Print some example assets from each category
                    if forex:
                        print(f"\nExample Forex Pairs: {', '.join(forex[:5])}")
                    if otc:
                        print(f"Example OTC Markets: {', '.join(otc[:5])}")
                    if crypto:
                        print(f"Example Cryptocurrencies: {', '.join(crypto[:5])}")
                    if indices:
                        print(f"Example Indices: {', '.join(indices[:5])}")
                    if commodities:
                        print(f"Example Commodities: {', '.join(commodities[:5])}")

                    # Save the assets to a file for later use
                    try:
                        with open('quotex_assets.json', 'w') as f:
                            import json
                            json.dump(assets, f, indent=4)
                        print("\nSaved assets to quotex_assets.json")
                    except Exception as save_error:
                        print(f"Error saving assets to file: {save_error}")
            except Exception as asset_error:
                print(f"Error fetching assets: {asset_error}")
                import traceback
                traceback.print_exc()
        else:
            print("Failed to connect to Quotex API")
            print("Please check your credentials and internet connection")
    except Exception as e:
        print(f"Error connecting to Quotex API: {e}")
        import traceback
        traceback.print_exc()

# Run the main function
if __name__ == "__main__":
    # Create a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # Run the main function
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("Test interrupted by user")
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the event loop
        loop.close()
