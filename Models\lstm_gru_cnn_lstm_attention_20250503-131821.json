{"module": "keras.src.models.functional", "class_name": "Functional", "config": {"name": "functional_1", "trainable": true, "layers": [{"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_shape": [null, 30, 229], "dtype": "float32", "sparse": false, "ragged": false, "name": "input_layer_1"}, "registered_name": null, "name": "input_layer_1", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 64, "kernel_size": [1], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 229]}, "name": "conv1d_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 229], "dtype": "float32", "keras_history": ["input_layer_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 64, "kernel_size": [3], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 229]}, "name": "conv1d_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 229], "dtype": "float32", "keras_history": ["input_layer_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 64, "kernel_size": [5], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 229]}, "name": "conv1d_3", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 229], "dtype": "float32", "keras_history": ["input_layer_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Concatenate", "config": {"name": "concatenate", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 64], [null, 30, 64], [null, 30, 64]]}, "name": "concatenate", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["conv1d_1", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["conv1d_2", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["conv1d_3", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 192]}, "name": "batch_normalization_6", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 192], "dtype": "float32", "keras_history": ["concatenate", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_4", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 128, "kernel_size": [1], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 192]}, "name": "conv1d_4", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 192], "dtype": "float32", "keras_history": ["batch_normalization_6", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_7", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}, "name": "batch_normalization_7", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["conv1d_4", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Bidirectional", "config": {"name": "bidirectional_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "merge_mode": "concat", "layer": {"module": "keras.layers", "class_name": "LSTM", "config": {"name": "forward_lstm_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "return_sequences": true, "return_state": false, "go_backwards": false, "stateful": false, "unroll": false, "zero_output_for_mask": true, "units": 128, "activation": "tanh", "recurrent_activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "recurrent_initializer": {"module": "keras.initializers", "class_name": "Orthogonal", "config": {"seed": null, "gain": 1.0}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "unit_forget_bias": true, "kernel_regularizer": null, "recurrent_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "recurrent_constraint": null, "bias_constraint": null, "dropout": 0.0, "recurrent_dropout": 0.0, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}}, "backward_layer": {"module": "keras.layers", "class_name": "LSTM", "config": {"name": "backward_lstm_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "return_sequences": true, "return_state": false, "go_backwards": true, "stateful": false, "unroll": false, "zero_output_for_mask": true, "units": 128, "activation": "tanh", "recurrent_activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "recurrent_initializer": {"module": "keras.initializers", "class_name": "Orthogonal", "config": {"seed": null, "gain": 1.0}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "unit_forget_bias": true, "kernel_regularizer": null, "recurrent_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "recurrent_constraint": null, "bias_constraint": null, "dropout": 0.0, "recurrent_dropout": 0.0, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}}}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}, "name": "bidirectional_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["batch_normalization_7", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_8", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}, "name": "batch_normalization_8", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["bidirectional_2", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_5", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.3, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_5", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["batch_normalization_8", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "MultiHeadAttention", "config": {"name": "multi_head_attention", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "num_heads": 8, "key_dim": 32, "value_dim": 32, "dropout": 0.0, "use_bias": true, "output_shape": null, "attention_axes": [1], "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "seed": null}, "registered_name": null, "build_config": {"shapes_dict": {"query_shape": [null, 30, 256], "value_shape": [null, 30, 256]}}, "name": "multi_head_attention", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["dropout_5", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["dropout_5", 0, 0]}}], "kwargs": {"query_mask": null, "value_mask": null}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_9", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}, "name": "batch_normalization_9", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["multi_head_attention", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 256], [null, 30, 256]]}, "name": "add_1", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["dropout_5", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["batch_normalization_9", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "LayerNormalization", "config": {"name": "layer_normalization", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": [-1], "epsilon": 1e-06, "center": true, "scale": true, "rms_scaling": false, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}, "name": "layer_normalization", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["add_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Bidirectional", "config": {"name": "bidirectional_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "merge_mode": "concat", "layer": {"module": "keras.layers", "class_name": "LSTM", "config": {"name": "forward_lstm_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "return_sequences": true, "return_state": false, "go_backwards": false, "stateful": false, "unroll": false, "zero_output_for_mask": true, "units": 64, "activation": "tanh", "recurrent_activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "recurrent_initializer": {"module": "keras.initializers", "class_name": "Orthogonal", "config": {"seed": null, "gain": 1.0}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "unit_forget_bias": true, "kernel_regularizer": null, "recurrent_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "recurrent_constraint": null, "bias_constraint": null, "dropout": 0.0, "recurrent_dropout": 0.0, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}}, "backward_layer": {"module": "keras.layers", "class_name": "LSTM", "config": {"name": "backward_lstm_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "return_sequences": true, "return_state": false, "go_backwards": true, "stateful": false, "unroll": false, "zero_output_for_mask": true, "units": 64, "activation": "tanh", "recurrent_activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "recurrent_initializer": {"module": "keras.initializers", "class_name": "Orthogonal", "config": {"seed": null, "gain": 1.0}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "unit_forget_bias": true, "kernel_regularizer": null, "recurrent_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "recurrent_constraint": null, "bias_constraint": null, "dropout": 0.0, "recurrent_dropout": 0.0, "seed": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}}}, "registered_name": null, "build_config": {"input_shape": [null, 30, 256]}, "name": "bidirectional_3", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 256], "dtype": "float32", "keras_history": ["layer_normalization", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_10", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}, "name": "batch_normalization_10", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["bidirectional_3", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_7", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.3, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_7", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["batch_normalization_10", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "GlobalAveragePooling1D", "config": {"name": "global_average_pooling1d", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_average_pooling1d", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["dropout_7", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "GlobalMaxPooling1D", "config": {"name": "global_max_pooling1d", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_max_pooling1d", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["dropout_7", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Concatenate", "config": {"name": "concatenate_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1}, "registered_name": null, "build_config": {"input_shape": [[null, 128], [null, 128]]}, "name": "concatenate_1", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["global_average_pooling1d", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["global_max_pooling1d", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_4", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": {"module": "keras.regularizers", "class_name": "L1L2", "config": {"l1": 1e-05, "l2": 0.0001}, "registered_name": null}, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 256]}, "name": "dense_4", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 256], "dtype": "float32", "keras_history": ["concatenate_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_11", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "batch_normalization_11", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["dense_4", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_8", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.4, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_8", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["batch_normalization_11", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_5", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": {"module": "keras.regularizers", "class_name": "L1L2", "config": {"l1": 1e-05, "l2": 0.0001}, "registered_name": null}, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dense_5", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["dropout_8", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_12", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "batch_normalization_12", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dense_5", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dense_6", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["dropout_8", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_9", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.4, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_9", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["batch_normalization_12", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 64], [null, 64]]}, "name": "add_2", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dense_6", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dropout_9", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.src.activations.activations", "class_name": "ReLU", "config": {"name": "re_lu_1", "negative_slope": 0.0, "max_value": null, "threshold": 0.0}, "registered_name": "ReLU", "name": "re_lu_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["add_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_7", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 1, "activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "dense_7", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["re_lu_1", 0, 0]}}], "kwargs": {}}]}], "input_layers": [["input_layer_1", 0, 0]], "output_layers": [["dense_7", 0, 0]]}, "registered_name": "Functional", "build_config": {"input_shape": null}, "compile_config": {"loss": "binary_crossentropy", "loss_weights": null, "metrics": ["accuracy", {"module": "keras.metrics", "class_name": "AUC", "config": {"name": "auc_1", "dtype": "float32", "num_thresholds": 200, "curve": "ROC", "summation_method": "interpolation", "multi_label": false, "num_labels": null, "label_weights": null, "from_logits": false}, "registered_name": null}, {"module": "keras.metrics", "class_name": "Precision", "config": {"name": "precision_1", "dtype": "float32", "thresholds": null, "top_k": null, "class_id": null}, "registered_name": null}, {"module": "keras.metrics", "class_name": "Recall", "config": {"name": "recall_1", "dtype": "float32", "thresholds": null, "top_k": null, "class_id": null}, "registered_name": null}], "weighted_metrics": null, "run_eagerly": false, "steps_per_execution": 1, "jit_compile": false}}