import os
import tensorflow as tf
from datetime import datetime

print("Creating a simple DQN model for testing...")

# Create a simple DQN model
def create_simple_dqn_model():
    # Market features input
    market_input = tf.keras.layers.Input(shape=(30, 10), name='market_features')
    
    # Portfolio state input
    portfolio_input = tf.keras.layers.Input(shape=(3,), name='portfolio_state')
    
    # Process market features
    x = tf.keras.layers.Conv1D(32, 3, activation='relu')(market_input)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.MaxPooling1D(2)(x)
    x = tf.keras.layers.LSTM(64, return_sequences=True)(x)
    x = tf.keras.layers.GlobalAveragePooling1D()(x)
    
    # Process portfolio state
    y = tf.keras.layers.Dense(16, activation='relu')(portfolio_input)
    
    # Combine features
    combined = tf.keras.layers.Concatenate()([x, y])
    
    # Output layers
    dense = tf.keras.layers.Dense(64, activation='relu')(combined)
    output = tf.keras.layers.Dense(2, activation='linear')(dense)  # Q-values for actions
    
    # Create model
    model = tf.keras.Model(inputs=[market_input, portfolio_input], outputs=output)
    model.compile(optimizer='adam', loss='mse')
    
    return model

# Create model directory if it doesn't exist
os.makedirs('models', exist_ok=True)

# Create and save the model
model = create_simple_dqn_model()
timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
model_path = os.path.join('models', f'dqn_model_{timestamp}.h5')
model.save(model_path)

print(f"DQN model saved to {model_path}")

# Create a features file
features = [
    'close', 'returns', 'sma_5', 'sma_20', 'rsi_14', 'macd',
    'stoch_k_14', 'adx_14', 'cci_20', 'atr_14'
]

features_path = os.path.join('models', f'dqn_model_{timestamp}_features.txt')
with open(features_path, 'w') as f:
    for feature in features:
        f.write(f"{feature}\n")

print(f"DQN features saved to {features_path}")
print("Done!")
