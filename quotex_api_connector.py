#!/usr/bin/env python
"""
Quotex API Connector
Provides a simplified interface for connecting to the Quotex API with PIN verification
"""

import os
import sys
import time
import json
import asyncio
import threading
from PyQt5 import QtCore, QtWidgets

class QuotexAPIConnector(QtCore.QObject):
    """Connector for Quotex API with PIN verification support"""

    # Define signals for connection events
    connection_started = QtCore.pyqtSignal()
    connection_success = QtCore.pyqtSignal(object)  # Pass the API client
    connection_failed = QtCore.pyqtSignal(str)  # Pass error message
    pin_required = QtCore.pyqtSignal()
    pin_sent = QtCore.pyqtSignal(bool, str)  # Success, message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.email = ""
        self.password = ""
        self.api_client = None
        self.api_connected = False
        self.pin_dialog = None
        self.read_credentials_from_config()

    def read_credentials_from_config(self):
        """Read credentials from config.ini file"""
        try:
            # List of possible config file locations
            config_files = [
                'config.ini',
                'settings/config.ini',
                'settings\\config.ini',
                'settings.ini'
            ]

            # Try to read directly from the files first
            for config_file in config_files:
                if os.path.exists(config_file):
                    print(f"Found config file: {config_file}")

                    # Try direct file reading
                    try:
                        with open(config_file, 'r') as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.strip().startswith('email='):
                                    self.email = line.strip().replace('email=', '')
                                elif line.strip().startswith('password='):
                                    self.password = line.strip().replace('password=', '')

                            if self.email and self.password:
                                print(f"Loaded credentials from {config_file} for: {self.email}")
                                return True
                    except Exception as file_error:
                        print(f"Error reading {config_file} directly: {file_error}")

            # If no config file found, try environment variables
            if not self.email or not self.password:
                self.email = os.environ.get('QUOTEX_EMAIL', '')
                self.password = os.environ.get('QUOTEX_PASSWORD', '')
                if self.email and self.password:
                    print(f"Loaded credentials from environment variables for: {self.email}")
                    return True

            return False
        except Exception as e:
            print(f"Error reading credentials from config: {e}")
            return False

    def connect_to_api(self, parent_widget=None):
        """Connect to Quotex API with PIN verification support"""
        # Check if we have credentials
        if not self.email or not self.password:
            if parent_widget:
                reply = QtWidgets.QMessageBox.question(
                    parent_widget,
                    "No API Credentials",
                    "No Quotex API credentials are set. Would you like to enter them now?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.Yes
                )

                if reply == QtWidgets.QMessageBox.Yes:
                    self.show_credentials_dialog(parent_widget)
                    # If credentials were set, try again
                    if self.email and self.password:
                        # Wait a moment and try again
                        QtCore.QTimer.singleShot(500, lambda: self.connect_to_api(parent_widget))
                    return False
            else:
                print("No credentials available for connection")
                return False

        # Emit signal that connection has started
        self.connection_started.emit()

        # Create and show PIN dialog
        if parent_widget:
            self.show_pin_dialog(parent_widget)

        # Start connection in a separate thread
        thread = threading.Thread(target=self._connect_thread)
        thread.daemon = True
        thread.start()

        return True

    def connect_to_api(self, parent_widget=None):
        """Connect to Quotex API with PIN verification support"""
        # Check if we have credentials
        if not self.email or not self.password:
            if parent_widget:
                reply = QtWidgets.QMessageBox.question(
                    parent_widget,
                    "No API Credentials",
                    "No Quotex API credentials are set. Would you like to enter them now?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.Yes
                )

                if reply == QtWidgets.QMessageBox.Yes:
                    self.show_credentials_dialog(parent_widget)
                    # If credentials were set, try again
                    if self.email and self.password:
                        # Wait a moment and try again
                        QtCore.QTimer.singleShot(500, lambda: self.connect_to_api(parent_widget))
                    return False
            else:
                print("No credentials available for connection")
                return False

        # Emit signal that connection has started
        self.connection_started.emit()

        # Create and show browser dialog
        if parent_widget:
            self.show_browser_dialog(parent_widget)

        return True

    def show_browser_dialog(self, parent_widget):
        """Show browser dialog for Quotex login"""
        # Create dialog
        dialog = QtWidgets.QDialog(parent_widget)
        dialog.setWindowTitle("Quotex Login")
        dialog.setMinimumWidth(800)
        dialog.setMinimumHeight(600)

        # Create layout
        layout = QtWidgets.QVBoxLayout(dialog)

        # Add header
        header_label = QtWidgets.QLabel("Quotex Login")
        header_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        header_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(header_label)

        # Add instructions
        instructions = QtWidgets.QLabel(
            "Please log in to your Quotex account using the browser below.\n\n"
            "1. Enter your email and password\n"
            "2. If PIN verification is required, enter the PIN from your email\n"
            "3. Once logged in, the API client will be initialized automatically"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add status label
        self.status_label = QtWidgets.QLabel("Loading Quotex login page...")
        self.status_label.setStyleSheet("color: blue;")
        layout.addWidget(self.status_label)

        # Add browser
        self.browser = QtWebEngineWidgets.QWebEngineView()
        self.browser.setUrl(QtCore.QUrl("https://qxbroker.com/en/sign-in"))
        layout.addWidget(self.browser)

        # Add buttons
        button_layout = QtWidgets.QHBoxLayout()

        # Cancel button
        cancel_button = QtWidgets.QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # Connect signals
        self.browser.loadFinished.connect(self._on_page_loaded)

        # Show dialog
        dialog.exec_()

    def _on_page_loaded(self, success):
        """Handle page loaded event"""
        if not success:
            self.status_label.setText("Failed to load page")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            return

        # Get current URL
        current_url = self.browser.url().toString()
        print(f"Page loaded: {current_url}")

        # Check if we're on the login page
        if "sign-in" in current_url:
            self.status_label.setText("Please log in to your Quotex account")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # Auto-fill credentials
            self.browser.page().runJavaScript(f"""
                document.querySelector('input[name="email"]').value = '{self.email}';
                document.querySelector('input[name="password"]').value = '{self.password}';
            """)

        # Check if we're on the trade page (login successful)
        elif "trade" in current_url:
            self.status_label.setText("Login successful")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

            # Extract session data
            self.browser.page().runJavaScript("""
                if (window.settings && window.settings.token) {
                    return {
                        token: window.settings.token,
                        user_id: window.settings.user_id,
                        email: window.settings.email
                    };
                } else {
                    return null;
                }
            """, self._on_session_data_extracted)

    def _on_session_data_extracted(self, data):
        """Handle session data extraction"""
        if data:
            print(f"Session data extracted: {data}")

            # Create API client
            try:
                from quotexapi.stable_api import Quotex

                # Create client with session data
                self.api_client = Quotex(self.email, self.password)
                self.api_client.token = data.get('token')
                self.api_connected = True

                # Emit success signal
                self.connection_success.emit(self.api_client)

                # Close the browser dialog
                if self.browser:
                    self.browser.close()
            except Exception as e:
                print(f"Error creating API client: {e}")
                self.connection_failed.emit(f"Error creating API client: {e}")
        else:
            print("Failed to extract session data")
            self.connection_failed.emit("Failed to extract session data")

    def show_credentials_dialog(self, parent_widget):
        """Show dialog to set API credentials"""
        dialog = QtWidgets.QDialog(parent_widget)
        dialog.setWindowTitle("Set Quotex API Credentials")
        dialog.setMinimumWidth(400)

        layout = QtWidgets.QVBoxLayout(dialog)

        # Add instructions
        instructions = QtWidgets.QLabel(
            "Enter your Quotex API credentials below. "
            "These will be used to connect to the Quotex API."
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add form layout
        form_layout = QtWidgets.QFormLayout()

        # Add email field
        email_label = QtWidgets.QLabel("Email:")
        email_input = QtWidgets.QLineEdit()
        email_input.setText(self.email)
        form_layout.addRow(email_label, email_input)

        # Add password field
        password_label = QtWidgets.QLabel("Password:")
        password_input = QtWidgets.QLineEdit()
        password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        password_input.setText(self.password)
        form_layout.addRow(password_label, password_input)

        layout.addLayout(form_layout)

        # Add buttons
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            # Save credentials
            self.email = email_input.text()
            self.password = password_input.text()

            # Update environment variables
            os.environ['QUOTEX_EMAIL'] = self.email
            os.environ['QUOTEX_PASSWORD'] = self.password

            print("API credentials updated")

            # Save to config.ini for future use
            try:
                with open('config.ini', 'w') as f:
                    f.write(f"email={self.email}\n")
                    f.write(f"password={self.password}\n")
                print("Credentials saved to config.ini")
                return True
            except Exception as e:
                print(f"Error saving credentials to config.ini: {e}")
                return False

        return False

    # All PIN-related methods have been replaced by the browser-based approach