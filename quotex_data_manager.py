#!/usr/bin/env python
import os
import sys
import time
import csv
import asyncio
import argparse
import random
import configparser
from datetime import datetime, timedelta
from quotexapi.stable_api import Quotex
from quotexapi.utils.processor import process_candles, get_color
from quotexapi.expiration import get_timestamp_days_ago, timestamp_to_date

# ASCII art banner
try:
    import pyfiglet
    custom_font = pyfiglet.Figlet(font="slant")
    ascii_art = custom_font.renderText("Quotex Data Manager")
except ImportError:
    ascii_art = """
    +-----------------------------------------+
    |        QUOTEX DATA MANAGER              |
    +-----------------------------------------+
    """

# Load credentials from config.ini
try:
    # Use raw ConfigParser to avoid interpolation issues with % in passwords
    config = configparser.RawConfigParser()
    config_path = os.path.join('settings', 'config.ini')
    if os.path.exists(config_path):
        config.read(config_path)
        email = config.get('settings', 'email', fallback=None)
        password = config.get('settings', 'password', fallback=None)
    else:
        email = None
        password = None
except Exception as e:
    print(f"Error loading credentials: {e}")
    email = "<EMAIL>"
    password = "+DMcYZ-z83cT8w%"

client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

# Global settings
DEFAULT_ASSET = "USDBRL"  # Changed to USDBRL as requested
DEFAULT_PERIOD = 60  # in seconds
DEFAULT_DURATION = 3600  # in seconds (1 hour)
DEFAULT_DAYS = 7  # for historical data
DEFAULT_AMOUNT = 10  # Default amount for trading operations

def save_candles_to_csv(candles, filename):
    """Save candles data to CSV file"""
    # Check if file exists to determine if we need to write headers
    file_exists = os.path.isfile(filename)

    # Define fieldnames for CSV
    fieldnames = ['timestamp', 'time', 'open', 'high', 'low', 'close', 'color', 'ticks']

    # Open file in append mode
    with open(filename, 'a', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Write header if file doesn't exist
        if not file_exists:
            writer.writeheader()

        # Write candle data
        for candle in candles:
            # Add color and timestamp if not present
            if 'color' not in candle:
                candle['color'] = get_color(candle)
            if 'timestamp' not in candle:
                candle['timestamp'] = datetime.fromtimestamp(candle['time']).strftime('%Y-%m-%d %H:%M:%S')

            # Write row
            writer.writerow(candle)

    return len(candles)

async def select_account():
    """Select between demo and real accounts"""
    while True:
        account_type = input("Select account type (demo or real): ").lower()

        if account_type in ["demo", "real"]:
            if account_type == "real":
                print("Real account selected")
                client.set_account_mode("REAL")
            else:
                print("Demo account selected")
                client.set_account_mode("PRACTICE")  # Default is PRACTICE/DEMO

            return account_type
        else:
            print("Invalid selection. Please enter 'demo' or 'real'.")

async def get_historical_candles(asset, period, days, output_file):
    """Retrieve historical candle data for the specified number of days"""
    print(f"\nRetrieving {days} days of historical candle data for {asset}...")

    # Calculate timestamps
    timestamp = get_timestamp_days_ago(days)
    end_time = time.time()

    # Calculate how many hours we need to fetch
    hours_to_fetch = days * 24
    candles_per_request = 60  # Typical number of candles per request

    # Store all candles
    all_candles = []

    # Start time for the first request
    start_from_time = timestamp

    print(f"Fetching data from {timestamp_to_date(timestamp)} to {timestamp_to_date(end_time)}")
    print(f"This will require approximately {hours_to_fetch} requests. Please be patient...")
    print("-" * 60)

    # Progress tracking
    total_requests = hours_to_fetch
    completed_requests = 0

    try:
        # Connect to API
        check_connect, message = await client.connect()
        if not check_connect:
            print(f"Connection failed: {message}")
            return []

        # Loop through each hour
        for i in range(hours_to_fetch):
            # Calculate offset to get one hour of data
            offset = 3600  # 1 hour in seconds

            # Get candles for this hour
            candles = await client.get_candles(asset, start_from_time, offset, period)

            if candles and len(candles) > 0:
                # Process candles if needed
                if not candles[0].get("open"):
                    candles = process_candles(candles, period)

                # Add color to candles
                for candle in candles:
                    candle['color'] = get_color(candle)

                # Add to our collection
                all_candles.extend(candles)

                # Save batch to CSV
                save_candles_to_csv(candles, output_file)

            # Move to next hour
            start_from_time += offset

            # Update progress
            completed_requests += 1
            progress = (completed_requests / total_requests) * 100
            print(f"\rProgress: {progress:.1f}% ({completed_requests}/{total_requests} hours) - {len(all_candles)} candles retrieved", end="")

            # Small delay to avoid overwhelming the API
            await asyncio.sleep(0.5)

    except Exception as e:
        print(f"\nError retrieving historical data: {e}")

    print(f"\n\nHistorical data retrieval complete. {len(all_candles)} candles retrieved.")
    print(f"Data saved to: {output_file}")

    return all_candles

async def capture_live_candles(asset, period, duration, continuous, output_file):
    """Capture live candle data and save to CSV"""
    print(f"\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully.")

        # Get asset information
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        print(f"Asset: {asset_name} ({asset_data})")

        if not asset_data[2]:  # Check if asset is open
            print("WARNING: Asset appears to be closed. Data may not be available.")

        print(f"\nStarting to capture live candles for {asset_name}...")
        print(f"Period: {period} seconds")
        if continuous:
            print("Running continuously until stopped (Ctrl+C to stop)")
        else:
            print(f"Duration: {duration} seconds")
        print(f"Saving to: {output_file}")
        print("-" * 60)

        # Store candles history
        candles_history = []

        # Start time for monitoring duration
        start_time = time.time()
        last_candle_time = 0
        last_minute = -1  # Track the last minute we captured

        # Start monitoring
        try:
            # Run until duration expires or indefinitely if continuous mode is enabled
            while continuous or (time.time() - start_time) < duration:
                # Get current time
                current_time = time.time()
                current_datetime = datetime.now()
                current_minute = current_datetime.minute

                # Calculate time remaining if not in continuous mode
                if not continuous:
                    time_remaining = duration - (current_time - start_time)

                # Check if we're in a new minute
                if current_minute != last_minute:
                    # Get the latest candles
                    candles = await client.get_candles(asset, current_time, 300, period)

                    if len(candles) > 0:
                        # Process candles if needed
                        if not candles[0].get("open"):
                            candles = process_candles(candles, period)

                        # Add to history if it's a new candle
                        if not candles_history or candles[-1]['time'] != last_candle_time:
                            # Add color to candle
                            candles[-1]['color'] = get_color(candles[-1])

                            # Add to history
                            candles_history.append(candles[-1])
                            last_candle_time = candles[-1]['time']
                            last_minute = current_minute  # Update last minute captured

                            # Save to CSV
                            saved_count = save_candles_to_csv([candles[-1]], output_file)

                            # Print information with clear minute marker
                            candle_time = datetime.fromtimestamp(candles[-1]['time'])
                            print(f"\n[{current_datetime.strftime('%H:%M:%S')}] New 1-minute candle captured")
                            print(f"Candle time: {candle_time}")
                            print(f"Open: {candles[-1]['open']}, Close: {candles[-1]['close']}, High: {candles[-1]['high']}, Low: {candles[-1]['low']}")
                            print(f"Color: {candles[-1]['color']}")
                            print(f"Saved to {output_file} (Total candles: {len(candles_history)})")

                            if not continuous:
                                print(f"Time remaining: {time_remaining:.0f} seconds")
                            else:
                                print("Running continuously (Ctrl+C to stop)")

                # Wait before checking again (1 second)
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            print("\nCapture interrupted by user.")

        # Final statistics
        print("\n" + "=" * 60)
        print("CAPTURE SESSION SUMMARY")
        print("=" * 60)
        print(f"Total candles captured: {len(candles_history)}")
        print(f"Data saved to: {output_file}")
        print(f"Capture duration: {time.time() - start_time:.2f} seconds")

        # Print the first and last candle timestamps to show the range
        if len(candles_history) > 0:
            first_candle = datetime.fromtimestamp(candles_history[0]['time'])
            last_candle = datetime.fromtimestamp(candles_history[-1]['time'])
            print(f"First candle: {first_candle}")
            print(f"Last candle: {last_candle}")
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def combined_historical_and_live(asset, period, days, duration, continuous, output_file):
    """Retrieve historical data and then capture live candles"""
    print(f"\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully.")

        # Get asset information
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        print(f"Asset: {asset_name} ({asset_data})")

        if not asset_data[2]:  # Check if asset is open
            print("WARNING: Asset appears to be closed. Data may not be available.")

        # Retrieve historical data
        print(f"\nRetrieving {days} days of historical data before starting live capture...")
        historical_candles = await get_historical_candles(asset, period, days, output_file)
        print(f"Historical data retrieval complete. {len(historical_candles)} candles retrieved.")

        # Start capturing live candles
        await capture_live_candles(asset, period, duration, continuous, output_file)
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def list_available_assets():
    """List all available assets"""
    print(f"\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully. Retrieving assets...")

        # Get all assets
        all_data = client.get_payment()

        # Get all asset codes
        codes_asset = await client.get_all_assets()

        # Create a mapping from display name to code
        asset_codes = {}
        for code, name in codes_asset.items():
            asset_codes[name] = code

        # Print all assets
        print("\nAll available assets:")
        print("-" * 60)
        print(f"{'Asset Name':<30} {'Asset Code':<20} {'Status':<10} {'Profit 1M':<10} {'Profit 5M':<10}")
        print("-" * 60)

        for asset_name in sorted(all_data.keys()):
            asset_data = all_data[asset_name]
            code = asset_codes.get(asset_name, "Unknown")
            status = "Open" if asset_data["open"] else "Closed"
            profit_1m = asset_data["profit"]["1M"]
            profit_5m = asset_data["profit"]["5M"]

            print(f"{asset_name:<30} {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def analyze_csv_file(file_path):
    """Analyze a CSV file containing candle data"""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return

    try:
        # Read the CSV file
        candles = []
        with open(file_path, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                candles.append(row)

        if not candles:
            print("No candles found in the file.")
            return

        # Convert numeric fields
        for candle in candles:
            for field in ['open', 'high', 'low', 'close', 'time']:
                if field in candle:
                    try:
                        candle[field] = float(candle[field])
                    except ValueError:
                        pass

        # Basic statistics
        print("\nCandle Data Analysis")
        print("-" * 60)
        print(f"Total candles: {len(candles)}")

        # Time range
        first_candle_time = datetime.fromtimestamp(float(candles[0]['time']))
        last_candle_time = datetime.fromtimestamp(float(candles[-1]['time']))
        print(f"Time range: {first_candle_time} to {last_candle_time}")

        # Price range
        min_price = min(float(candle['low']) for candle in candles)
        max_price = max(float(candle['high']) for candle in candles)
        print(f"Price range: {min_price} to {max_price}")

        # Color distribution
        green_candles = sum(1 for candle in candles if candle['color'].lower() == 'green')
        red_candles = sum(1 for candle in candles if candle['color'].lower() == 'red')
        gray_candles = len(candles) - green_candles - red_candles

        print(f"Green candles: {green_candles} ({green_candles/len(candles)*100:.1f}%)")
        print(f"Red candles: {red_candles} ({red_candles/len(candles)*100:.1f}%)")
        print(f"Gray candles: {gray_candles} ({gray_candles/len(candles)*100:.1f}%)")

        # Average candle size
        avg_candle_size = sum(abs(float(candle['close']) - float(candle['open'])) for candle in candles) / len(candles)
        print(f"Average candle size: {avg_candle_size:.6f}")

        # Largest candles
        candles_by_size = sorted(candles, key=lambda x: abs(float(x['close']) - float(x['open'])), reverse=True)
        print("\nLargest candles:")
        for i, candle in enumerate(candles_by_size[:5]):
            candle_time = datetime.fromtimestamp(float(candle['time']))
            candle_size = abs(float(candle['close']) - float(candle['open']))
            print(f"{i+1}. {candle_time}: Size={candle_size:.6f}, Open={candle['open']}, Close={candle['close']}, Color={candle['color']}")

    except Exception as e:
        print(f"Error analyzing file: {e}")

# Server Connection Functions
async def connect(attempts=5):
    """Attempts to connect to the Quotex server with multiple retries"""
    print("\nConnecting to Quotex server...")

    for attempt in range(1, attempts + 1):
        print(f"Connection attempt {attempt}/{attempts}...")
        check_connect, message = await client.connect()

        if check_connect:
            print(f"Connection successful: {message}")
            return True, message
        else:
            print(f"Connection failed: {message}")

            if attempt < attempts:
                print("Retrying in 3 seconds...")
                await asyncio.sleep(3)
            else:
                print("Maximum connection attempts reached.")

    return False, "Failed to connect after multiple attempts"

async def test_connection():
    """Test if the connection to the server is working"""
    print("\nTesting connection to Quotex server...")

    if await client.check_connect():
        print("✅ Connection is active and working properly.")
        return True
    else:
        print("❌ Connection is not active. Please reconnect.")
        return False

# Balance Management Functions
async def get_balance():
    """Get the current account balance"""
    try:
        balance = await client.get_balance()
        account_type = "Demo" if client.account_is_demo else "Real"
        print(f"\n💰 Current {account_type} Balance: {balance}")
        return balance
    except Exception as e:
        print(f"Error getting balance: {e}")
        return None

async def balance_refill():
    """Refill the demo account balance"""
    if client.account_is_demo:
        try:
            result = await client.edit_practice_balance(10000)  # Default refill amount
            print(f"\n💰 Demo balance refilled: {result}")
            return result
        except Exception as e:
            print(f"Error refilling balance: {e}")
            return None
    else:
        print("⚠️ Balance refill is only available for demo accounts.")
        return None

# Purchase Operations
async def buy_simple(asset=None, direction=None, duration=None, amount=None):
    """Execute a simple purchase operation"""
    # Get parameters if not provided
    if asset is None:
        asset = input(f"Enter asset code (default: {DEFAULT_ASSET}): ") or DEFAULT_ASSET

    if direction is None:
        direction_input = input("Enter direction (call/put, default: random): ").lower()
        if direction_input in ["call", "put"]:
            direction = direction_input
        else:
            direction = random.choice(["call", "put"])
            print(f"Random direction selected: {direction}")

    if duration is None:
        duration = int(input(f"Enter duration in seconds (default: 60): ") or 60)

    if amount is None:
        amount = float(input(f"Enter amount to trade (default: {DEFAULT_AMOUNT}): ") or DEFAULT_AMOUNT)

    print(f"\n🛒 Executing {direction.upper()} operation on {asset}")
    print(f"Amount: {amount}, Duration: {duration} seconds")

    try:
        # Check if asset is open
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        if not asset_data[2]:  # Check if asset is open
            print(f"⚠️ Warning: Asset {asset} appears to be closed. Operation may fail.")

        # Execute the buy operation
        status, buy_info = await client.buy(amount, asset, direction, duration)

        if status:
            print(f"✅ Operation executed successfully!")
            print(f"Operation ID: {buy_info}")
            return buy_info
        else:
            print(f"❌ Operation failed: {buy_info}")
            return None
    except Exception as e:
        print(f"Error executing operation: {e}")
        return None

async def buy_and_check_win(asset=None, direction=None, duration=None, amount=None):
    """Execute a purchase and wait for the result"""
    operation_id = await buy_simple(asset, direction, duration, amount)

    if operation_id:
        print(f"\nWaiting for operation result (ID: {operation_id})...")

        # Wait for the duration plus a small buffer
        wait_time = duration + 5 if duration else 65
        print(f"Will check result in {wait_time} seconds...")

        # Wait for the operation to complete
        await asyncio.sleep(wait_time)

        try:
            # Get history to check the result
            history = await client.get_history()

            # Find our operation in the history
            for operation in history:
                if str(operation.get('id')) == str(operation_id):
                    profit = operation.get('profit')
                    win = profit > 0

                    if win:
                        print(f"🎉 Operation WON! Profit: {profit}")
                    else:
                        print(f"😞 Operation LOST. Profit: {profit}")

                    return win, profit

            print("⚠️ Operation not found in history. It may still be processing.")
            return None, None
        except Exception as e:
            print(f"Error checking operation result: {e}")
            return None, None
    else:
        return None, None

async def buy_multiple(orders=10, assets=None):
    """Execute multiple purchase operations"""
    if assets is None:
        # Default assets to use
        assets = [DEFAULT_ASSET, "EURUSD", "USDJPY", "GBPUSD"]

    print(f"\n🔄 Executing {orders} random operations...")

    results = []
    for i in range(orders):
        # Randomly select parameters
        asset = random.choice(assets)
        direction = random.choice(["call", "put"])
        duration = random.choice([60, 120, 180, 300])  # 1, 2, 3, or 5 minutes
        amount = DEFAULT_AMOUNT

        print(f"\nOperation {i+1}/{orders}:")
        operation_id = await buy_simple(asset, direction, duration, amount)

        if operation_id:
            results.append({
                "id": operation_id,
                "asset": asset,
                "direction": direction,
                "duration": duration,
                "amount": amount
            })

        # Small delay between operations
        await asyncio.sleep(1)

    print(f"\n✅ Completed {len(results)}/{orders} operations")
    return results

# Profile and Asset Data Functions
async def get_profile():
    """Get user profile information"""
    try:
        profile = await client.get_profile()

        print("\n👤 USER PROFILE")
        print("-" * 60)
        print(f"Username: {profile.nick_name}")
        print(f"User ID: {profile.profile_id}")
        print(f"Demo Balance: {profile.demo_balance}")
        print(f"Real Balance: {profile.live_balance}")
        print(f"Country: {profile.country_name}")
        print(f"Currency: {profile.currency}")

        return profile
    except Exception as e:
        print(f"Error getting profile: {e}")
        return None

async def assets_open():
    """List all available assets and check which ones are open"""
    try:
        # Get all assets
        all_data = client.get_payment()

        # Get all asset codes
        codes_asset = await client.get_all_assets()

        # Create a mapping from display name to code
        asset_codes = {}
        for code, name in codes_asset.items():
            asset_codes[name] = code

        # Print all assets
        print("\n📊 AVAILABLE ASSETS")
        print("-" * 80)
        print(f"{'Asset Name':<30} {'Asset Code':<20} {'Status':<10} {'Profit %':<10}")
        print("-" * 80)

        # Count open assets
        open_count = 0
        closed_count = 0

        for asset_name in sorted(all_data.keys()):
            asset_data = all_data[asset_name]
            code = asset_codes.get(asset_name, "Unknown")
            status = "🟢 Open" if asset_data["open"] else "🔴 Closed"
            profit = asset_data["profit"]["1M"]

            if asset_data["open"]:
                open_count += 1
            else:
                closed_count += 1

            print(f"{asset_name:<30} {code:<20} {status:<10} {profit:<10}")

        print("-" * 80)
        print(f"Total Assets: {len(all_data)} (Open: {open_count}, Closed: {closed_count})")

        return all_data
    except Exception as e:
        print(f"Error listing assets: {e}")
        return None

async def get_payout(asset=None):
    """Check the payout percentage of a specific asset"""
    if asset is None:
        asset = input(f"Enter asset code (default: {DEFAULT_ASSET}): ") or DEFAULT_ASSET

    try:
        # Get all assets data
        all_data = client.get_payment()

        # Get asset information
        asset_name, asset_data = await client.get_available_asset(asset)

        if asset_name in all_data:
            asset_info = all_data[asset_name]

            print(f"\n💹 PAYOUT INFORMATION FOR {asset_name}")
            print("-" * 60)
            print(f"Status: {'Open' if asset_info['open'] else 'Closed'}")
            print(f"Payout for 1 minute: {asset_info['profit']['1M']}%")
            print(f"Payout for 5 minutes: {asset_info['profit']['5M']}%")

            return asset_info
        else:
            print(f"Asset {asset} not found")
            return None
    except Exception as e:
        print(f"Error getting payout: {e}")
        return None

# Option Selling Functions
async def sell_option(option_id=None):
    """Sell an option before expiration"""
    if option_id is None:
        option_id = input("Enter the operation ID to sell: ")

    try:
        result = await client.sell_option([option_id])

        if result and result.get('sold'):
            print(f"\n✅ Option {option_id} sold successfully")
            print(f"Profit: {result.get('profit')}")
            return result
        else:
            print(f"\n❌ Failed to sell option {option_id}")
            if result:
                print(f"Reason: {result}")
            return None
    except Exception as e:
        print(f"Error selling option: {e}")
        return None

# Utility Functions
def asset_parse(asset):
    """Format asset name for display"""
    is_otc = "_otc" in asset.lower()
    base_asset = asset.replace("_OTC", "").replace("_otc", "")

    if is_otc:
        return f"{base_asset} 🔵 (OTC)"
    else:
        return base_asset

async def get_all_options():
    """List all available options in this script"""
    print("\n🔍 ALL AVAILABLE FEATURES")
    print("=" * 80)

    # Server Connection
    print("\n🌐 SERVER CONNECTION")
    print("-" * 60)
    print("1. connect(attempts=5)")
    print("   Attempts to connect to the Quotex server with multiple retries.")
    print("2. test_connection()")
    print("   Verifies if the connection to the server was successful.")

    # Balance Management
    print("\n💰 BALANCE MANAGEMENT")
    print("-" * 60)
    print("3. get_balance()")
    print("   Retrieves the current account balance (demo or real).")
    print("4. balance_refill()")
    print("   Refills the demo account balance to a default value.")

    # Purchase Operations
    print("\n🛒 PURCHASE OPERATIONS")
    print("-" * 60)
    print("5. buy_simple()")
    print("   Executes a simple purchase operation with specified parameters.")
    print("6. buy_and_check_win()")
    print("   Performs a purchase and waits for the result to check if profitable.")
    print("7. buy_multiple(orders=10)")
    print("   Executes multiple purchase operations with random parameters.")

    # Profile and Asset Data
    print("\n📊 PROFILE AND ASSET DATA")
    print("-" * 60)
    print("8. get_profile()")
    print("   Retrieves user profile information.")
    print("9. assets_open()")
    print("   Lists all available assets and checks which ones are open.")
    print("10. get_payout()")
    print("    Checks the payout percentage of a specific asset.")

    # Candlestick Data
    print("\n🕯️ CANDLESTICK DATA")
    print("-" * 60)
    print("11. get_historical_candles()")
    print("    Retrieves historical candlestick data for a specific asset.")
    print("12. capture_live_candles()")
    print("    Captures live candlestick data for a specific asset.")
    print("13. combined_historical_and_live()")
    print("    Retrieves historical data and then captures live candles.")

    # Option Selling
    print("\n🔄 OPTION SELLING")
    print("-" * 60)
    print("14. sell_option()")
    print("    Performs an early sale of a previously purchased option.")

    # Data Analysis
    print("\n📈 DATA ANALYSIS")
    print("-" * 60)
    print("15. analyze_csv_file()")
    print("    Analyzes a CSV file containing candle data.")

    print("\n" + "=" * 80)
    return True

async def main_menu():
    """Display main menu and handle user choices"""
    while True:
        print("\n" + "=" * 60)
        print(ascii_art)
        print("=" * 60)
        print("MAIN MENU")
        print("=" * 60)
        print("1. 🌐 Server Connection")
        print("2. 💰 Balance Management")
        print("3. 🛒 Purchase Operations")
        print("4. 📊 Profile and Asset Data")
        print("5. 🕯️ Candlestick Data")
        print("6. 🔄 Option Selling")
        print("7. 📈 Data Analysis")
        print("8. 🔍 Show All Features")
        print("9. ❌ Exit")

        choice = input("\nChoose an option (1-9): ")

        if choice == "1":
            # Server Connection
            print("\n🌐 SERVER CONNECTION")
            print("-" * 60)
            print("1. Connect to server")
            print("2. Test connection")
            print("3. Back to main menu")

            sub_choice = input("\nChoose an option (1-3): ")

            if sub_choice == "1":
                await connect()
            elif sub_choice == "2":
                await test_connection()
            elif sub_choice == "3":
                continue
            else:
                print("Invalid option.")

        elif choice == "2":
            # Balance Management
            print("\n💰 BALANCE MANAGEMENT")
            print("-" * 60)
            print("1. Get current balance")
            print("2. Refill demo balance")
            print("3. Back to main menu")

            sub_choice = input("\nChoose an option (1-3): ")

            if sub_choice == "1":
                await get_balance()
            elif sub_choice == "2":
                await balance_refill()
            elif sub_choice == "3":
                continue
            else:
                print("Invalid option.")

        elif choice == "3":
            # Purchase Operations
            print("\n🛒 PURCHASE OPERATIONS")
            print("-" * 60)
            print("1. Simple purchase")
            print("2. Purchase and check win")
            print("3. Multiple purchases")
            print("4. Back to main menu")

            sub_choice = input("\nChoose an option (1-4): ")

            if sub_choice == "1":
                await buy_simple()
            elif sub_choice == "2":
                await buy_and_check_win()
            elif sub_choice == "3":
                orders = int(input("Enter number of orders (default: 10): ") or 10)
                await buy_multiple(orders)
            elif sub_choice == "4":
                continue
            else:
                print("Invalid option.")

        elif choice == "4":
            # Profile and Asset Data
            print("\n📊 PROFILE AND ASSET DATA")
            print("-" * 60)
            print("1. Get profile information")
            print("2. List available assets")
            print("3. Check asset payout")
            print("4. Back to main menu")

            sub_choice = input("\nChoose an option (1-4): ")

            if sub_choice == "1":
                await get_profile()
            elif sub_choice == "2":
                await assets_open()
            elif sub_choice == "3":
                await get_payout()
            elif sub_choice == "4":
                continue
            else:
                print("Invalid option.")

        elif choice == "5":
            # Candlestick Data
            print("\n🕯️ CANDLESTICK DATA")
            print("-" * 60)
            print("1. Capture live candles")
            print("2. Retrieve historical data")
            print("3. Retrieve historical + capture live")
            print("4. Back to main menu")

            sub_choice = input("\nChoose an option (1-4): ")

            if sub_choice == "1":
                # Capture live candles
                asset = input(f"Enter asset code (default: {DEFAULT_ASSET}): ") or DEFAULT_ASSET
                period = int(input(f"Enter candle period in seconds (default: {DEFAULT_PERIOD}): ") or DEFAULT_PERIOD)

                continuous_input = input("Run continuously? (y/n, default: y): ").lower() or "y"
                continuous = continuous_input.startswith("y")

                if not continuous:
                    duration = int(input(f"Enter duration in seconds (default: {DEFAULT_DURATION}): ") or DEFAULT_DURATION)
                else:
                    duration = DEFAULT_DURATION

                output_file = input("Enter output CSV file (default: auto-generated): ")
                if not output_file:
                    output_file = f"{asset.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_candles.csv"

                await capture_live_candles(asset, period, duration, continuous, output_file)

            elif sub_choice == "2":
                # Retrieve historical data
                asset = input(f"Enter asset code (default: {DEFAULT_ASSET}): ") or DEFAULT_ASSET
                period = int(input(f"Enter candle period in seconds (default: {DEFAULT_PERIOD}): ") or DEFAULT_PERIOD)
                days = int(input(f"Enter number of days to retrieve (default: {DEFAULT_DAYS}): ") or DEFAULT_DAYS)

                output_file = input("Enter output CSV file (default: auto-generated): ")
                if not output_file:
                    output_file = f"{asset.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_historical.csv"

                await get_historical_candles(asset, period, days, output_file)

            elif sub_choice == "3":
                # Retrieve historical data + capture live candles
                asset = input(f"Enter asset code (default: {DEFAULT_ASSET}): ") or DEFAULT_ASSET
                period = int(input(f"Enter candle period in seconds (default: {DEFAULT_PERIOD}): ") or DEFAULT_PERIOD)
                days = int(input(f"Enter number of days to retrieve (default: {DEFAULT_DAYS}): ") or DEFAULT_DAYS)

                continuous_input = input("Run continuously? (y/n, default: y): ").lower() or "y"
                continuous = continuous_input.startswith("y")

                if not continuous:
                    duration = int(input(f"Enter duration in seconds (default: {DEFAULT_DURATION}): ") or DEFAULT_DURATION)
                else:
                    duration = DEFAULT_DURATION

                output_file = input("Enter output CSV file (default: auto-generated): ")
                if not output_file:
                    output_file = f"{asset.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_combined.csv"

                await combined_historical_and_live(asset, period, days, duration, continuous, output_file)

            elif sub_choice == "4":
                continue
            else:
                print("Invalid option.")

        elif choice == "6":
            # Option Selling
            print("\n🔄 OPTION SELLING")
            print("-" * 60)
            await sell_option()

        elif choice == "7":
            # Data Analysis
            print("\n📈 DATA ANALYSIS")
            print("-" * 60)
            file_path = input("Enter the path to the CSV file: ")
            await analyze_csv_file(file_path)

        elif choice == "8":
            # Show All Features
            await get_all_options()

        elif choice == "9":
            print("\nExiting program. Goodbye!")
            break

        else:
            print("\nInvalid option. Please choose a number between 1 and 9.")

async def main():
    try:
        # Initial connection to the server
        print("\n" + "=" * 60)
        print(ascii_art)
        print("=" * 60)
        print("QUOTEX DATA MANAGER - STARTING")
        print("=" * 60)

        # Connect to the server
        connected, message = await connect()
        if connected:
            # Get account type
            await select_account()

            # Show welcome message
            print("\nWelcome to Quotex Data Manager!")
            print("All features are now available in one place.")

            # Display main menu
            await main_menu()
        else:
            print("\nCould not connect to the server. Please check your internet connection and credentials.")
            print("Exiting program.")
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")
    finally:
        # Ensure we close the connection
        try:
            await client.close()
            print("Connection closed.")
        except:
            pass

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
