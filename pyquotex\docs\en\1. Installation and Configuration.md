# PyQuotex Installation and Configuration

## Table of Contents
- [System Requirements](#system-requirements)
- [Installation](#installation)
  - [From pip](#from-pip)
  - [From GitHub](#from-github)
- [Initial Setup](#initial-setup)
- [Credentials Management](#credentials-management)
- [SSL/TLS Configuration](#ssltls-configuration)

## System Requirements

To use PyQuotex, you'll need:

- Python 3.8 or higher
- Latest version of OpenSSL
- Compatible operating system:
  - Linux
  - Windows
  - macOS

### Main Dependencies
```
websocket-client>=1.8.0
requests>=2.31.0
beautifulsoup4>=4.12.2
```

### Optional Dependencies
```
playwright>=1.44.0
numpy>=2.2.3,<3.0.0
playwright-stealth>=1.0.6,<2.0.0
```

## Installation

### From pip
You can install PyQuotex directly from GitHub using pip:

```bash
pip install git+https://github.com/cleitonleonel/pyquotex.git
```

### From GitHub and Poetry
You can also clone the repository and perform a local installation:

- First [Install Poetry](https://python-poetry.org/docs/#installing-with-the-official-installer)

```bash
git clone https://github.com/cleitonleonel/pyquotex.git
cd pyquotex
poetry install
```

### Installing Browsers for Playwright
After installing PyQuotex, you need to install the necessary browsers for Playwright:

```bash
playwright install
```

## Initial Setup

To start using PyQuotex, first import and configure the client:

```python
from quotexapi.stable_api import Quotex

client = Quotex(
    email="<EMAIL>",
    password="your_password",
    lang="en"  # Default language: "pt" (Portuguese)
)

# Enable debug mode (optional)
client.debug_ws_enable = True
```

## Credentials Management

There are two main ways to handle credentials:

### 1. Configuration File
PyQuotex will automatically look for a `config.ini` file in the `settings` folder. If it doesn't exist, it will create one requesting credentials:

```ini
[settings]
email=<EMAIL>
password=your_password
```

### 2. Direct Configuration
You can provide credentials directly when creating the client instance:

```python
client = Quotex(
    email="<EMAIL>",
    password="your_password"
)
```

## SSL/TLS Configuration

### Windows
For Windows, it's necessary to install the latest version of OpenSSL:
1. Download the installer from [Openssl-Windows](https://slproweb.com/products/Win32OpenSSL.html)
2. Install following the installer instructions

### Linux
On Linux systems, update OpenSSL using the package manager:

```bash
sudo apt update
sudo apt install openssl
```

### SSL Configuration in Code
PyQuotex handles SSL configuration automatically, but you can customize it:

```python
import ssl
import certifi

# SSL context configuration to use TLS 1.3
ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1 | ssl.OP_NO_TLSv1_2
ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
ssl_context.load_verify_locations(certifi.where())
```

### Certificate Management
PyQuotex uses SSL certificates for secure connections:

```python
import os
import certifi

# Configure certificate path
cert_path = os.path.join("../", "quotex.pem")
os.environ['SSL_CERT_FILE'] = cert_path
os.environ['WEBSOCKET_CLIENT_CA_BUNDLE'] = cert_path
```

---

For more information and support, you can join the community [Telegram group](https://t.me/+Uzcmc-NZvN4xNTQx).