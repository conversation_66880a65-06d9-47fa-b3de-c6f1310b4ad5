import numpy as np
import pandas as pd
import tensorflow as tf
from xgboost import XGBClassifier
import os
import datetime
import joblib
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import json

class EnsembleModel:
    """
    Advanced ensemble model for binary options prediction

    This ensemble combines predictions from multiple models (LSTM-GRU, XGBoost, DQN)
    with dynamic weighting based on recent performance.
    """
    def __init__(self, models=None, model_types=None, weights=None, window_size=10):
        """
        Initialize the ensemble model

        Parameters:
        - models: List of trained models
        - model_types: List of model types corresponding to each model
        - weights: List of initial weights for each model
        - window_size: Window size for performance tracking
        """
        self.models = models or []
        self.model_types = model_types or []
        self.weights = weights or []
        self.window_size = window_size

        # Performance tracking
        self.performance_history = []
        self.prediction_history = []

        # If weights not provided, use equal weighting
        if not self.weights and self.models:
            self.weights = [1/len(self.models)] * len(self.models)

        # Ensure model_types is provided for each model
        if len(self.models) != len(self.model_types):
            raise ValueError("Number of models must match number of model types")

    def add_model(self, model, model_type, weight=1.0):
        """
        Add a model to the ensemble

        Parameters:
        - model: Trained model to add
        - model_type: Type of model ('lstm', 'xgboost', 'dqn', etc.)
        - weight: Initial weight for the model
        """
        self.models.append(model)
        self.model_types.append(model_type)

        # Normalize weights
        self.weights.append(weight)
        self._normalize_weights()

        print(f"Added {model_type} model to ensemble with weight {weight:.2f}")
        print(f"Current weights: {[f'{w:.2f}' for w in self.weights]}")

    def _normalize_weights(self):
        """
        Normalize weights to sum to 1
        """
        total = sum(self.weights)
        if total > 0:
            self.weights = [w/total for w in self.weights]

    def predict(self, X, return_individual=False):
        """
        Make an ensemble prediction

        Parameters:
        - X: Dictionary containing input data for different model types
        - return_individual: Whether to return individual model predictions

        Returns:
        - Dictionary containing prediction results
        """
        if not self.models:
            raise ValueError("No models in the ensemble")

        predictions = []
        individual_predictions = []
        confidences = []

        for model, model_type, weight in zip(self.models, self.model_types, self.weights):
            # Model-specific prediction logic
            if model_type == 'lstm':
                # LSTM/GRU models
                if 'sequence_data' not in X:
                    raise ValueError("sequence_data required for LSTM model")

                pred_prob = model.predict(X['sequence_data'], verbose=0)
                pred_prob = pred_prob.flatten()

            elif model_type == 'xgboost':
                # XGBoost model
                if 'tabular_data' not in X:
                    raise ValueError("tabular_data required for XGBoost model")

                pred_prob = model.predict_proba(X['tabular_data'])[:, 1]

            elif model_type == 'dqn':
                # DQN agent
                if 'state' not in X:
                    raise ValueError("state required for DQN model")

                # Get Q-values
                market_features = np.expand_dims(X['state']['market_features'], axis=0)
                portfolio_state = np.expand_dims(X['state']['portfolio_state'], axis=0)
                q_values = model.model.predict([market_features, portfolio_state], verbose=0)[0]

                # Convert Q-values to probability
                # Higher Q-value for action 1 (UP) means higher probability of UP
                pred_prob = np.array([q_values[1] / (q_values[0] + q_values[1] + 1e-6)])

            else:
                # Generic model with predict_proba method
                try:
                    if 'tabular_data' in X:
                        pred_prob = model.predict_proba(X['tabular_data'])[:, 1]
                    else:
                        pred_prob = model.predict(X)
                except:
                    pred_prob = model.predict(X)

            # Calculate confidence for this model
            confidence = abs(pred_prob - 0.5) * 2  # Scale to 0-1
            confidences.append(float(confidence))

            # Store individual prediction
            individual_predictions.append({
                'model_type': model_type,
                'probability': float(pred_prob[0]),
                'prediction': int(pred_prob[0] > 0.5),
                'confidence': float(confidence),
                'weight': float(weight)
            })

            # Add weighted prediction
            predictions.append(pred_prob * weight)

        # Combine predictions using weights
        ensemble_prediction = sum(predictions)

        # Convert to binary prediction
        binary_prediction = (ensemble_prediction > 0.5).astype(int)

        # Calculate ensemble confidence
        ensemble_confidence = abs(ensemble_prediction - 0.5) * 2  # Scaled 0-1 confidence

        # Create result dictionary
        result = {
            'probability': float(ensemble_prediction[0]),
            'prediction': int(binary_prediction[0]),
            'direction': 'UP' if binary_prediction[0] == 1 else 'DOWN',
            'confidence': float(ensemble_confidence[0]),
            'timestamp': datetime.datetime.now().isoformat()
        }

        # Add individual predictions if requested
        if return_individual:
            result['individual_predictions'] = individual_predictions

        # Store prediction for performance tracking
        self.prediction_history.append(result)

        return result

    def update_weights(self, performance_metrics):
        """
        Update model weights based on performance metrics

        Parameters:
        - performance_metrics: List of dictionaries containing performance metrics for each model
        """
        if len(performance_metrics) != len(self.models):
            raise ValueError("Number of performance metrics must match number of models")

        # Store performance metrics
        self.performance_history.append({
            'timestamp': datetime.datetime.now().isoformat(),
            'metrics': performance_metrics
        })

        # Keep only the last window_size performance metrics
        if len(self.performance_history) > self.window_size:
            self.performance_history = self.performance_history[-self.window_size:]

        # Calculate weights based on recent performance
        # Use a combination of accuracy, precision, recall, and ROC AUC
        new_weights = []

        for i, metrics in enumerate(performance_metrics):
            # Calculate combined score
            combined_score = (
                metrics.get('accuracy', 0) * 0.3 +
                metrics.get('precision', 0) * 0.2 +
                metrics.get('recall', 0) * 0.2 +
                metrics.get('roc_auc', 0) * 0.3
            )

            # Apply smoothing with current weight
            current_weight = self.weights[i]
            smoothed_weight = 0.7 * combined_score + 0.3 * current_weight

            new_weights.append(smoothed_weight)

        # Update weights
        self.weights = new_weights

        # Normalize weights
        self._normalize_weights()

        print("Updated ensemble weights:")
        for model_type, weight in zip(self.model_types, self.weights):
            print(f"  {model_type}: {weight:.4f}")

    def evaluate(self, y_true):
        """
        Evaluate ensemble performance on recent predictions

        Parameters:
        - y_true: Array of true labels corresponding to recent predictions

        Returns:
        - Dictionary of performance metrics
        """
        if len(self.prediction_history) == 0:
            return None

        # Get predictions from history
        y_pred = [p['prediction'] for p in self.prediction_history[-len(y_true):]]
        y_prob = [p['probability'] for p in self.prediction_history[-len(y_true):]]

        # Calculate metrics
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        f1 = f1_score(y_true, y_pred)
        roc_auc = roc_auc_score(y_true, y_prob)

        # Create metrics dictionary
        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'roc_auc': roc_auc
        }

        return metrics

    def save(self, filepath):
        """
        Save ensemble model configuration

        Parameters:
        - filepath: Path to save ensemble configuration
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Create configuration dictionary
        config = {
            'weights': self.weights,
            'model_types': self.model_types,
            'window_size': self.window_size,
            'timestamp': datetime.datetime.now().isoformat()
        }

        # Save configuration
        with open(filepath, 'w') as f:
            json.dump(config, f, indent=4)

        print(f"Ensemble configuration saved to {filepath}")

    def load_config(self, filepath):
        """
        Load ensemble configuration

        Parameters:
        - filepath: Path to ensemble configuration file
        """
        with open(filepath, 'r') as f:
            config = json.load(f)

        self.weights = config['weights']
        self.model_types = config['model_types']
        self.window_size = config['window_size']

        print(f"Ensemble configuration loaded from {filepath}")
        print(f"Model types: {self.model_types}")
        print(f"Weights: {[f'{w:.2f}' for w in self.weights]}")

def create_ensemble(lstm_model=None, xgb_model=None, dqn_agent=None,
                   lstm_weight=0.4, xgb_weight=0.4, dqn_weight=0.2,
                   window_size=10):
    """
    Create an ensemble model with the specified models

    Parameters:
    - lstm_model: Trained LSTM-GRU model
    - xgb_model: Trained XGBoost model
    - dqn_agent: Trained DQN agent
    - lstm_weight: Initial weight for LSTM model
    - xgb_weight: Initial weight for XGBoost model
    - dqn_weight: Initial weight for DQN agent
    - window_size: Window size for performance tracking

    Returns:
    - ensemble: Configured ensemble model
    """
    ensemble = EnsembleModel(window_size=window_size)

    # Add models if provided
    if lstm_model is not None:
        ensemble.add_model(lstm_model, 'lstm', weight=lstm_weight)

    if xgb_model is not None:
        ensemble.add_model(xgb_model, 'xgboost', weight=xgb_weight)

    if dqn_agent is not None:
        ensemble.add_model(dqn_agent, 'dqn', weight=dqn_weight)

    return ensemble

def evaluate_ensemble(ensemble, X_test, y_test):
    """
    Evaluate ensemble model performance

    Parameters:
    - ensemble: Trained ensemble model
    - X_test: Test data
    - y_test: True labels

    Returns:
    - metrics: Dictionary of performance metrics
    """
    # Make predictions
    y_pred = []
    y_prob = []

    for i in range(len(y_test)):
        # Prepare input for prediction
        X = {k: v[i:i+1] for k, v in X_test.items()}

        # Make prediction
        prediction = ensemble.predict(X)

        # Store prediction
        y_pred.append(prediction['prediction'])
        y_prob.append(prediction['probability'])

    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    roc_auc = roc_auc_score(y_test, y_prob)

    # Create metrics dictionary
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'roc_auc': roc_auc
    }

    # Print metrics
    print("\nEnsemble Model Evaluation:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1 Score: {f1:.4f}")
    print(f"ROC AUC: {roc_auc:.4f}")

    return metrics