#!/usr/bin/env python
"""
Test script to verify ensemble model initialization during startup
"""

import sys
from PyQt5 import QtWidgets

def test_startup_ensemble():
    """Test that ensemble models are initialized during startup"""
    print("🔍 Testing Ensemble Model Initialization During Startup...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import trading UI class
        from trading_ui import TradingUI
        
        # Create UI instance (this should trigger ensemble initialization)
        print("Creating TradingUI instance...")
        ui = TradingUI()
        
        # Check if ensemble model manager was initialized
        if hasattr(ui, 'ensemble_model_manager') and ui.ensemble_model_manager:
            print(f"✅ Ensemble model manager initialized")
            
            # Check loaded models
            if hasattr(ui.ensemble_model_manager, 'models'):
                loaded_models = list(ui.ensemble_model_manager.models.keys())
                print(f"✅ Loaded models: {loaded_models}")
                print(f"✅ Total models: {len(loaded_models)}")
                
                # Check each expected model
                expected_models = ['xgboost', 'lstm_gru', 'transformer', 'dqn']
                for model_name in expected_models:
                    if model_name in loaded_models:
                        print(f"✅ {model_name}: LOADED")
                    else:
                        print(f"❌ {model_name}: NOT LOADED")
            else:
                print("❌ No models attribute found")
        else:
            print("❌ Ensemble model manager not initialized")
        
        # Check if model weights were initialized
        if hasattr(ui, 'model_weights') and ui.model_weights:
            print(f"✅ Model weights initialized")
            
            # Filter out metadata keys
            metadata_keys = {'data_source', 'total_models', 'timestamp'}
            model_weights = {k: v for k, v in ui.model_weights.items() 
                           if k not in metadata_keys and isinstance(v, (int, float))}
            
            print(f"✅ Model weights: {len(model_weights)} models")
            for model_name, weight in model_weights.items():
                display_name = model_name.replace('_', ' ').title()
                print(f"   • {display_name}: {weight:.4f}")
        else:
            print("❌ Model weights not initialized")
        
        # Check if real model manager was initialized
        if hasattr(ui, 'real_model_manager') and ui.real_model_manager:
            print(f"✅ Real model manager initialized")
        else:
            print("❌ Real model manager not initialized")
        
        # Don't show the UI, just test initialization
        app.quit()
        
        return ui
        
    except Exception as e:
        print(f"❌ Error testing startup ensemble: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_learning_page_update():
    """Test that Learning page gets the correct model weights"""
    print("\n🔍 Testing Learning Page Model Weights Update...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import trading UI class
        from trading_ui import TradingUI
        
        # Create UI instance
        ui = TradingUI()
        
        # Check if learning metrics widget exists
        if hasattr(ui, 'learning_metrics'):
            print("✅ Learning metrics widget found")
            
            # Test the update_model_weights method
            if hasattr(ui, 'model_weights') and ui.model_weights:
                print("✅ Model weights available for update")
                
                # Call the update method like the UI does
                ui.learning_metrics.update_model_weights(ui.model_weights)
                
                # Check the table
                if hasattr(ui.learning_metrics, 'weights_table'):
                    row_count = ui.learning_metrics.weights_table.rowCount()
                    print(f"✅ Learning page model table has {row_count} rows")
                    
                    # Print table contents
                    for row in range(row_count):
                        model_item = ui.learning_metrics.weights_table.item(row, 0)
                        weight_item = ui.learning_metrics.weights_table.item(row, 1)
                        if model_item and weight_item:
                            model_name = model_item.text()
                            weight_value = weight_item.text()
                            print(f"   • {model_name}: {weight_value}")
                else:
                    print("❌ Weights table not found")
            else:
                print("❌ No model weights available")
        else:
            print("❌ Learning metrics widget not found")
        
        app.quit()
        return ui
        
    except Exception as e:
        print(f"❌ Error testing learning page update: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 Starting Startup Model Tests...\n")
    
    # Test 1: Startup ensemble initialization
    ui1 = test_startup_ensemble()
    
    # Test 2: Learning page update
    ui2 = test_learning_page_update()
    
    print("\n📊 Test Results:")
    if ui1:
        print("✅ Startup ensemble initialization: PASSED")
    else:
        print("❌ Startup ensemble initialization: FAILED")
    
    if ui2:
        print("✅ Learning page update: PASSED")
    else:
        print("❌ Learning page update: FAILED")
    
    if ui1 and ui2:
        print("\n🎉 ALL TESTS PASSED! Learning page should show all 4 models.")
    else:
        print("\n❌ Some tests failed. Check the output above.")
