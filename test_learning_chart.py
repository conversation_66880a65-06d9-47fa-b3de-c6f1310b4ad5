#!/usr/bin/env python
"""
Test script to verify learning chart functionality
"""

import sys
import numpy as np
from PyQt5 import QtWidgets

def test_learning_chart_update():
    """Test that learning chart is updated correctly"""
    print("🔍 Testing Learning Chart Update...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import trading UI class
        from trading_ui import TradingUI
        
        # Create UI instance
        print("Creating TradingUI instance...")
        ui = TradingUI()
        
        # Check if learning chart exists
        if hasattr(ui, 'learning_chart'):
            print("✅ Learning chart found")
            
            # Check if the chart has the correct method
            if hasattr(ui.learning_chart, 'set_learning_data'):
                print("✅ Learning chart has set_learning_data method")
                
                # Test updating the chart with sample data
                print("📊 Testing chart update with sample data...")
                iterations = list(range(1, 11))  # 10 iterations
                accuracy_data = [0.5 + i * 0.04 for i in range(10)]  # Improving accuracy
                loss_data = [1.0 - acc for acc in accuracy_data]  # Corresponding loss
                
                # Update the chart
                ui.learning_chart.set_learning_data(iterations, accuracy_data, loss_data)
                print(f"✅ Chart updated with {len(iterations)} data points")
                print(f"   Accuracy range: {min(accuracy_data):.3f} - {max(accuracy_data):.3f}")
                print(f"   Loss range: {min(loss_data):.3f} - {max(loss_data):.3f}")
                
                # Test the force_learning_update method
                print("🔄 Testing force_learning_update method...")
                ui.force_learning_update()
                print("✅ force_learning_update completed")
                
            else:
                print("❌ Learning chart missing set_learning_data method")
        else:
            print("❌ Learning chart not found")
        
        # Check if accuracy_history is being generated
        if hasattr(ui, 'accuracy_history'):
            print(f"✅ Accuracy history found with {len(ui.accuracy_history)} entries")
            if ui.accuracy_history:
                sample_entry = ui.accuracy_history[0]
                print(f"   Sample entry structure: {list(sample_entry.keys())}")
        else:
            print("⚠️ No accuracy_history found")
        
        # Test the update_learning method
        print("🔄 Testing update_learning method...")
        if hasattr(ui, 'update_learning'):
            ui.update_learning()
            print("✅ update_learning completed")
        else:
            print("❌ update_learning method not found")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Error testing learning chart: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_chart_data_formats():
    """Test different data formats for learning chart"""
    print("\n🔍 Testing Learning Chart Data Formats...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import chart widget directly
        from chart_widgets import LearningProgressChart
        
        # Create chart instance
        chart = LearningProgressChart()
        print("✅ LearningProgressChart created")
        
        # Test 1: Simple data
        print("📊 Test 1: Simple data format")
        iterations = [1, 2, 3, 4, 5]
        accuracy = [0.5, 0.6, 0.7, 0.75, 0.8]
        loss = [0.5, 0.4, 0.3, 0.25, 0.2]
        
        chart.set_learning_data(iterations, accuracy, loss)
        print("✅ Simple data format test passed")
        
        # Test 2: Longer data series
        print("📊 Test 2: Longer data series")
        iterations = list(range(1, 31))  # 30 iterations
        accuracy = [0.5 + i * 0.01 for i in range(30)]  # Gradually improving
        loss = [1.0 - acc for acc in accuracy]
        
        chart.set_learning_data(iterations, accuracy, loss)
        print("✅ Longer data series test passed")
        
        # Test 3: Real-world like data with noise
        print("📊 Test 3: Real-world data with noise")
        iterations = list(range(1, 51))  # 50 iterations
        accuracy = []
        for i in range(50):
            base_acc = 0.5 + i * 0.008  # Gradual improvement
            noise = np.random.uniform(-0.02, 0.02)  # Add noise
            acc = max(0.3, min(0.95, base_acc + noise))  # Clamp values
            accuracy.append(acc)
        loss = [1.0 - acc for acc in accuracy]
        
        chart.set_learning_data(iterations, accuracy, loss)
        print("✅ Real-world data test passed")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Error testing chart data formats: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Learning Chart Tests...\n")
    
    # Test 1: Learning chart update in main UI
    test1_result = test_learning_chart_update()
    
    # Test 2: Chart data formats
    test2_result = test_learning_chart_data_formats()
    
    print("\n📊 Test Results:")
    if test1_result:
        print("✅ Learning chart update test: PASSED")
    else:
        print("❌ Learning chart update test: FAILED")
    
    if test2_result:
        print("✅ Chart data formats test: PASSED")
    else:
        print("❌ Chart data formats test: FAILED")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED! Learning chart should be working.")
    else:
        print("\n❌ Some tests failed. Check the output above.")
