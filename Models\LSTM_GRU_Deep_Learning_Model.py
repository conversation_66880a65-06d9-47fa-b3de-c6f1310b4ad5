import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model, load_model
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Input, Bidirectional
from tensorflow.keras.layers import Conv1D, MaxPooling1D, Flatten, Attention, Concatenate, Add, Multiply, LayerNormalization
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, TensorBoard
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l1_l2
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import os
import datetime

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

def create_lstm_gru_model(input_shape, model_type='advanced'):
    """
    Create a hybrid LSTM-GRU model for time series prediction

    Parameters:
    - input_shape: Shape of input data (sequence_length, features)
    - model_type: 'simple', 'advanced', 'attention', 'cnn_lstm_attention', 'deep_cnn_lstm', or 'transformer' architecture

    Returns:
    - Compiled Keras model
    """
    inputs = Input(shape=input_shape)

    if model_type == 'simple':
        # Simple architecture
        x = LSTM(128, return_sequences=True)(inputs)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        x = GRU(64, return_sequences=False)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

        x = Dense(32, activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.2)(x)

    elif model_type == 'advanced':
        # Advanced architecture with bidirectional layers and more depth
        # CNN layer to extract local patterns
        conv = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(inputs)
        conv = BatchNormalization()(conv)

        # Bidirectional LSTM to capture sequence patterns in both directions
        x = Bidirectional(LSTM(128, return_sequences=True))(conv)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        # Bidirectional GRU layer
        x = Bidirectional(GRU(64, return_sequences=True))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        # Another GRU layer
        x = GRU(64, return_sequences=False)(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        # Dense layers with residual connections
        dense1 = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        dense1 = BatchNormalization()(dense1)
        dense1 = Dropout(0.3)(dense1)

        dense2 = Dense(32, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(dense1)
        dense2 = BatchNormalization()(dense2)
        dense2 = Dropout(0.3)(dense2)

        # Residual connection
        x = Dense(32, activation='linear')(dense1)
        x = Add()([x, dense2])
        x = tf.keras.activations.relu(x)

    elif model_type == 'attention':
        # Architecture with attention mechanism
        # CNN layer to extract local patterns
        conv = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(inputs)
        conv = MaxPooling1D(pool_size=2)(conv)
        conv = BatchNormalization()(conv)

        # LSTM layer
        lstm = Bidirectional(LSTM(128, return_sequences=True))(conv)
        lstm = BatchNormalization()(lstm)

        # GRU layer
        gru = Bidirectional(GRU(64, return_sequences=True))(lstm)
        gru = BatchNormalization()(gru)

        # Self-attention mechanism
        attention = tf.keras.layers.MultiHeadAttention(
            num_heads=4, key_dim=32
        )(gru, gru)
        attention = BatchNormalization()(attention)

        # Combine attention with GRU output
        x = Add()([gru, attention])
        x = Flatten()(x)

        # Dense layers
        x = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.4)(x)

        x = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.4)(x)

    elif model_type == 'cnn_lstm_attention':
        # Advanced CNN-LSTM with Multi-Head Attention architecture
        # Multiple CNN layers with different kernel sizes for multi-scale feature extraction
        conv1 = Conv1D(filters=64, kernel_size=1, padding='same', activation='relu')(inputs)
        conv3 = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(inputs)
        conv5 = Conv1D(filters=64, kernel_size=5, padding='same', activation='relu')(inputs)

        # Concatenate multi-scale features
        conv_concat = Concatenate()([conv1, conv3, conv5])
        conv_concat = BatchNormalization()(conv_concat)

        # Apply another CNN layer to reduce dimensionality
        conv_reduced = Conv1D(filters=128, kernel_size=1, padding='same', activation='relu')(conv_concat)
        conv_reduced = BatchNormalization()(conv_reduced)

        # Bidirectional LSTM layers
        lstm1 = Bidirectional(LSTM(128, return_sequences=True))(conv_reduced)
        lstm1 = BatchNormalization()(lstm1)
        lstm1 = Dropout(0.3)(lstm1)

        # Multi-head self-attention mechanism
        attention = tf.keras.layers.MultiHeadAttention(
            num_heads=8, key_dim=32
        )(lstm1, lstm1)
        attention = BatchNormalization()(attention)

        # Residual connection with attention output
        lstm_attention = Add()([lstm1, attention])
        lstm_attention = LayerNormalization(epsilon=1e-6)(lstm_attention)

        # Second LSTM layer
        lstm2 = Bidirectional(LSTM(64, return_sequences=True))(lstm_attention)
        lstm2 = BatchNormalization()(lstm2)
        lstm2 = Dropout(0.3)(lstm2)

        # Global average and max pooling for feature extraction
        avg_pool = tf.keras.layers.GlobalAveragePooling1D()(lstm2)
        max_pool = tf.keras.layers.GlobalMaxPooling1D()(lstm2)

        # Concatenate pooled features
        x = Concatenate()([avg_pool, max_pool])

        # Dense layers with skip connections
        dense1 = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        dense1 = BatchNormalization()(dense1)
        dense1 = Dropout(0.4)(dense1)

        dense2 = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(dense1)
        dense2 = BatchNormalization()(dense2)
        dense2 = Dropout(0.4)(dense2)

        # Skip connection
        skip_conn = Dense(64, activation='linear')(dense1)
        x = Add()([skip_conn, dense2])
        x = tf.keras.activations.relu(x)

    elif model_type == 'deep_cnn_lstm':
        # Deep CNN-LSTM architecture inspired by state-of-the-art models
        # Initial convolutional block
        x = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(inputs)
        x = BatchNormalization()(x)
        x = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)
        x = MaxPooling1D(pool_size=2)(x)
        x = Dropout(0.2)(x)

        # Second convolutional block
        x = Conv1D(filters=128, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)
        x = Conv1D(filters=128, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)
        x = MaxPooling1D(pool_size=2)(x)
        x = Dropout(0.2)(x)

        # Third convolutional block
        x = Conv1D(filters=256, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)
        x = Conv1D(filters=256, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        # LSTM layers
        x = Bidirectional(LSTM(128, return_sequences=True, return_state=False))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        x = Bidirectional(LSTM(64, return_sequences=False, return_state=False))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        # Dense layers
        x = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.4)(x)

        x = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.4)(x)

    elif model_type == 'transformer':
        # Transformer-based architecture with multi-head self-attention
        # Initial feature extraction with Conv1D
        x = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(inputs)
        x = BatchNormalization()(x)
        x = Conv1D(filters=64, kernel_size=3, padding='same', activation='relu')(x)
        x = BatchNormalization()(x)

        # Define key parameters for transformer
        embed_dim = 64  # Embedding dimension
        num_heads = 4   # Number of attention heads
        ff_dim = 128    # Feed-forward network dimension

        # First transformer block
        # Multi-head self-attention
        attention_output1 = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )(x, x)
        attention_output1 = Dropout(0.1)(attention_output1)

        # Add & Normalize (residual connection and layer normalization)
        x1 = Add()([x, attention_output1])
        x1 = LayerNormalization(epsilon=1e-6)(x1)

        # Feed-forward network
        ffn_output1 = Dense(ff_dim, activation='relu')(x1)
        ffn_output1 = Dense(embed_dim)(ffn_output1)
        ffn_output1 = Dropout(0.1)(ffn_output1)

        # Add & Normalize
        x2 = Add()([x1, ffn_output1])
        x2 = LayerNormalization(epsilon=1e-6)(x2)

        # Second transformer block
        # Multi-head self-attention
        attention_output2 = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )(x2, x2)
        attention_output2 = Dropout(0.1)(attention_output2)

        # Add & Normalize
        x3 = Add()([x2, attention_output2])
        x3 = LayerNormalization(epsilon=1e-6)(x3)

        # Feed-forward network
        ffn_output2 = Dense(ff_dim, activation='relu')(x3)
        ffn_output2 = Dense(embed_dim)(ffn_output2)
        ffn_output2 = Dropout(0.1)(ffn_output2)

        # Add & Normalize
        x4 = Add()([x3, ffn_output2])
        x4 = LayerNormalization(epsilon=1e-6)(x4)

        # Global pooling (both average and max pooling for better feature extraction)
        avg_pool = tf.keras.layers.GlobalAveragePooling1D()(x4)
        max_pool = tf.keras.layers.GlobalMaxPooling1D()(x4)

        # Concatenate pooled features
        x = Concatenate()([avg_pool, max_pool])

        # Final MLP layers
        x = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        x = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

    # Output layer - binary classification
    outputs = Dense(1, activation='sigmoid')(x)

    model = Model(inputs=inputs, outputs=outputs)

    # Compile with binary crossentropy for binary classification
    model.compile(
        loss='binary_crossentropy',
        optimizer=Adam(learning_rate=0.001),
        metrics=['accuracy', tf.keras.metrics.AUC(), tf.keras.metrics.Precision(), tf.keras.metrics.Recall()]
    )

    return model

def prepare_time_series(df, feature_columns, target_column, seq_length=30, scaler_type='robust', test_size=0.2, val_size=0.25):
    """
    Prepare time series data for LSTM/GRU models with enhanced preprocessing

    Parameters:
    - df: DataFrame containing features and target
    - feature_columns: List of feature column names
    - target_column: Target column name
    - seq_length: Length of sequences to create
    - scaler_type: Type of scaler to use ('standard', 'robust', or 'minmax')
    - test_size: Proportion of data to use for testing
    - val_size: Proportion of non-test data to use for validation

    Returns:
    - X_train, X_val, X_test: Training, validation, and test features
    - y_train, y_val, y_test: Training, validation, and test targets
    - scaler: Fitted scaler object
    - feature_importance: Dictionary of feature importance (if applicable)
    """
    # Extract features and target
    data = df[feature_columns].copy()
    target = df[target_column].values

    # Handle missing values
    data = data.fillna(method='ffill')
    data = data.fillna(method='bfill')  # In case there are still NaNs at the beginning

    # Check for infinite values and replace with large numbers
    data = data.replace([np.inf, -np.inf], np.nan)
    data = data.fillna(method='ffill')

    # Handle categorical features
    categorical_columns = []
    for col in data.columns:
        if data[col].dtype == 'object' or data[col].dtype == 'category':
            categorical_columns.append(col)

    # Convert categorical columns to numeric
    if categorical_columns:
        print(f"Converting categorical columns: {categorical_columns}")
        for col in categorical_columns:
            if col == 'color':
                # Map color values to numeric
                color_map = {'green': 1.0, 'red': 0.0, 'doji': 0.5}
                data[col] = data[col].map(color_map)
                print(f"Converted 'color' column using mapping: {color_map}")
            elif col == 'market_regime':
                # Map market regime values to numeric
                regime_map = {'bullish': 1.0, 'bearish': -1.0, 'neutral': 0.0, 'ranging': 0.0}
                data[col] = data[col].map(regime_map)
                print(f"Converted 'market_regime' column using mapping: {regime_map}")
            else:
                # For other categorical columns, use one-hot encoding
                print(f"Dropping categorical column: {col} (not handled)")
                data = data.drop(col, axis=1)

    # Check for any remaining non-numeric columns
    non_numeric_cols = data.select_dtypes(exclude=['number']).columns.tolist()
    if non_numeric_cols:
        print(f"Warning: Dropping non-numeric columns: {non_numeric_cols}")
        data = data.drop(non_numeric_cols, axis=1)

    # Apply scaling based on specified scaler type
    if scaler_type == 'standard':
        scaler = StandardScaler()
    elif scaler_type == 'robust':
        scaler = RobustScaler()  # More robust to outliers
    elif scaler_type == 'minmax':
        scaler = MinMaxScaler(feature_range=(0, 1))
    else:
        raise ValueError(f"Unknown scaler type: {scaler_type}")

    # Fit scaler on data
    scaled_data = scaler.fit_transform(data.values)

    # Create sequences
    X, y = [], []
    for i in range(len(scaled_data) - seq_length):
        X.append(scaled_data[i:i+seq_length])
        y.append(target[i+seq_length])

    X = np.array(X)
    y = np.array(y)

    # Calculate feature importance (correlation with target)
    feature_importance = {}
    for i, feature in enumerate(data.columns):
        # Calculate correlation between each feature's last value in sequence and target
        feature_values = np.array([seq[-1, i] for seq in X])
        correlation = np.corrcoef(feature_values, y)[0, 1]
        feature_importance[feature] = abs(correlation)

    # Split into train, validation, and test sets (time-based split)
    # First split into train+val and test
    train_val_size = int(len(X) * (1 - test_size))
    X_train_val, X_test = X[:train_val_size], X[train_val_size:]
    y_train_val, y_test = y[:train_val_size], y[train_val_size:]

    # Then split train+val into train and val
    train_size = int(len(X_train_val) * (1 - val_size))
    X_train, X_val = X_train_val[:train_size], X_train_val[train_size:]
    y_train, y_val = y_train_val[:train_size], y_train_val[train_size:]

    print(f"Data shapes - X_train: {X_train.shape}, X_val: {X_val.shape}, X_test: {X_test.shape}")
    print(f"Class distribution - Train: {np.mean(y_train):.2f}, Val: {np.mean(y_val):.2f}, Test: {np.mean(y_test):.2f}")

    return X_train, X_val, X_test, y_train, y_val, y_test, scaler, feature_importance

def train_lstm_gru_model(df, feature_columns, target_column='target_1min', seq_length=30,
                         model_type='advanced', scaler_type='robust', batch_size=32, epochs=100,
                         patience=15, learning_rate=0.001, model_dir='models'):
    """
    Train the LSTM-GRU model with enhanced options

    Parameters:
    - df: DataFrame containing features and target
    - feature_columns: List of feature column names
    - target_column: Target column name
    - seq_length: Length of sequences to create
    - model_type: Type of model architecture ('simple', 'advanced', 'attention', 'cnn_lstm_attention', 'deep_cnn_lstm', or 'transformer')
    - scaler_type: Type of scaler to use ('standard', 'robust', or 'minmax')
    - batch_size: Batch size for training
    - epochs: Maximum number of epochs to train
    - patience: Patience for early stopping
    - learning_rate: Initial learning rate
    - model_dir: Directory to save model files

    Returns:
    - model: Trained model
    - scaler: Fitted scaler
    - history: Training history
    - feature_importance: Dictionary of feature importance
    - evaluation: Dictionary of evaluation metrics
    """
    # Create model directory if it doesn't exist
    os.makedirs(model_dir, exist_ok=True)

    # Prepare data
    X_train, X_val, X_test, y_train, y_val, y_test, scaler, feature_importance = prepare_time_series(
        df, feature_columns, target_column, seq_length, scaler_type
    )

    # Create model
    model = create_lstm_gru_model(
        input_shape=(X_train.shape[1], X_train.shape[2]),
        model_type=model_type
    )

    # Print model summary
    model.summary()

    # Create model checkpoint path with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    model_name = f"lstm_gru_{model_type}_{timestamp}"
    checkpoint_path = os.path.join(model_dir, f"{model_name}.h5")

    # Set up callbacks
    callbacks = [
        # Early stopping to prevent overfitting
        EarlyStopping(
            monitor='val_loss',
            patience=patience,
            restore_best_weights=True,
            verbose=1
        ),
        # Save best model
        ModelCheckpoint(
            checkpoint_path,
            save_best_only=True,
            monitor='val_loss',
            verbose=1
        ),
        # Reduce learning rate when plateau is reached
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=patience // 3,
            min_lr=1e-6,
            verbose=1
        ),
        # TensorBoard logging
        TensorBoard(
            log_dir=os.path.join('logs', model_name),
            histogram_freq=1
        )
    ]

    # Train model
    print(f"\nTraining {model_type} LSTM-GRU model for {target_column}...")
    history = model.fit(
        X_train, y_train,
        epochs=epochs,
        batch_size=batch_size,
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1
    )

    # Evaluate model on test set
    print("\nEvaluating model on test set...")
    test_results = model.evaluate(X_test, y_test, verbose=1)

    # Create evaluation dictionary
    metrics = model.metrics_names
    evaluation = {metric: value for metric, value in zip(metrics, test_results)}

    # Make predictions on test set
    y_pred_prob = model.predict(X_test)
    y_pred = (y_pred_prob > 0.5).astype(int).flatten()

    # Calculate additional metrics
    from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score

    # Print classification report
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))

    # Print confusion matrix
    print("\nConfusion Matrix:")
    cm = confusion_matrix(y_test, y_pred)
    print(cm)

    # Calculate ROC AUC
    roc_auc = roc_auc_score(y_test, y_pred_prob)
    print(f"ROC AUC: {roc_auc:.4f}")

    # Add additional metrics to evaluation
    evaluation['roc_auc'] = roc_auc

    # Save model architecture as JSON
    model_json = model.to_json()
    with open(os.path.join(model_dir, f"{model_name}.json"), "w") as json_file:
        json_file.write(model_json)

    # Save scaler
    import joblib
    scaler_path = os.path.join(model_dir, f"{model_name}_scaler.pkl")
    joblib.dump(scaler, scaler_path)

    print(f"\nModel saved to {checkpoint_path}")
    print(f"Scaler saved to {scaler_path}")

    return model, scaler, history, feature_importance, evaluation

def predict_with_lstm_gru(model, scaler, data, feature_columns, seq_length=30):
    """
    Make predictions using a trained LSTM-GRU model

    Parameters:
    - model: Trained LSTM-GRU model
    - scaler: Fitted scaler
    - data: DataFrame containing features
    - feature_columns: List of feature column names
    - seq_length: Length of sequences used during training

    Returns:
    - predictions: Dictionary containing prediction results
    """
    # Extract features
    features = data[feature_columns].copy()

    # Handle missing values
    features = features.fillna(method='ffill')
    features = features.fillna(method='bfill')

    # Scale features
    scaled_features = scaler.transform(features.values)

    # Create sequence for the latest data point
    if len(scaled_features) >= seq_length:
        sequence = scaled_features[-seq_length:].reshape(1, seq_length, len(feature_columns))

        # Make prediction
        prediction_prob = model.predict(sequence)[0][0]
        prediction = 1 if prediction_prob > 0.5 else 0
        confidence = max(prediction_prob, 1 - prediction_prob)

        return {
            'probability': float(prediction_prob),
            'prediction': int(prediction),
            'direction': 'UP' if prediction == 1 else 'DOWN',
            'confidence': float(confidence)
        }
    else:
        print(f"Not enough data points. Need at least {seq_length} data points.")
        return None
