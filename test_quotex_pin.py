#!/usr/bin/env python
"""
Test script for Quotex PIN Helper
This script tests the PIN helper for Quotex API
"""

import os
import sys
from quotex_pin_helper import QuotexPINHelper

def read_credentials_from_config():
    """Read credentials from config.ini file"""
    try:
        # List of possible config file locations
        config_files = [
            'config.ini',
            'settings/config.ini',
            'settings\\config.ini',
            'settings.ini'
        ]
        
        # Try to read directly from the files first
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"Found config file: {config_file}")
                
                # Try direct file reading
                try:
                    with open(config_file, 'r') as f:
                        lines = f.readlines()
                        email = None
                        password = None
                        
                        for line in lines:
                            if line.strip().startswith('email='):
                                email = line.strip().replace('email=', '')
                            elif line.strip().startswith('password='):
                                password = line.strip().replace('password=', '')
                        
                        if email and password:
                            print(f"Loaded credentials from {config_file} for: {email}")
                            return email, password
                except Exception as file_error:
                    print(f"Error reading {config_file} directly: {file_error}")
        
        # If no config file found, try environment variables
        email = os.environ.get('QUOTEX_EMAIL', '')
        password = os.environ.get('QUOTEX_PASSWORD', '')
        if email and password:
            print(f"Loaded credentials from environment variables for: {email}")
            return email, password
        
        return None, None
    except Exception as e:
        print(f"Error reading credentials from config: {e}")
        return None, None

def main():
    """Main function"""
    # Read credentials from config.ini
    email, password = read_credentials_from_config()
    
    if not email or not password:
        print("No credentials found. Please set them in config.ini or environment variables.")
        sys.exit(1)
    
    # Create PIN helper
    helper = QuotexPINHelper(email, password)
    
    # Request PIN
    print("\n=== Requesting PIN ===")
    success, message = helper.request_pin()
    print(f"PIN Request: {'Success' if success else 'Failed'}")
    print(f"Message: {message}")
    
    if not success:
        print("PIN request failed. Exiting.")
        sys.exit(1)
    
    # Ask for PIN
    pin = input("\nEnter the PIN code from your email: ")
    
    # Submit PIN
    print("\n=== Submitting PIN ===")
    success, message = helper.submit_pin(pin)
    print(f"PIN Submission: {'Success' if success else 'Failed'}")
    print(f"Message: {message}")
    
    if success:
        print("\nSuccessfully authenticated with Quotex API!")
        if isinstance(message, dict):
            print(f"Session data: {message}")
    else:
        print("\nFailed to authenticate with Quotex API.")

if __name__ == "__main__":
    main()
