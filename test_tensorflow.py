import os
import sys

print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

try:
    import tensorflow as tf
    print(f"TensorFlow version: {tf.__version__}")
    print("TensorFlow is available and working correctly.")
    
    # Try a simple TensorFlow operation
    a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
    b = tf.constant([[5.0, 6.0], [7.0, 8.0]])
    c = tf.matmul(a, b)
    print(f"Test operation result: {c}")
    
    # Check for GPU support
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        print(f"TensorFlow is using GPU: {len(gpus)} GPU(s) available")
        for gpu in gpus:
            print(f"  - {gpu.name}")
    else:
        print("TensorFlow is using CPU only")
        
except ImportError as e:
    print(f"Error importing TensorFlow: {e}")
except Exception as e:
    print(f"Error testing TensorFlow: {e}")
