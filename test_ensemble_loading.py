#!/usr/bin/env python
"""
Test script to verify ensemble model loading
"""

def test_ensemble_models():
    """Test that all 4 models are loaded in the ensemble"""
    print("🔍 Testing Ensemble Model Loading...")
    
    try:
        # Import the model manager
        from Models.model_manager import ModelManager
        
        # Initialize model manager
        manager = ModelManager(model_dir='models')
        
        # Load all models
        print("Loading all models...")
        manager.load_models()
        
        # Check which models were loaded
        loaded_models = list(manager.models.keys())
        print(f"✅ Loaded models: {loaded_models}")
        print(f"✅ Total models loaded: {len(loaded_models)}")
        
        # Check each expected model
        expected_models = ['xgboost', 'lstm_gru', 'transformer', 'dqn']
        for model_name in expected_models:
            if model_name in loaded_models:
                print(f"✅ {model_name}: LOADED")
            else:
                print(f"❌ {model_name}: NOT LOADED")
        
        # Create ensemble
        print("\nCreating ensemble...")
        manager.create_ensemble()
        
        # Check ensemble
        if hasattr(manager, 'ensemble') and manager.ensemble:
            print(f"✅ Ensemble created with {len(manager.ensemble.models)} models")
            if hasattr(manager.ensemble, 'model_types'):
                print(f"   Model types: {manager.ensemble.model_types}")
            if hasattr(manager.ensemble, 'weights'):
                print(f"   Weights: {[f'{w:.3f}' for w in manager.ensemble.weights]}")
        else:
            print("❌ Ensemble not created")
        
        # Test model weights generation
        print("\nTesting model weights generation...")
        ensemble_weights = {}
        
        # Get weights from ensemble if available
        if hasattr(manager, 'ensemble') and manager.ensemble:
            if hasattr(manager.ensemble, 'model_types') and hasattr(manager.ensemble, 'weights'):
                for model_type, weight in zip(manager.ensemble.model_types, manager.ensemble.weights):
                    ensemble_weights[model_type] = weight
        
        # Add weights for all loaded models
        for model_name in manager.models.keys():
            if model_name not in ensemble_weights:
                if model_name == 'xgboost':
                    ensemble_weights[model_name] = 0.4
                elif model_name == 'lstm_gru':
                    ensemble_weights[model_name] = 0.25
                elif model_name == 'transformer':
                    ensemble_weights[model_name] = 0.25
                elif model_name == 'dqn':
                    ensemble_weights[model_name] = 0.1
                else:
                    ensemble_weights[model_name] = 0.1
        
        print(f"✅ Generated weights for {len(ensemble_weights)} models:")
        for model_name, weight in ensemble_weights.items():
            print(f"   {model_name}: {weight:.3f}")
            
        return manager, ensemble_weights
        
    except Exception as e:
        print(f"❌ Error testing ensemble models: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_ui_integration():
    """Test UI integration with ensemble models"""
    print("\n🔍 Testing UI Integration...")
    
    try:
        # Import trading UI class
        from trading_ui import TradingUI
        
        # Create a mock UI instance (without showing the window)
        ui = TradingUI()
        
        # Initialize ensemble model manager
        from Models.model_manager import ModelManager
        ui.ensemble_model_manager = ModelManager(model_dir='models')
        ui.ensemble_model_manager.load_models()
        ui.ensemble_model_manager.create_ensemble()
        
        # Test the get_ensemble_analytics method
        analytics = ui.get_ensemble_analytics()
        
        print(f"✅ Generated analytics for {len(analytics.get('models', {}))} models")
        for model_name, metrics in analytics.get('models', {}).items():
            print(f"   {model_name}: accuracy={metrics.get('accuracy', 0):.3f}")
        
        # Test model weights generation
        ui.model_weights = {}
        ensemble_weights = {}
        
        # Get weights from loaded models
        if hasattr(ui.ensemble_model_manager, 'models'):
            for model_name in ui.ensemble_model_manager.models.keys():
                if model_name == 'xgboost':
                    ensemble_weights[model_name] = 0.4
                elif model_name == 'lstm_gru':
                    ensemble_weights[model_name] = 0.25
                elif model_name == 'transformer':
                    ensemble_weights[model_name] = 0.25
                elif model_name == 'dqn':
                    ensemble_weights[model_name] = 0.1
                else:
                    ensemble_weights[model_name] = 0.1
        
        ui.model_weights = {
            **ensemble_weights,
            'data_source': 'ensemble_model_performance',
            'total_models': len(ensemble_weights),
            'timestamp': '2025-01-06T12:00:00'
        }
        
        print(f"✅ UI model weights: {len(ensemble_weights)} models")
        for model_name, weight in ensemble_weights.items():
            print(f"   {model_name}: {weight:.3f}")
            
        return ui
        
    except Exception as e:
        print(f"❌ Error testing UI integration: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 Starting Ensemble Model Tests...\n")
    
    # Test 1: Ensemble model loading
    manager, weights = test_ensemble_models()
    
    # Test 2: UI integration
    ui = test_ui_integration()
    
    print("\n✅ All tests completed!")
    
    if manager and weights and ui:
        print(f"✅ SUCCESS: {len(weights)} models loaded and integrated with UI")
    else:
        print("❌ FAILURE: Some tests failed")
