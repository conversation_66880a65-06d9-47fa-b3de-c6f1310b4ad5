@echo off
color 0A
cls
echo ===============================================================
echo                    QUOTEX DATA MANAGER
echo ===============================================================
echo.
echo Starting Quotex Data Manager with all features:
echo.
echo  - Server Connection (connect, test connection)
echo  - Balance Management (get balance, refill demo balance)
echo  - Purchase Operations (buy, check win, multiple buys)
echo  - Profile and Asset Data (profile, assets, payout)
echo  - Candlestick Data (historical and live candles)
echo  - Option Selling (sell options before expiration)
echo  - Data Analysis (analyze candle data files)
echo.
echo ===============================================================
echo.
echo Connecting to Quotex server...
echo.
python quotex_data_manager.py
echo.
echo ===============================================================
echo Program finished. Press any key to exit.
echo ===============================================================
pause > nul
