#!/usr/bin/env python
"""
TensorFlow Installation Check Module

This module checks if TensorFlow is installed and properly configured.
It also provides functions to install TensorFlow if it's not available.
"""

import os
import sys
import subprocess
import platform
import importlib.util
from datetime import datetime

def is_tensorflow_available():
    """
    Check if TensorFlow is available in the current environment

    Returns:
    - bool: True if TensorFlow is available, False otherwise
    """
    try:
        # Check if Tensor<PERSON>low is installed
        tf_spec = importlib.util.find_spec("tensorflow")
        if tf_spec is None:
            print("TensorFlow is not installed")
            return False

        # Try to import TensorFlow
        import tensorflow as tf

        # Check TensorFlow version
        print(f"TensorFlow version: {tf.__version__}")

        # Check for GPU support
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"TensorFlow is using GPU: {len(gpus)} GPU(s) available")
            for gpu in gpus:
                print(f"  - {gpu.name}")
        else:
            print("TensorFlow is using CPU only")

        # Try a simple TensorFlow operation to verify it works
        a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
        b = tf.constant([[5.0, 6.0], [7.0, 8.0]])
        c = tf.matmul(a, b)

        print("TensorFlow test operation successful")
        return True

    except ImportError:
        print("TensorFlow import failed")
        return False
    except Exception as e:
        print(f"TensorFlow test failed: {e}")
        return False

def install_tensorflow():
    """
    Attempt to install TensorFlow using pip

    Returns:
    - bool: True if installation was successful, False otherwise
    """
    try:
        print("Attempting to install TensorFlow...")

        # Check Python version
        python_version = platform.python_version()
        print(f"Python version: {python_version}")

        # Check if pip is available
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "--version"])
        except subprocess.CalledProcessError:
            print("pip is not available. Cannot install TensorFlow.")
            return False

        # Install TensorFlow
        print("Installing TensorFlow...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "tensorflow"])

        # Check if installation was successful
        if is_tensorflow_available():
            print("TensorFlow installation successful")
            return True
        else:
            print("TensorFlow installation failed")
            return False

    except Exception as e:
        print(f"Error installing TensorFlow: {e}")
        return False

def setup_tensorflow(required=False):
    """
    Check if TensorFlow is available and install it if needed

    Parameters:
    - required: Whether TensorFlow is required for the application to run

    Returns:
    - bool: True if TensorFlow is available or was successfully installed, False otherwise
    """
    print("\n" + "=" * 50)
    print("TENSORFLOW SETUP")
    print("=" * 50)

    # Check if TensorFlow is already available
    tf_available = is_tensorflow_available()

    if not tf_available:
        print("\nTensorFlow is not available.")

        # Check Python version
        python_version = platform.python_version()
        python_major, python_minor, _ = map(int, python_version.split('.'))

        if python_major == 3 and python_minor >= 11:
            print(f"You are using Python {python_version}, which may not be compatible with TensorFlow.")
            print("TensorFlow currently supports Python 3.7-3.10 for most versions.")
            print("The system will continue with XGBoost only.")

            if required:
                print("\nWARNING: TensorFlow was marked as required but cannot be installed.")
                print("The system will attempt to function with XGBoost only.")
                print("For full functionality, consider using Python 3.8-3.10.")

            # Don't attempt to install TensorFlow on incompatible Python versions
            return False

        # For compatible Python versions, ask about installation
        if required:
            print("TensorFlow is required for this application.")
            install_choice = input("Do you want to install TensorFlow now? (y/n): ").strip().lower()
        else:
            print("TensorFlow is recommended for advanced features but not required.")
            install_choice = input("Do you want to install TensorFlow for advanced features? (y/n): ").strip().lower()

        if install_choice == 'y':
            tf_available = install_tensorflow()

            if tf_available:
                print("\nTensorFlow is now available.")
            else:
                print("\nFailed to install TensorFlow.")

                if required:
                    print("TensorFlow is required but could not be installed.")
                    print("The application may not function correctly.")
                    return False
                else:
                    print("The application will run with limited functionality.")
        else:
            print("\nSkipping TensorFlow installation.")

            if required:
                print("TensorFlow is required but will not be installed.")
                print("The application may not function correctly.")
                return False
            else:
                print("The application will run with limited functionality.")
    else:
        print("\nTensorFlow is already installed and working correctly.")

    return tf_available

if __name__ == "__main__":
    # Run the setup function when the script is executed directly
    setup_tensorflow()
