"""
Create simple models for auto_trader.py to use
"""

import os
import numpy as np
import pandas as pd
import joblib
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import RandomForestClassifier
import xgboost as xgb

# Create directories
os.makedirs('models', exist_ok=True)
os.makedirs('models/snapshots', exist_ok=True)

print("Creating simple models for auto_trader.py...")

# Create a simple dataset
np.random.seed(42)
n_samples = 1000
n_features = 229  # Match the expected feature count

# Create random features
X = np.random.randn(n_samples, n_features)
# Create random binary target (0 or 1)
y = np.random.randint(0, 2, size=n_samples)

# Create feature names
feature_names = [f'feature_{i}' for i in range(n_features)]

# Split into train and test
train_size = int(0.8 * n_samples)
X_train, X_test = X[:train_size], X[train_size:]
y_train, y_test = y[:train_size], y[train_size:]

# Create a scaler
scaler = RobustScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Create and save LSTM-GRU model
print("Creating LSTM-GRU model...")
# Reshape data for LSTM [samples, time steps, features]
X_train_lstm = X_train_scaled.reshape(X_train_scaled.shape[0], 1, X_train_scaled.shape[1])
X_test_lstm = X_test_scaled.reshape(X_test_scaled.shape[0], 1, X_test_scaled.shape[1])

# Create LSTM model
lstm_model = Sequential([
    LSTM(64, input_shape=(1, n_features), return_sequences=True),
    Dropout(0.2),
    LSTM(32),
    Dropout(0.2),
    Dense(16, activation='relu'),
    Dense(1, activation='sigmoid')
])

# Compile model
lstm_model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

# Train model
lstm_model.fit(X_train_lstm, y_train, epochs=5, batch_size=32, verbose=1, validation_data=(X_test_lstm, y_test))

# Save model
timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
lstm_model_path = f'models/lstm_gru_transformer_{timestamp}.h5'
lstm_model.save(lstm_model_path)
print(f"LSTM-GRU model saved to {lstm_model_path}")

# Save feature names
with open(f'models/lstm_gru_transformer_{timestamp}_features.txt', 'w') as f:
    f.write('\n'.join(feature_names))

# Save scaler
scaler_path = f'models/lstm_gru_transformer_{timestamp}_scaler.pkl'
joblib.dump(scaler, scaler_path)
print(f"Scaler saved to {scaler_path}")

# Create and save XGBoost model
print("\nCreating XGBoost model...")
xgb_model = xgb.XGBClassifier(n_estimators=100, max_depth=3, learning_rate=0.1)
xgb_model.fit(X_train_scaled, y_train)

# Save XGBoost model
xgb_model_path = f'models/xgboost_model_{timestamp}.pkl'
joblib.dump(xgb_model, xgb_model_path)
print(f"XGBoost model saved to {xgb_model_path}")

# Save XGBoost feature names
with open(f'models/xgboost_selected_features_{timestamp}.pkl', 'wb') as f:
    joblib.dump(feature_names, f)

print("\nModels created successfully!")
print("You can now run auto_trader.py and select these models.")
