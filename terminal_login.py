#!/usr/bin/env python
"""
Terminal-based login for Quotex API
This script handles API verification and login through the terminal
"""

import os
import sys
import time
import json
import asyncio
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
except ImportError:
    print("Error: quotexapi module not found")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

def read_credentials_from_config():
    """Read credentials from config.ini file"""
    import configparser

    # Create config parser with no interpolation to handle special characters in passwords
    config = configparser.ConfigParser(interpolation=None)

    # Check if config.ini exists
    if os.path.exists('config.ini'):
        try:
            # Try to read config file
            config.read('config.ini')

            # Check if credentials section exists
            if 'credentials' in config:
                # Get credentials
                email = config['credentials'].get('email', '')
                password = config['credentials'].get('password', '')

                if email and password:
                    print(f"Loaded credentials from config.ini for: {email}")
                    return email, password
        except configparser.MissingSectionHeaderError:
            # Handle old format config.ini without section headers
            print("Config file has incorrect format. Trying to read in legacy format...")
            try:
                email = ""
                password = ""
                with open('config.ini', 'r') as f:
                    for line in f:
                        if line.startswith('email='):
                            email = line.strip().split('=', 1)[1]
                        elif line.startswith('password='):
                            password = line.strip().split('=', 1)[1]

                if email and password:
                    print(f"Loaded credentials from legacy config.ini for: {email}")
                    # Convert to new format
                    save_credentials_to_config(email, password)
                    return email, password
            except Exception as e:
                print(f"Error reading legacy config file: {e}")
        except Exception as e:
            print(f"Error reading config file: {e}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = getpass("Enter your Quotex password: ")

    # Save credentials to config.ini
    if email and password:
        save_credentials_to_config(email, password)

    return email, password

def save_credentials_to_config(email, password):
    """Save credentials to config.ini file"""
    import configparser

    # Create config parser with no interpolation to handle special characters in passwords
    config = configparser.ConfigParser(interpolation=None)

    # Check if config.ini exists and try to read it
    if os.path.exists('config.ini'):
        try:
            # Try to read config file
            config.read('config.ini')
        except Exception as e:
            print(f"Warning: Could not read existing config.ini: {e}")
            # If there's an error, we'll create a new config file
            # First, backup the old one
            try:
                import shutil
                backup_file = 'config.ini.bak'
                shutil.copy2('config.ini', backup_file)
                print(f"Backed up old config file to {backup_file}")
            except Exception as backup_error:
                print(f"Warning: Could not backup old config file: {backup_error}")

    # Ensure credentials section exists
    if 'credentials' not in config:
        config['credentials'] = {}

    # Set credentials
    config['credentials']['email'] = email
    config['credentials']['password'] = password

    # Write config file
    try:
        with open('config.ini', 'w') as f:
            config.write(f)
        print("Credentials saved to config.ini")
    except Exception as e:
        print(f"Error saving credentials to config.ini: {e}")
        # Try to save to a different file as a fallback
        try:
            with open('config.ini.new', 'w') as f:
                config.write(f)
            print("Credentials saved to config.ini.new")
        except Exception as fallback_error:
            print(f"Error saving credentials to fallback file: {fallback_error}")

def getpass(prompt):
    """Get password from user without echoing"""
    import getpass
    return getpass.getpass(prompt)

def clear_cached_sessions():
    """Clear any cached session data to force fresh login"""
    print("🧹 Clearing cached session data...")

    # List of potential cache files and directories to clear
    cache_items = [
        'quotex_session.json',
        'quotex_cache.json',
        'session_data.json',
        '.quotex_cache',
        '__pycache__',
        'quotex_cookies.txt',
        'session.dat',
        'auth_token.json',
        'quotex_login_state.json',
        'last_pin_request.json'
    ]

    cleared_count = 0

    for item in cache_items:
        try:
            if os.path.isfile(item):
                os.remove(item)
                print(f"   ✅ Removed cache file: {item}")
                cleared_count += 1
            elif os.path.isdir(item):
                import shutil
                shutil.rmtree(item)
                print(f"   ✅ Removed cache directory: {item}")
                cleared_count += 1
        except Exception as e:
            # Silently ignore errors - cache clearing is best effort
            pass

    # Clear environment variables that might cache session data
    env_vars_to_clear = [
        'QUOTEX_SESSION_TOKEN',
        'QUOTEX_AUTH_TOKEN',
        'QUOTEX_SESSION_ID',
        'QUOTEX_CACHED_LOGIN',
        'QUOTEX_LAST_PIN_TIME'
    ]

    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
            print(f"   ✅ Cleared environment variable: {var}")
            cleared_count += 1

    if cleared_count > 0:
        print(f"🔄 Cleared {cleared_count} cached items for fresh login")
    else:
        print("✨ No cached session data found - starting fresh")

    print("🔐 Ready for fresh PIN verification")

def force_new_pin_request():
    """Force a completely new PIN request by clearing all session data"""
    print("🔄 Forcing fresh PIN request...")

    # Clear any global client references
    global _global_quotex_client
    if _global_quotex_client:
        try:
            # Try to disconnect if possible
            if hasattr(_global_quotex_client, 'close'):
                _global_quotex_client.close()
            elif hasattr(_global_quotex_client, 'disconnect'):
                _global_quotex_client.disconnect()
        except:
            pass
        _global_quotex_client = None
        print("   ✅ Cleared global client reference")

    # Clear session data
    clear_cached_sessions()

    # Add a small delay to ensure clean state
    import time
    time.sleep(1)

    print("🔐 Ready for completely fresh PIN request")

def get_pin_with_retry(max_attempts=3, email=None, password=None):
    """Get PIN from user with retry option"""
    for attempt in range(1, max_attempts + 1):
        # Show options
        print(f"\nPIN Entry Attempt {attempt}/{max_attempts}")
        print("Options:")
        print("1. Enter PIN")
        print("2. Request new PIN")
        print("3. Abort")

        choice = input("Choose an option (1-3): ")

        if choice == '1':
            # Get PIN
            pin = input("Enter the PIN from your email: ")
            if pin:
                return pin
            print("No PIN entered. Please try again.")
        elif choice == '2' and email and password:
            # Create an event loop for the PIN request
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Request new PIN
            print("\nRequesting a new PIN...")
            result = loop.run_until_complete(request_pin_directly(email, password))

            # Close the loop
            loop.close()

            if result:
                print("PIN request sent. Please check your email for the new PIN.")
            else:
                print("Failed to request new PIN.")
        elif choice == '3':
            print("Aborting PIN entry.")
            return None
        else:
            print("Invalid choice. Please try again.")

    print(f"Maximum attempts ({max_attempts}) reached. Aborting.")
    return None

def request_new_pin(email, password):
    """Request a new PIN from Quotex API"""
    print(f"Requesting new PIN for {email}...")

    try:
        # Try to use quotex_pin_helper if available
        try:
            import quotex_pin_helper
            print("Using quotex_pin_helper for PIN request...")

            # Create PIN helper
            helper = quotex_pin_helper.QuotexPINHelper(email, password)

            # Request PIN
            print("Requesting PIN...")
            success, message = helper.request_pin()

            if success:
                print("PIN request successful!")
                return True
            else:
                print(f"PIN request failed: {message}")
                return False
        except ImportError:
            print("quotex_pin_helper not available.")
        except Exception as helper_error:
            print(f"Error using PIN helper: {helper_error}")

        # Fall back to manual instructions
        print("\nTo request a new PIN:")
        print("1. Go to the Quotex website (https://quotex.io/)")
        print("2. Click on 'Login'")
        print("3. Enter your email and password")
        print("4. Click 'Forgot PIN' or similar option")
        print("5. Check your email for the new PIN\n")

        return False
    except Exception as e:
        print(f"Error requesting new PIN: {e}")
        return False

async def request_pin_directly(email, password):
    """Request a PIN directly from Quotex API with multiple methods"""
    print(f"\n=== Requesting PIN for {email} ===")

    try:
        # Create a new client for PIN request
        print("Creating Quotex client for PIN request...")
        pin_client = Quotex(email, password)

        # First, let's check what methods are available
        print("🔍 Checking available PIN-related methods...")
        available_methods = [method for method in dir(pin_client) if not method.startswith('_')]
        pin_methods = [method for method in available_methods if 'pin' in method.lower() or 'verify' in method.lower() or 'auth' in method.lower()]

        if pin_methods:
            print(f"📋 Found PIN-related methods: {', '.join(pin_methods)}")
        else:
            print("⚠️ No obvious PIN-related methods found")

        # Try multiple PIN request strategies
        pin_requested = False

        # Strategy 1: Direct PIN request methods
        for method_name in ['request_pin', 'send_pin_request', 'resend_pin', 'request_verification', 'send_verification_code']:
            if hasattr(pin_client, method_name):
                print(f"🔑 Trying {method_name} method...")
                try:
                    method = getattr(pin_client, method_name)
                    result = await method()
                    print(f"✅ {method_name} result: {result}")
                    pin_requested = True
                    break
                except Exception as method_error:
                    print(f"❌ {method_name} failed: {method_error}")
                    continue

        # Strategy 2: Try to trigger PIN by attempting login
        if not pin_requested:
            print("🔄 Attempting to trigger PIN request through login attempt...")
            try:
                # Mock input to capture PIN if provided during connection
                import builtins
                original_input = builtins.input
                captured_pin = None

                def capture_pin_input(prompt):
                    nonlocal captured_pin
                    print(f"PIN prompt detected: {prompt}")
                    # Let the user enter the PIN normally
                    pin_value = original_input(prompt)
                    captured_pin = pin_value
                    return pin_value

                # Replace input temporarily
                builtins.input = capture_pin_input

                # Try to connect which should trigger PIN request
                connect_result = await asyncio.wait_for(pin_client.connect(), timeout=15)

                # Restore original input
                builtins.input = original_input

                if connect_result:
                    print("✅ Connection successful with PIN!")
                    if captured_pin:
                        print(f"📝 PIN captured: {captured_pin}")
                        # Store the PIN for later use
                        os.environ['QUOTEX_CAPTURED_PIN'] = captured_pin
                    return True
                else:
                    print("⚠️ Connection failed - PIN request may have been triggered")
                    pin_requested = True
            except asyncio.TimeoutError:
                # Restore original input
                builtins.input = original_input
                print("⏰ Connection timed out - PIN request likely triggered")
                pin_requested = True
            except Exception as connect_error:
                # Restore original input
                builtins.input = original_input
                error_msg = str(connect_error).lower()
                if any(keyword in error_msg for keyword in ['pin', 'verification', 'code', 'auth']):
                    print("✅ PIN verification required - PIN request triggered successfully")
                    pin_requested = True
                else:
                    print(f"❌ Connection error: {connect_error}")

        # Strategy 3: Use HTTP request to Quotex login endpoint (if available)
        if not pin_requested:
            print("🌐 Attempting HTTP-based PIN request...")
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    # Try to make a login request to trigger PIN
                    login_data = {
                        'email': email,
                        'password': password,
                        'action': 'login'
                    }

                    # Common Quotex endpoints that might trigger PIN
                    endpoints = [
                        'https://quotex.io/api/v1/auth/login',
                        'https://qxbroker.com/api/v1/auth/login',
                        'https://quotex.io/login',
                    ]

                    for endpoint in endpoints:
                        try:
                            async with session.post(endpoint, json=login_data, timeout=5) as response:
                                if response.status in [200, 400, 401, 403]:  # Any response means endpoint exists
                                    print(f"✅ HTTP PIN request sent to {endpoint}")
                                    pin_requested = True
                                    break
                        except Exception:
                            continue

            except ImportError:
                print("⚠️ aiohttp not available for HTTP PIN request")
            except Exception as http_error:
                print(f"⚠️ HTTP PIN request failed: {http_error}")

        if pin_requested:
            print("✅ PIN request completed using one or more methods")
            print("📧 Please check your email (including spam folder) for the PIN")
            return True
        else:
            print("❌ All PIN request methods failed")
            print("💡 You may need to manually request a PIN from the Quotex website")
            return False

    except Exception as e:
        print(f"❌ Error requesting PIN: {e}")
        print("💡 Try manually requesting a PIN from https://quotex.io/")
        return False

# Global variable to store the connected client
_global_quotex_client = None

async def verify_api_and_login():
    """Verify API and login through terminal - Smart login with session management"""
    global _global_quotex_client

    print("=== Quotex API Verification and Login ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Check if we already have a valid session
    if _global_quotex_client:
        print("🔄 Checking existing session...")
        try:
            # Test if the existing client is still connected
            if hasattr(_global_quotex_client, 'check_connect'):
                is_connected = await _global_quotex_client.check_connect()
                if is_connected:
                    print("✅ Existing session is still valid - reusing connection")
                    return True, _global_quotex_client
            print("⚠️ Existing session expired - creating new session")
        except Exception as e:
            print(f"⚠️ Session check failed: {e} - creating new session")

    print("🔐 SINGLE LOGIN MODE: PIN verification required once per session")

    # Only clear cache if we don't have a valid session
    clear_cached_sessions()

    # Read credentials
    email, password = read_credentials_from_config()

    if not email or not password:
        print("No credentials provided. Exiting.")
        return False

    # ALWAYS request a fresh PIN - no option to skip
    print(f"\n🔑 Fresh PIN verification required for: {email}")
    print("📧 Requesting fresh PIN from Quotex...")

    # Create client and attempt direct connection with PIN handling
    print("\n🔄 Creating Quotex client session...")
    client = Quotex(email, password)

    print("🔐 Connecting to Quotex API...")
    print("📧 When prompted, please enter the PIN from your email")

    try:
        # Try to connect - this will prompt for PIN if needed
        connected = await client.connect()

        if connected:
            print("✅ Successfully connected to Quotex API!")
            connection_successful = True
        else:
            print("❌ Failed to connect to Quotex API")
            return False

    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

    # Get assets after successful connection
    print("Fetching assets...")
    assets = None

    # Try different methods that might exist in the API
    try:
        # First try to get available methods
        print("Available methods:")
        methods = [method for method in dir(client) if not method.startswith('_') and not method.startswith('__')]
        print(", ".join(methods))

        # Try different method names that might exist
        if hasattr(client, 'get_all_asset'):
            print("Trying get_all_asset method...")
            assets = await client.get_all_asset()
        elif hasattr(client, 'get_all_assets'):
            print("Trying get_all_assets method...")
            assets = await client.get_all_assets()
        elif hasattr(client, 'get_assets'):
            print("Trying get_assets method...")
            assets = await client.get_assets()
        elif hasattr(client, 'get_asset_list'):
            print("Trying get_asset_list method...")
            assets = await client.get_asset_list()
        elif hasattr(client, 'get_all_asset_name'):
            print("Trying get_all_asset_name method...")
            assets = client.get_all_asset_name()
        else:
            print("Could not find a method to get assets.")
            # Consider connection successful even without assets
            assets = {}
    except Exception as asset_error:
        print(f"Error getting assets: {asset_error}")
        # Consider connection successful even if asset fetch fails
        assets = {}

    if assets:
        print(f"Retrieved {len(assets)} assets")

        # Save assets to file for later use
        try:
            with open('quotex_assets.json', 'w') as f:
                json.dump(assets, f, indent=4)
            print("Saved assets to quotex_assets.json")
        except Exception as save_error:
            print(f"Error saving assets: {save_error}")
    else:
        print("No assets retrieved, but connection was successful.")

    # Keep connection alive for UI
    print("Connection maintained for UI - NOT disconnecting")

    # Store the client globally
    _global_quotex_client = client

    # Return success and the client object
    return connection_successful, client

def get_global_client():
    """Get the global Quotex client if available"""
    return _global_quotex_client

def terminal_login():
    """Terminal login function that can be called from other scripts"""
    global _global_quotex_client

    try:
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Run the verification function
        result = loop.run_until_complete(verify_api_and_login())

        # Close the loop
        loop.close()

        # Handle the new return format
        if isinstance(result, tuple):
            success, client = result
            if success and client:
                # Store the client globally
                _global_quotex_client = client
                print("Quotex client stored globally for UI access")
            return success
        else:
            # Old format (just boolean)
            return result
    except KeyboardInterrupt:
        print("\nLogin interrupted by user")
        return False
    except Exception as e:
        print(f"Error in login: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run the terminal login
    success = terminal_login()

    if success:
        print("Login successful! You can now start the trading UI.")
        sys.exit(0)
    else:
        print("Login failed. Please try again.")
        sys.exit(1)
