"""
TensorFlow Configuration Module

This module configures TensorFlow to use both CPU and GPU resources efficiently.
It sets memory growth, enables mixed precision training, and configures thread settings.
"""

import os
import tensorflow as tf
import numpy as np
from datetime import datetime

def configure_tensorflow(memory_limit=None, use_mixed_precision=True, log_device_placement=False):
    """
    Configure TensorFlow to use both CPU and GPU resources efficiently

    Parameters:
    - memory_limit: GPU memory limit in MB (None = no limit)
    - use_mixed_precision: Whether to use mixed precision training
    - log_device_placement: Whether to log device placement

    Returns:
    - config_info: Dictionary with configuration information
    """
    print("\n" + "=" * 50)
    print("TENSORFLOW CONFIGURATION")
    print("=" * 50)

    # Start time for benchmarking
    start_time = datetime.now()

    # Set random seeds for reproducibility
    np.random.seed(42)
    tf.random.set_seed(42)

    # Get TensorFlow version
    tf_version = tf.__version__
    print(f"TensorFlow version: {tf_version}")

    # Configure logging
    tf.get_logger().setLevel('INFO')

    # Configure device placement logging
    tf.debugging.set_log_device_placement(log_device_placement)

    # Check for GPU availability
    gpus = tf.config.list_physical_devices('GPU')
    cpus = tf.config.list_physical_devices('CPU')

    config_info = {
        'tf_version': tf_version,
        'gpus_available': len(gpus),
        'cpus_available': len(cpus),
        'mixed_precision_enabled': False,
        'memory_growth_enabled': False,
        'memory_limit_set': False,
        'xla_enabled': False
    }

    # Configure GPUs if available
    if gpus:
        print(f"Found {len(gpus)} GPU(s):")
        for i, gpu in enumerate(gpus):
            print(f"  - GPU {i}: {gpu.name}")

        try:
            # Enable memory growth to avoid allocating all GPU memory at once
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            config_info['memory_growth_enabled'] = True
            print("Memory growth enabled for all GPUs")

            # Set memory limit if specified
            if memory_limit is not None:
                tf.config.set_logical_device_configuration(
                    gpus[0],
                    [tf.config.LogicalDeviceConfiguration(memory_limit=memory_limit)]
                )
                config_info['memory_limit_set'] = True
                print(f"GPU memory limit set to {memory_limit} MB")
        except RuntimeError as e:
            print(f"Error configuring GPU: {e}")
    else:
        print("No GPU found. Using CPU only.")

    # Configure CPU threads - use os.cpu_count() which works on all platforms
    try:
        # Try platform-specific method first (Linux)
        if hasattr(os, 'sched_getaffinity'):
            cpu_count = len(os.sched_getaffinity(0))
        else:
            # Fall back to os.cpu_count() for Windows and other platforms
            cpu_count = os.cpu_count() or 4
    except Exception:
        # Default to 4 if all methods fail
        cpu_count = 4

    print(f"\nCPU threads available: {cpu_count}")

    # Set inter/intra op parallelism threads
    tf.config.threading.set_inter_op_parallelism_threads(cpu_count)
    tf.config.threading.set_intra_op_parallelism_threads(cpu_count)
    print(f"Inter/intra op parallelism threads set to {cpu_count}")

    # Enable mixed precision if requested
    if use_mixed_precision:
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        config_info['mixed_precision_enabled'] = True
        print("\nMixed precision training enabled (float16)")

    # Enable XLA (Accelerated Linear Algebra)
    try:
        tf.config.optimizer.set_jit(True)
        config_info['xla_enabled'] = True
        print("XLA (Accelerated Linear Algebra) enabled")
    except:
        print("Could not enable XLA")

    # Set up distribution strategy
    strategy = None
    if gpus:
        try:
            # Use MirroredStrategy for multiple GPUs
            if len(gpus) > 1:
                strategy = tf.distribute.MirroredStrategy()
                print(f"\nMirroredStrategy enabled with {strategy.num_replicas_in_sync} devices")
            else:
                # Use default strategy for single GPU
                strategy = tf.distribute.get_strategy()
                print("\nDefault strategy enabled for single GPU")
        except:
            print("\nCould not set up GPU distribution strategy")
    else:
        # Use default strategy for CPU
        strategy = tf.distribute.get_strategy()
        print("\nDefault strategy enabled for CPU")

    config_info['strategy'] = strategy

    # Run a simple test to verify configuration
    print("\nRunning test computation...")
    with tf.device('/CPU:0'):
        a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
        b = tf.constant([[5.0, 6.0], [7.0, 8.0]])
        c = tf.matmul(a, b)
        print(f"Test result: {c.numpy()}")

    # Calculate configuration time
    config_time = (datetime.now() - start_time).total_seconds()
    print(f"\nTensorFlow configuration completed in {config_time:.2f} seconds")

    return config_info

def get_model_compile_options(use_xla=True, use_auto_mixed_precision=True):
    """
    Get model compilation options for optimal performance

    Parameters:
    - use_xla: Whether to use XLA compilation
    - use_auto_mixed_precision: Whether to use auto mixed precision

    Returns:
    - options: Dictionary with compilation options
    """
    options = {}

    if use_xla:
        options['jit_compile'] = True

    if use_auto_mixed_precision:
        options['auto_mixed_precision'] = True

    return options

# If this script is run directly, configure TensorFlow
if __name__ == "__main__":
    config_info = configure_tensorflow()
    print("\nTensorFlow is configured and ready to use.")
