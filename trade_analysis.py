#!/usr/bin/env python
"""
Trade Analysis Module

This module provides comprehensive pre-trade analysis to increase prediction accuracy
before executing trades. It analyzes market conditions, technical indicators, and
pattern recognition to determine if a trade should be taken.
"""

import numpy as np
import pandas as pd
from datetime import datetime
import time
import os
import joblib
from scipy import stats

# Technical analysis libraries
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("TALib not available. Using alternative implementations.")

# Constants for analysis
OVERBOUGHT_RSI = 70
OVERSOLD_RSI = 30
STRONG_TREND_ADX = 25
VERY_STRONG_TREND_ADX = 40
HIGH_VOLATILITY = 0.02  # 2% standard deviation in returns
LOW_VOLATILITY = 0.005  # 0.5% standard deviation in returns

def analyze_trade_opportunity(candles, prediction, min_confidence=0.65):
    """
    Perform comprehensive analysis to determine if a trade should be taken

    Parameters:
    - candles: List of candle data
    - prediction: Dictionary containing model prediction
    - min_confidence: Minimum confidence threshold

    Returns:
    - take_trade: <PERSON><PERSON><PERSON> indicating whether to take the trade
    - analysis: Dictionary with detailed analysis
    - adjusted_confidence: Adjusted confidence level
    """
    # Convert candles to DataFrame
    df = pd.DataFrame(candles)

    # Ensure we have enough data
    if len(df) < 30:
        return False, {"error": "Not enough candles for analysis"}, 0

    # Extract prediction details
    direction = prediction.get('direction')
    confidence = prediction.get('confidence', 0)
    probability = prediction.get('probability', 0.5)

    if direction is None or confidence is None:
        return False, {"error": "Invalid prediction data"}, 0

    # Initialize analysis result
    analysis = {
        'timestamp': datetime.now().isoformat(),
        'direction': direction,
        'base_confidence': confidence,
        'indicators': {},
        'patterns': {},
        'market_conditions': {},
        'confirmations': [],
        'warnings': [],
        'take_trade': False
    }

    # Calculate basic price metrics
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    df['range'] = df['high'] - df['low']
    df['body'] = abs(df['close'] - df['open'])
    df['color'] = np.where(df['close'] >= df['open'], 'green', 'red')

    # Calculate moving averages
    df['sma_5'] = df['close'].rolling(window=5).mean()
    df['sma_10'] = df['close'].rolling(window=10).mean()
    df['sma_20'] = df['close'].rolling(window=20).mean()
    df['sma_50'] = df['close'].rolling(window=50).mean() if len(df) >= 50 else np.nan
    df['ema_5'] = df['close'].ewm(span=5, adjust=False).mean()
    df['ema_10'] = df['close'].ewm(span=10, adjust=False).mean()
    df['ema_20'] = df['close'].ewm(span=20, adjust=False).mean()

    # Calculate RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi_14'] = 100 - (100 / (1 + rs))

    # Calculate MACD
    df['ema_12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['ema_26'] = df['close'].ewm(span=26, adjust=False).mean()
    df['macd'] = df['ema_12'] - df['ema_26']
    df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
    df['macd_hist'] = df['macd'] - df['macd_signal']

    # Calculate Bollinger Bands
    df['bb_middle'] = df['close'].rolling(window=20).mean()
    std_dev = df['close'].rolling(window=20).std()
    df['bb_upper'] = df['bb_middle'] + (std_dev * 2)
    df['bb_lower'] = df['bb_middle'] - (std_dev * 2)
    df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']

    # Calculate ADX for trend strength
    df['tr1'] = abs(df['high'] - df['low'])
    df['tr2'] = abs(df['high'] - df['close'].shift(1))
    df['tr3'] = abs(df['low'] - df['close'].shift(1))
    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    df['atr_14'] = df['true_range'].rolling(window=14).mean()

    # Calculate +DI and -DI
    df['up_move'] = df['high'].diff()
    df['down_move'] = -df['low'].diff()

    df['plus_dm'] = np.where((df['up_move'] > df['down_move']) & (df['up_move'] > 0), df['up_move'], 0)
    df['minus_dm'] = np.where((df['down_move'] > df['up_move']) & (df['down_move'] > 0), df['down_move'], 0)

    # Smoothed True Range and Directional Movement
    df['smoothed_tr'] = df['true_range'].rolling(window=14).sum()
    df['smoothed_plus_dm'] = df['plus_dm'].rolling(window=14).sum()
    df['smoothed_minus_dm'] = df['minus_dm'].rolling(window=14).sum()

    # Directional Indicators
    df['plus_di_14'] = 100 * df['smoothed_plus_dm'] / df['smoothed_tr']
    df['minus_di_14'] = 100 * df['smoothed_minus_dm'] / df['smoothed_tr']

    # Directional Index
    df['dx'] = 100 * abs(df['plus_di_14'] - df['minus_di_14']) / (df['plus_di_14'] + df['minus_di_14'])

    # Average Directional Index
    df['adx_14'] = df['dx'].rolling(window=14).mean()

    # Get latest values
    last_close = df['close'].iloc[-1]
    last_open = df['open'].iloc[-1]
    last_high = df['high'].iloc[-1]
    last_low = df['low'].iloc[-1]
    last_color = df['color'].iloc[-1]

    sma_5 = df['sma_5'].iloc[-1]
    sma_10 = df['sma_10'].iloc[-1]
    sma_20 = df['sma_20'].iloc[-1]
    sma_50 = df['sma_50'].iloc[-1] if not pd.isna(df['sma_50'].iloc[-1]) else sma_20

    ema_5 = df['ema_5'].iloc[-1]
    ema_10 = df['ema_10'].iloc[-1]
    ema_20 = df['ema_20'].iloc[-1]

    rsi = df['rsi_14'].iloc[-1]
    macd = df['macd'].iloc[-1]
    macd_signal = df['macd_signal'].iloc[-1]
    macd_hist = df['macd_hist'].iloc[-1]

    bb_upper = df['bb_upper'].iloc[-1]
    bb_lower = df['bb_lower'].iloc[-1]
    bb_middle = df['bb_middle'].iloc[-1]
    bb_width = df['bb_width'].iloc[-1]

    adx = df['adx_14'].iloc[-1]
    plus_di = df['plus_di_14'].iloc[-1]
    minus_di = df['minus_di_14'].iloc[-1]

    # Calculate volatility
    volatility = df['returns'].rolling(window=20).std().iloc[-1]

    # Store indicator values in analysis
    analysis['indicators'] = {
        'price': {
            'open': float(last_open),
            'high': float(last_high),
            'low': float(last_low),
            'close': float(last_close),
            'color': last_color
        },
        'moving_averages': {
            'sma_5': float(sma_5),
            'sma_10': float(sma_10),
            'sma_20': float(sma_20),
            'sma_50': float(sma_50),
            'ema_5': float(ema_5),
            'ema_10': float(ema_10),
            'ema_20': float(ema_20)
        },
        'oscillators': {
            'rsi': float(rsi),
            'macd': float(macd),
            'macd_signal': float(macd_signal),
            'macd_hist': float(macd_hist)
        },
        'volatility': {
            'atr': float(df['atr_14'].iloc[-1]),
            'bb_width': float(bb_width),
            'std_dev': float(volatility)
        },
        'trend': {
            'adx': float(adx),
            'plus_di': float(plus_di),
            'minus_di': float(minus_di)
        }
    }

    # Determine market regime
    if adx > VERY_STRONG_TREND_ADX:
        if plus_di > minus_di:
            market_regime = "strong_bullish"
        else:
            market_regime = "strong_bearish"
    elif adx > STRONG_TREND_ADX:
        if plus_di > minus_di:
            market_regime = "bullish"
        else:
            market_regime = "bearish"
    elif bb_width < df['bb_width'].rolling(window=20).mean().iloc[-1] * 0.8:
        market_regime = "consolidation"
    elif abs(sma_5 - sma_10) / sma_10 < 0.001 and abs(sma_10 - sma_20) / sma_20 < 0.001:
        market_regime = "ranging"
    else:
        market_regime = "neutral"

    # Determine if overbought/oversold
    overbought = rsi > OVERBOUGHT_RSI
    oversold = rsi < OVERSOLD_RSI

    # Check for Bollinger Band squeeze (volatility contraction)
    bb_squeeze = bb_width < df['bb_width'].rolling(window=20).mean().iloc[-1] * 0.8

    # Check for price near Bollinger Bands
    near_upper_band = last_close > bb_upper * 0.95
    near_lower_band = last_close < bb_lower * 1.05

    # Store market conditions
    analysis['market_conditions'] = {
        'regime': market_regime,
        'volatility': float(volatility),
        'trend_strength': float(adx / 100),  # Normalize to 0-1
        'overbought': bool(overbought),
        'oversold': bool(oversold),
        'bb_squeeze': bool(bb_squeeze),
        'near_upper_band': bool(near_upper_band),
        'near_lower_band': bool(near_lower_band)
    }

    # Check for candlestick patterns
    last_3_candles = df.tail(3)

    # Bullish patterns
    bullish_engulfing = (
        last_3_candles['color'].iloc[-2] == 'red' and
        last_3_candles['color'].iloc[-1] == 'green' and
        last_3_candles['open'].iloc[-1] < last_3_candles['close'].iloc[-2] and
        last_3_candles['close'].iloc[-1] > last_3_candles['open'].iloc[-2]
    )

    hammer = (
        last_3_candles['color'].iloc[-1] == 'green' and
        (last_3_candles['high'].iloc[-1] - last_3_candles['close'].iloc[-1]) <
        (last_3_candles['open'].iloc[-1] - last_3_candles['low'].iloc[-1]) * 0.3 and
        (last_3_candles['close'].iloc[-1] - last_3_candles['open'].iloc[-1]) <
        (last_3_candles['open'].iloc[-1] - last_3_candles['low'].iloc[-1]) * 0.3
    )

    # Bearish patterns
    bearish_engulfing = (
        last_3_candles['color'].iloc[-2] == 'green' and
        last_3_candles['color'].iloc[-1] == 'red' and
        last_3_candles['open'].iloc[-1] > last_3_candles['close'].iloc[-2] and
        last_3_candles['close'].iloc[-1] < last_3_candles['open'].iloc[-2]
    )

    shooting_star = (
        last_3_candles['color'].iloc[-1] == 'red' and
        (last_3_candles['high'].iloc[-1] - last_3_candles['open'].iloc[-1]) >
        (last_3_candles['open'].iloc[-1] - last_3_candles['close'].iloc[-1]) * 2 and
        (last_3_candles['close'].iloc[-1] - last_3_candles['low'].iloc[-1]) <
        (last_3_candles['open'].iloc[-1] - last_3_candles['close'].iloc[-1]) * 0.3
    )

    # Store pattern recognition
    analysis['patterns'] = {
        'bullish_engulfing': bool(bullish_engulfing),
        'hammer': bool(hammer),
        'bearish_engulfing': bool(bearish_engulfing),
        'shooting_star': bool(shooting_star)
    }

    # Check for trend confirmation
    price_above_ma = last_close > sma_20
    ma_alignment_bullish = sma_5 > sma_10 > sma_20
    ma_alignment_bearish = sma_5 < sma_10 < sma_20

    # Check for momentum confirmation
    macd_above_signal = macd > macd_signal
    macd_rising = macd > df['macd'].iloc[-2]
    rsi_rising = rsi > df['rsi_14'].iloc[-2]
    rsi_falling = rsi < df['rsi_14'].iloc[-2]

    # Check for volume confirmation if available
    volume_confirmation = False
    if 'volume' in df.columns:
        avg_volume = df['volume'].rolling(window=10).mean().iloc[-1]
        last_volume = df['volume'].iloc[-1]
        volume_increasing = last_volume > avg_volume * 1.2
        volume_confirmation = volume_increasing

    # Adjust confidence based on analysis
    adjusted_confidence = confidence

    # Confirmation signals for UP direction
    if direction == "UP":
        # Add confirmation signals
        if price_above_ma:
            analysis['confirmations'].append("Price above 20-period MA")
            adjusted_confidence += 0.03

        if ma_alignment_bullish:
            analysis['confirmations'].append("Bullish MA alignment")
            adjusted_confidence += 0.05

        if macd_above_signal:
            analysis['confirmations'].append("MACD above signal line")
            adjusted_confidence += 0.03

        if macd_rising:
            analysis['confirmations'].append("MACD rising")
            adjusted_confidence += 0.02

        if rsi_rising and rsi > 50:
            analysis['confirmations'].append("RSI rising and above 50")
            adjusted_confidence += 0.03

        if bullish_engulfing:
            analysis['confirmations'].append("Bullish engulfing pattern")
            adjusted_confidence += 0.05

        if hammer:
            analysis['confirmations'].append("Hammer pattern")
            adjusted_confidence += 0.04

        if volume_confirmation:
            analysis['confirmations'].append("Volume confirmation")
            adjusted_confidence += 0.03

        if market_regime in ["bullish", "strong_bullish"]:
            analysis['confirmations'].append(f"Bullish market regime ({market_regime})")
            adjusted_confidence += 0.05

        # Add warning signals
        if overbought:
            analysis['warnings'].append("RSI overbought")
            adjusted_confidence -= 0.07

        if near_upper_band:
            analysis['warnings'].append("Price near upper Bollinger Band")
            adjusted_confidence -= 0.05

        if bearish_engulfing:
            analysis['warnings'].append("Bearish engulfing pattern")
            adjusted_confidence -= 0.08

        if shooting_star:
            analysis['warnings'].append("Shooting star pattern")
            adjusted_confidence -= 0.06

        if market_regime in ["bearish", "strong_bearish"]:
            analysis['warnings'].append(f"Bearish market regime ({market_regime})")
            adjusted_confidence -= 0.08

    # Confirmation signals for DOWN direction
    elif direction == "DOWN":
        # Add confirmation signals
        if not price_above_ma:
            analysis['confirmations'].append("Price below 20-period MA")
            adjusted_confidence += 0.03

        if ma_alignment_bearish:
            analysis['confirmations'].append("Bearish MA alignment")
            adjusted_confidence += 0.05

        if not macd_above_signal:
            analysis['confirmations'].append("MACD below signal line")
            adjusted_confidence += 0.03

        if not macd_rising:
            analysis['confirmations'].append("MACD falling")
            adjusted_confidence += 0.02

        if rsi_falling and rsi < 50:
            analysis['confirmations'].append("RSI falling and below 50")
            adjusted_confidence += 0.03

        if bearish_engulfing:
            analysis['confirmations'].append("Bearish engulfing pattern")
            adjusted_confidence += 0.05

        if shooting_star:
            analysis['confirmations'].append("Shooting star pattern")
            adjusted_confidence += 0.04

        if volume_confirmation:
            analysis['confirmations'].append("Volume confirmation")
            adjusted_confidence += 0.03

        if market_regime in ["bearish", "strong_bearish"]:
            analysis['confirmations'].append(f"Bearish market regime ({market_regime})")
            adjusted_confidence += 0.05

        # Add warning signals
        if oversold:
            analysis['warnings'].append("RSI oversold")
            adjusted_confidence -= 0.07

        if near_lower_band:
            analysis['warnings'].append("Price near lower Bollinger Band")
            adjusted_confidence -= 0.05

        if bullish_engulfing:
            analysis['warnings'].append("Bullish engulfing pattern")
            adjusted_confidence -= 0.08

        if hammer:
            analysis['warnings'].append("Hammer pattern")
            adjusted_confidence -= 0.06

        if market_regime in ["bullish", "strong_bullish"]:
            analysis['warnings'].append(f"Bullish market regime ({market_regime})")
            adjusted_confidence -= 0.08

    # Adjust for volatility
    if volatility > HIGH_VOLATILITY:
        analysis['warnings'].append(f"High volatility ({volatility:.4f})")
        adjusted_confidence -= 0.05
    elif volatility < LOW_VOLATILITY:
        analysis['warnings'].append(f"Low volatility ({volatility:.4f})")
        adjusted_confidence -= 0.03

    # Adjust for trend strength
    if adx < STRONG_TREND_ADX:
        analysis['warnings'].append(f"Weak trend (ADX: {adx:.1f})")
        adjusted_confidence -= 0.04
    elif adx > VERY_STRONG_TREND_ADX:
        analysis['confirmations'].append(f"Very strong trend (ADX: {adx:.1f})")
        adjusted_confidence += 0.05

    # Ensure confidence is within bounds
    adjusted_confidence = max(0.0, min(1.0, adjusted_confidence))

    # Determine if we should take the trade
    min_required_confirmations = 2  # Reduced from 3 to be less conservative
    max_allowed_warnings = 3  # Increased from 2 to be less conservative

    take_trade = (
        adjusted_confidence >= min_confidence and
        len(analysis['confirmations']) >= min_required_confirmations and
        len(analysis['warnings']) <= max_allowed_warnings
    )

    # Special case: high confidence should override some warnings
    if adjusted_confidence > min_confidence + 0.1 and len(analysis['confirmations']) >= 3:
        take_trade = True

    # Special case: very high confidence with strong confirmations
    if adjusted_confidence > min_confidence + 0.15 and len(analysis['confirmations']) >= 4:
        take_trade = True

    # Special case: extremely high confidence should almost always trade
    if adjusted_confidence > 0.9 and len(analysis['confirmations']) >= 2:
        take_trade = True

    # Special case: too many warnings
    if len(analysis['warnings']) > 4:  # Increased from 3 to be less conservative
        take_trade = False

    # Store final decision and adjusted confidence
    analysis['take_trade'] = take_trade
    analysis['adjusted_confidence'] = float(adjusted_confidence)
    analysis['confidence_adjustment'] = float(adjusted_confidence - confidence)

    return take_trade, analysis, adjusted_confidence

def analyze_multiple_timeframes(candles_1m, candles_5m, candles_15m, prediction, min_confidence=0.65):
    """
    Analyze trade opportunity across multiple timeframes for stronger confirmation

    Parameters:
    - candles_1m: 1-minute candles
    - candles_5m: 5-minute candles
    - candles_15m: 15-minute candles
    - prediction: Dictionary containing model prediction
    - min_confidence: Minimum confidence threshold

    Returns:
    - take_trade: Boolean indicating whether to take the trade
    - analysis: Dictionary with detailed analysis
    - adjusted_confidence: Adjusted confidence level
    """
    # Analyze each timeframe
    take_trade_1m, analysis_1m, adjusted_confidence_1m = analyze_trade_opportunity(candles_1m, prediction, min_confidence)
    take_trade_5m, analysis_5m, adjusted_confidence_5m = analyze_trade_opportunity(candles_5m, prediction, min_confidence)
    take_trade_15m, analysis_15m, adjusted_confidence_15m = analyze_trade_opportunity(candles_15m, prediction, min_confidence)

    # Combine analyses
    combined_analysis = {
        'timestamp': datetime.now().isoformat(),
        'direction': prediction.get('direction'),
        'base_confidence': prediction.get('confidence', 0),
        'timeframes': {
            '1m': analysis_1m,
            '5m': analysis_5m,
            '15m': analysis_15m
        },
        'timeframe_agreement': {
            'take_trade': [take_trade_1m, take_trade_5m, take_trade_15m],
            'confidence': [adjusted_confidence_1m, adjusted_confidence_5m, adjusted_confidence_15m]
        },
        'confirmations': [],
        'warnings': []
    }

    # Calculate agreement metrics
    agreement_count = sum([1 for t in [take_trade_1m, take_trade_5m, take_trade_15m] if t])
    agreement_ratio = agreement_count / 3

    # Calculate weighted confidence
    # Give more weight to higher timeframes
    weighted_confidence = (
        adjusted_confidence_1m * 0.2 +
        adjusted_confidence_5m * 0.3 +
        adjusted_confidence_15m * 0.5
    )

    # Add timeframe agreement confirmations/warnings
    if agreement_count == 3:
        combined_analysis['confirmations'].append("All timeframes agree to take the trade")
    elif agreement_count == 2:
        combined_analysis['confirmations'].append("Two timeframes agree to take the trade")
    elif agreement_count == 1:
        combined_analysis['warnings'].append("Only one timeframe suggests taking the trade")
    else:
        combined_analysis['warnings'].append("No timeframe suggests taking the trade")

    # Determine final decision
    take_trade = agreement_count >= 2 and weighted_confidence >= min_confidence

    # Store final decision and confidence
    combined_analysis['take_trade'] = take_trade
    combined_analysis['weighted_confidence'] = float(weighted_confidence)
    combined_analysis['agreement_ratio'] = float(agreement_ratio)

    return take_trade, combined_analysis, weighted_confidence

def get_risk_reward_ratio(candles, direction, expiration_seconds=60):
    """
    Calculate the risk-reward ratio for a potential trade

    Parameters:
    - candles: List of candle data
    - direction: Trading direction ('UP' or 'DOWN')
    - expiration_seconds: Expiration time in seconds

    Returns:
    - risk_reward: Risk-reward ratio
    - take_profit: Potential profit level
    - stop_loss: Potential stop loss level
    """
    # Convert to DataFrame
    df = pd.DataFrame(candles)

    # Calculate ATR for volatility-based targets
    df['tr1'] = abs(df['high'] - df['low'])
    df['tr2'] = abs(df['high'] - df['close'].shift(1))
    df['tr3'] = abs(df['low'] - df['close'].shift(1))
    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    df['atr_14'] = df['true_range'].rolling(window=14).mean()

    # Get current price and ATR
    current_price = df['close'].iloc[-1]
    atr = df['atr_14'].iloc[-1]

    # Calculate expiration candles (approximate)
    candle_period = 60  # Assuming 1-minute candles
    expiration_candles = max(1, expiration_seconds // candle_period)

    # Calculate potential price movement based on ATR and expiration
    potential_movement = atr * np.sqrt(expiration_candles)

    # Set take profit and stop loss levels
    if direction == "UP":
        take_profit = current_price + potential_movement
        stop_loss = current_price - (potential_movement * 0.5)  # Tighter stop loss
    else:  # direction == "DOWN"
        take_profit = current_price - potential_movement
        stop_loss = current_price + (potential_movement * 0.5)  # Tighter stop loss

    # Calculate risk-reward ratio
    if direction == "UP":
        reward = take_profit - current_price
        risk = current_price - stop_loss
    else:  # direction == "DOWN"
        reward = current_price - take_profit
        risk = stop_loss - current_price

    risk_reward = reward / risk if risk > 0 else 0

    return risk_reward, take_profit, stop_loss
