import os
import sys
import tensorflow as tf
import joblib

print("Checking model files...")
print(f"Current directory: {os.getcwd()}")

# Check if models directory exists
if not os.path.exists('models'):
    print("Models directory not found!")
    sys.exit(1)

# List model files
print("\nListing model files:")
model_files = os.listdir('models')
for file in model_files:
    if file.endswith('.h5') or file.endswith('.pkl'):
        print(f"- {file}")

# Try to load a TensorFlow model
print("\nTrying to load TensorFlow models:")
tf_models = [f for f in model_files if f.endswith('.h5')]
for model_file in tf_models[:2]:  # Try first 2 models
    try:
        model_path = os.path.join('models', model_file)
        print(f"Loading {model_file}...")
        model = tf.keras.models.load_model(model_path)
        print(f"Successfully loaded {model_file}")
        print(f"Model summary: {model.summary()}")
    except Exception as e:
        print(f"Error loading {model_file}: {e}")

# Try to load an XGBoost model
print("\nTrying to load XGBoost models:")
xgb_models = [f for f in model_files if f.endswith('.pkl') and 'xgboost' in f]
for model_file in xgb_models[:2]:  # Try first 2 models
    try:
        model_path = os.path.join('models', model_file)
        print(f"Loading {model_file}...")
        model = joblib.load(model_path)
        print(f"Successfully loaded {model_file}")
    except Exception as e:
        print(f"Error loading {model_file}: {e}")

print("\nModel check complete.")
