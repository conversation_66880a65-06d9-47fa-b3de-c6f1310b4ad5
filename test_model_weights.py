#!/usr/bin/env python
"""
Test script to verify model weights generation for Learning page
"""

def test_model_weights_generation():
    """Test model weights generation like the UI does"""
    print("🔍 Testing Model Weights Generation...")
    
    try:
        # Import the model manager
        from Models.model_manager import ModelManager
        
        # Initialize model manager
        manager = ModelManager(model_dir='models')
        
        # Load all models
        print("Loading all models...")
        manager.load_models()
        
        # Check which models were loaded
        loaded_models = list(manager.models.keys())
        print(f"✅ Loaded models: {loaded_models}")
        
        # Create ensemble
        print("Creating ensemble...")
        manager.create_ensemble()
        
        # Generate model weights like the UI does
        ensemble_weights = {}
        
        # First, try to get weights from the ensemble if it exists
        if hasattr(manager, 'ensemble') and manager.ensemble:
            if hasattr(manager.ensemble, 'weights') and hasattr(manager.ensemble, 'model_types'):
                for model_type, weight in zip(manager.ensemble.model_types, manager.ensemble.weights):
                    ensemble_weights[model_type] = weight
                    print(f"   From ensemble: {model_type} = {weight:.3f}")

        # Always include information about loaded models (this ensures all models show up)
        if hasattr(manager, 'models'):
            for model_name in manager.models.keys():
                if model_name not in ensemble_weights:
                    # Assign appropriate weights for each model type
                    if model_name == 'xgboost':
                        ensemble_weights[model_name] = 0.4
                    elif model_name == 'lstm_gru':
                        ensemble_weights[model_name] = 0.25
                    elif model_name == 'transformer':
                        ensemble_weights[model_name] = 0.25
                    elif model_name == 'dqn':
                        ensemble_weights[model_name] = 0.1
                    else:
                        ensemble_weights[model_name] = 0.1
                    print(f"   Added missing: {model_name} = {ensemble_weights[model_name]:.3f}")

        # Create the final model weights dictionary like the UI does
        model_weights = {
            **ensemble_weights,
            'data_source': 'ensemble_model_performance',
            'total_models': len(ensemble_weights),
            'timestamp': '2025-01-06T12:00:00'
        }
        
        print(f"\n✅ Final model weights dictionary:")
        for key, value in model_weights.items():
            print(f"   {key}: {value}")
        
        # Test the analytics widget filtering
        print(f"\n🔍 Testing Analytics Widget Filtering...")
        metadata_keys = {'data_source', 'total_models', 'timestamp'}
        
        filtered_weights = {}
        for model_name, weight in model_weights.items():
            # Skip metadata keys
            if model_name in metadata_keys:
                print(f"   Skipping metadata: {model_name}")
                continue
                
            # Only add if weight is a number
            if isinstance(weight, (int, float)):
                filtered_weights[model_name] = weight
                display_name = model_name.replace('_', ' ').title()
                print(f"   Will display: {display_name} = {weight:.4f}")
        
        print(f"\n✅ Models that will appear in Learning page: {len(filtered_weights)}")
        for model_name, weight in filtered_weights.items():
            display_name = model_name.replace('_', ' ').title()
            print(f"   • {display_name}: {weight:.4f}")
            
        return model_weights, filtered_weights
        
    except Exception as e:
        print(f"❌ Error testing model weights: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_analytics_widget():
    """Test the analytics widget update method"""
    print("\n🔍 Testing Analytics Widget...")
    
    try:
        # Create a mock weights dictionary like the UI generates
        mock_weights = {
            'xgboost': 0.4,
            'lstm_gru': 0.25,
            'transformer': 0.25,
            'dqn': 0.1,
            'data_source': 'ensemble_model_performance',
            'total_models': 4,
            'timestamp': '2025-01-06T12:00:00'
        }
        
        print("Mock weights dictionary:")
        for key, value in mock_weights.items():
            print(f"   {key}: {value}")
        
        # Simulate the analytics widget filtering
        metadata_keys = {'data_source', 'total_models', 'timestamp'}
        
        print("\nFiltering process:")
        model_count = 0
        for model_name, weight in mock_weights.items():
            # Skip metadata keys
            if model_name in metadata_keys:
                print(f"   ❌ Skipping metadata: {model_name}")
                continue
                
            # Only add if weight is a number
            if isinstance(weight, (int, float)):
                model_count += 1
                display_name = model_name.replace('_', ' ').title()
                print(f"   ✅ Adding model: {display_name} = {weight:.4f}")
            else:
                print(f"   ❌ Skipping non-numeric: {model_name} = {weight}")
        
        print(f"\n✅ Total models that should appear: {model_count}")
        
        return model_count == 4
        
    except Exception as e:
        print(f"❌ Error testing analytics widget: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Model Weights Tests...\n")
    
    # Test 1: Model weights generation
    model_weights, filtered_weights = test_model_weights_generation()
    
    # Test 2: Analytics widget filtering
    widget_test_passed = test_analytics_widget()
    
    print("\n📊 Test Results:")
    if model_weights and filtered_weights:
        print(f"✅ Model weights generation: PASSED ({len(filtered_weights)} models)")
    else:
        print("❌ Model weights generation: FAILED")
    
    if widget_test_passed:
        print("✅ Analytics widget filtering: PASSED")
    else:
        print("❌ Analytics widget filtering: FAILED")
    
    if model_weights and filtered_weights and widget_test_passed:
        print("\n🎉 ALL TESTS PASSED! Learning page should show all 4 models.")
    else:
        print("\n❌ Some tests failed. Check the output above.")
