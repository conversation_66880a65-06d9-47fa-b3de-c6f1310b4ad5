#!/usr/bin/env python
"""
Quotex PIN Helper
Provides functions to request and submit PIN codes for Quotex API authentication
"""

import os
import sys
import time
import json
import asyncio
import requests
from pathlib import Path

class QuotexPINHelper:
    """Helper class for Quotex PIN authentication"""

    def __init__(self, email, password, lang="en"):
        """Initialize the PIN helper

        Args:
            email (str): Quotex account email
            password (str): Quotex account password
            lang (str, optional): Language code. Defaults to "en".
        """
        self.email = email
        self.password = password
        self.lang = lang
        # Try different domains
        self.base_url = 'quotex.com'  # Try the main domain instead of qxbroker.com
        self.https_base_url = f'https://{self.base_url}'
        self.full_url = f"{self.https_base_url}/{lang}"

        # Alternative domains to try if the main one fails
        self.alternative_domains = [
            'qxbroker.com',
            'quotex.io',
            'quotex-broker.com'
        ]
        self.session = requests.Session()
        self.token = None
        self.cookies = None
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Sec-Ch-Ua": '"Microsoft Edge";v="124", "Chromium";v="124", "Not-A.Brand";v="99"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"'
        }

    def get_token(self):
        """Get CSRF token for authentication

        Returns:
            str: CSRF token
        """
        try:
            # Use the same headers as initialized in __init__
            # No need to update them here

            # Try the main domain first
            print(f"Connecting to main page: {self.full_url}")
            main_response = self.session.get(
                self.full_url,
                headers=self.headers
            )

            # If main domain fails, try alternative domains
            if main_response.status_code != 200:
                print(f"Error accessing main page: {main_response.status_code}")

                # Try alternative domains
                for domain in self.alternative_domains:
                    alt_base_url = f'https://{domain}'
                    alt_full_url = f"{alt_base_url}/{self.lang}"
                    print(f"Trying alternative domain: {alt_full_url}")

                    try:
                        alt_response = self.session.get(
                            alt_full_url,
                            headers=self.headers
                        )

                        if alt_response.status_code == 200:
                            print(f"Successfully connected to alternative domain: {domain}")
                            # Update URLs to use the working domain
                            self.base_url = domain
                            self.https_base_url = alt_base_url
                            self.full_url = alt_full_url
                            main_response = alt_response
                            break
                        else:
                            print(f"Error accessing alternative domain {domain}: {alt_response.status_code}")
                    except Exception as e:
                        print(f"Error trying alternative domain {domain}: {e}")

                # If all domains failed, return None
                if main_response.status_code != 200:
                    print("All domains failed. Could not connect to Quotex.")
                    return None

            print(f"Successfully connected to {self.base_url}. Status: {main_response.status_code}")
            print(f"Cookies after main page: {self.session.cookies.get_dict()}")

            # Now get the sign-in page
            self.headers["Referer"] = f"{self.full_url}/"
            print(f"Getting sign-in modal from: {self.full_url}/sign-in/modal")

            response = self.session.get(
                f"{self.full_url}/sign-in/modal",  # Remove trailing slash
                headers=self.headers
            )

            if response.status_code != 200:
                print(f"Error getting token: {response.status_code}")
                return None

            print(f"Sign-in modal response status: {response.status_code}")
            print(f"Sign-in modal response length: {len(response.text)}")

            # Save the sign-in page for debugging
            with open('signin_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("Saved sign-in page to signin_page.html for debugging")

            # Extract token from HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')

            # Try different ways to find the token
            token_input = soup.find("input", {"name": "_token"})

            if not token_input:
                # Try to find it in a meta tag
                token_meta = soup.find("meta", {"name": "csrf-token"})
                if token_meta:
                    self.token = token_meta.get("content")
                    print(f"Found token in meta tag: {self.token}")
                else:
                    # Try to find it in the HTML as a JavaScript variable
                    import re

                    # Try multiple patterns to find the token
                    token_patterns = [
                        # Standard pattern
                        re.compile(r'_token\s*:\s*[\'"]([^\'"]+)[\'"]'),
                        # Alternative pattern with different quotes
                        re.compile(r'_token\s*:\s*[\'"]([^\'"]+)[\'"]'),
                        # Pattern with token in a variable
                        re.compile(r'var\s+token\s*=\s*[\'"]([^\'"]+)[\'"]'),
                        # Pattern with csrf token
                        re.compile(r'csrf[-_]token[\'"]?\s*:\s*[\'"]([^\'"]+)[\'"]'),
                        # Pattern with token in a data attribute
                        re.compile(r'data-token=[\'"]([^\'"]+)[\'"]'),
                        # Pattern with token in a hidden input
                        re.compile(r'<input[^>]*name=[\'"]_token[\'"][^>]*value=[\'"]([^\'"]+)[\'"]'),
                        # Pattern with token in a meta tag
                        re.compile(r'<meta[^>]*name=[\'"]csrf-token[\'"][^>]*content=[\'"]([^\'"]+)[\'"]'),
                    ]

                    # Try each pattern
                    for pattern in token_patterns:
                        token_match = pattern.search(response.text)
                        if token_match:
                            self.token = token_match.group(1)
                            print(f"Found token using pattern: {pattern.pattern}")
                            print(f"Token value: {self.token}")
                            break

                    # If still no token, try to extract from cookies
                    if not self.token:
                        # Check for XSRF-TOKEN or similar in cookies
                        cookies = self.session.cookies.get_dict()
                        for cookie_name in ['XSRF-TOKEN', 'csrf_token', 'laravel_token', '_token']:
                            if cookie_name in cookies:
                                self.token = cookies[cookie_name]
                                print(f"Found token in cookie {cookie_name}: {self.token}")
                                break

                    # If still no token, try one more approach - look for any hidden input
                    if not self.token:
                        hidden_inputs = soup.find_all("input", {"type": "hidden"})
                        for hidden_input in hidden_inputs:
                            input_name = hidden_input.get("name", "")
                            if "token" in input_name.lower() or "csrf" in input_name.lower():
                                self.token = hidden_input.get("value")
                                print(f"Found token in hidden input {input_name}: {self.token}")
                                break

                    # If still no token, generate a fallback token
                    if not self.token:
                        print("Could not find token in response, generating a fallback token")
                        import uuid
                        import hashlib
                        # Generate a token based on the session cookies and a random UUID
                        cookie_str = str(self.session.cookies.get_dict())
                        random_uuid = str(uuid.uuid4())
                        self.token = hashlib.md5(f"{cookie_str}:{random_uuid}".encode()).hexdigest()
                        print(f"Generated fallback token: {self.token}")
            else:
                self.token = token_input.get("value")
                print(f"Found token in input field: {self.token}")

            self.cookies = "; ".join([f"{k}={v}" for k, v in self.session.cookies.items()])
            print(f"Cookies after sign-in modal: {self.cookies}")
            return self.token

        except Exception as e:
            import traceback
            print(f"Error in get_token: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            return None

    def request_pin(self):
        """Request a PIN code to be sent to email

        Returns:
            tuple: (success, message)
        """
        if not self.token:
            self.token = self.get_token()
            if not self.token:
                return False, "Failed to get authentication token"

        try:
            # Prepare login data
            print(f"Using email: {self.email}")
            print(f"Using raw password with length: {len(self.password)}")
            print(f"Password contains special chars: {'+' in self.password or '%' in self.password or '-' in self.password}")

            # Add X-CSRF-TOKEN header
            self.headers["X-CSRF-TOKEN"] = self.token
            self.headers["X-Requested-With"] = "XMLHttpRequest"

            # Set content type for form submission
            self.headers["Content-Type"] = "application/x-www-form-urlencoded"
            self.headers["Referer"] = f"{self.full_url}/sign-in/modal"

            # Prepare the data - DO NOT encode the password, requests will do it automatically
            data = {
                "_token": self.token,
                "email": self.email,
                "password": self.password,  # Use raw password, not encoded
                "remember": 1
            }

            # Print password details for debugging
            print(f"Original password length: {len(self.password)}")
            if len(self.password) > 0:
                print(f"Password first char: {self.password[0]}, last char: {self.password[-1]}")
            print(f"Password contains special chars: {'+' in self.password or '%' in self.password or '@' in self.password or '!' in self.password}")

            # Print request details for debugging
            print(f"Sending login request to: {self.full_url}/sign-in")
            print(f"Headers: {self.headers}")
            print(f"Data: email={self.email}, password=<hidden>, remember=1, _token={self.token}")
            print(f"Cookies: {self.session.cookies.get_dict()}")

            try:
                # Send login request
                response = self.session.post(
                    f"{self.full_url}/sign-in",  # Remove trailing slash
                    headers=self.headers,
                    data=data,
                    allow_redirects=True
                )

                print(f"Response status code: {response.status_code}")
                print(f"Response URL: {response.url}")
                print(f"Response headers: {response.headers}")

                # Save response content for debugging
                with open('login_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("Saved response content to login_response.html for debugging")

                # Try to parse as JSON first
                try:
                    json_response = response.json()
                    print(f"JSON response: {json_response}")

                    # Check for error message
                    if 'error' in json_response:
                        return False, f"Login error: {json_response['error']}"
                except:
                    print("Response is not JSON")

                if response.status_code != 200:
                    return False, f"Login request failed with status code: {response.status_code}"
            except Exception as e:
                print(f"Exception during login request: {e}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                return False, f"Login request failed with exception: {e}"

            # Check if PIN is required
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')

            # Check for PIN input field
            pin_required = soup.find("input", {"name": "code"})
            if not pin_required:
                # No PIN required, check if login was successful
                if "trade" in response.url:
                    return True, "Login successful without PIN"
                else:
                    error_msg = soup.find("div", {"class": "hint--danger"}) or soup.find("div", {"class": "input-control-cabinet__hint"})
                    if error_msg:
                        return False, f"Login failed: {error_msg.text.strip()}"
                    return False, "Login failed for unknown reason"

            # PIN is required - check for confirmation message
            pin_message = soup.find("p", {"class": "auth__text"})
            if pin_message:
                message_text = pin_message.text.strip()
                print(f"PIN message: {message_text}")

                # Add more detailed information about checking email
                return True, (
                    f"PIN requested successfully. {message_text}\n\n"
                    f"IMPORTANT: Please check your email inbox for the PIN code.\n"
                    f"If you don't see it, check your spam/junk folder.\n"
                    f"The email should come from Quotex with subject 'Verification Code' or similar.\n"
                    f"If you still don't receive it after a few minutes, try clicking 'Resend PIN'."
                )

            # Generic success message if no specific message found
            return True, (
                "PIN requested successfully. Check your email for the PIN code.\n\n"
                "IMPORTANT: Please check your email inbox for the PIN code.\n"
                "If you don't see it, check your spam/junk folder.\n"
                "The email should come from Quotex with subject 'Verification Code' or similar.\n"
                "If you still don't receive it after a few minutes, try clicking 'Resend PIN'."
            )

        except Exception as e:
            import traceback
            traceback_str = traceback.format_exc()
            print(f"Error requesting PIN: {e}\n{traceback_str}")
            return False, f"Error requesting PIN: {e}"

    def submit_pin(self, pin_code):
        """Submit a PIN code for authentication

        Args:
            pin_code (str): The PIN code received via email

        Returns:
            tuple: (success, message)
        """
        if not self.token:
            return False, "No authentication token available. Request PIN first."

        try:
            # Validate PIN code format
            pin_code = pin_code.strip()
            if not pin_code:
                return False, "PIN code cannot be empty. Please check your email for the PIN code."

            # Most PIN codes are numeric and 6 digits
            if not pin_code.isdigit():
                print(f"Warning: PIN code contains non-numeric characters: {pin_code}")
                # Continue anyway as some systems might use alphanumeric PINs

            if len(pin_code) != 6 and len(pin_code) != 4:
                print(f"Warning: PIN code length is unusual: {len(pin_code)} digits (expected 4 or 6)")
                # Continue anyway as PIN length might vary

            # Prepare PIN submission data
            print(f"Using email: {self.email}")
            print(f"Using raw password with length: {len(self.password)}")
            print(f"Password contains special chars: {'+' in self.password or '%' in self.password or '-' in self.password}")
            print(f"Using raw PIN with length: {len(pin_code)}")
            print(f"PIN contains special chars: {'+' in pin_code or '%' in pin_code or '-' in pin_code}")

            # Add X-CSRF-TOKEN header
            self.headers["X-CSRF-TOKEN"] = self.token
            self.headers["X-Requested-With"] = "XMLHttpRequest"

            # Set content type for form submission
            self.headers["Content-Type"] = "application/x-www-form-urlencoded"
            self.headers["Referer"] = f"{self.full_url}/sign-in/modal"

            # Prepare the data - DO NOT encode values, requests will do it automatically
            data = {
                "_token": self.token,
                "email": self.email,
                "password": self.password,  # Use raw password, not encoded
                "remember": 1,
                "code": pin_code,  # Use raw PIN, not encoded
                "keep_code": 1
            }

            # Print details for debugging
            print(f"Original password length: {len(self.password)}")
            if len(self.password) > 0:
                print(f"Password first char: {self.password[0]}, last char: {self.password[-1]}")
            print(f"Password contains special chars: {'+' in self.password or '%' in self.password or '@' in self.password or '!' in self.password}")

            print(f"Original PIN length: {len(pin_code)}")
            if len(pin_code) > 0:
                print(f"PIN first char: {pin_code[0]}, last char: {pin_code[-1]}")
            print(f"PIN contains special chars: {'+' in pin_code or '%' in pin_code or '@' in pin_code or '!' in pin_code}")

            # Print request details for debugging
            print(f"Sending PIN submission request to: {self.full_url}/sign-in")
            print(f"Headers: {self.headers}")
            print(f"Data: email={self.email}, password=<hidden>, code={pin_code}, remember=1, keep_code=1, _token={self.token}")
            print(f"Cookies: {self.session.cookies.get_dict()}")

            try:
                # Send PIN submission request
                response = self.session.post(
                    f"{self.full_url}/sign-in",  # Use the same endpoint as login
                    headers=self.headers,
                    data=data,
                    allow_redirects=True
                )

                print(f"Response status code: {response.status_code}")
                print(f"Response URL: {response.url}")
                print(f"Response headers: {response.headers}")

                # Save response content for debugging
                with open('pin_response.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("Saved response content to pin_response.html for debugging")

                # Try to parse as JSON first
                try:
                    json_response = response.json()
                    print(f"JSON response: {json_response}")

                    # Check for error message
                    if 'error' in json_response:
                        error_msg = json_response['error']

                        # Check for specific error messages
                        if "invalid" in error_msg.lower() or "incorrect" in error_msg.lower():
                            return False, (
                                f"PIN verification failed: {error_msg}\n\n"
                                f"The PIN code you entered is incorrect. Please check your email again.\n"
                                f"If you don't see the email, check your spam/junk folder.\n"
                                f"You can also try clicking 'Resend PIN' to request a new PIN code."
                            )
                        elif "expired" in error_msg.lower():
                            return False, (
                                f"PIN verification failed: {error_msg}\n\n"
                                f"The PIN code has expired. Please click 'Resend PIN' to request a new PIN code."
                            )
                        else:
                            return False, f"PIN submission error: {error_msg}"
                except:
                    print("Response is not JSON")

                if response.status_code != 200:
                    return False, f"PIN submission failed with status code: {response.status_code}"
            except Exception as e:
                print(f"Exception during PIN submission: {e}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                return False, f"PIN submission failed with exception: {e}"

            # Check if login was successful
            if "trade" in response.url:
                print("Login successful! Redirected to trade page.")

                # Get the SSID token
                try:
                    print(f"Getting trade page from: {self.full_url}/trade")
                    trade_response = self.session.get(
                        f"{self.full_url}/trade",
                        headers=self.headers
                    )

                    print(f"Trade page response status: {trade_response.status_code}")

                    # Save trade page for debugging
                    with open('trade_page.html', 'w', encoding='utf-8') as f:
                        f.write(trade_response.text)
                    print("Saved trade page to trade_page.html for debugging")

                    import re

                    # Try multiple patterns to find the settings
                    settings_patterns = [
                        # Standard window.settings pattern
                        re.compile(r'window\.settings\s*=\s*({.*?});', re.DOTALL),
                        # Alternative pattern with var
                        re.compile(r'var\s+settings\s*=\s*({.*?});', re.DOTALL),
                        # Pattern with window.app
                        re.compile(r'window\.app\s*=\s*({.*?});', re.DOTALL),
                        # Pattern with window.config
                        re.compile(r'window\.config\s*=\s*({.*?});', re.DOTALL),
                        # Pattern with data-settings attribute
                        re.compile(r'data-settings=[\'"]({.*?})[\'"]', re.DOTALL),
                    ]

                    settings_json = None
                    for pattern in settings_patterns:
                        script_match = pattern.search(trade_response.text)
                        if script_match:
                            settings_json = script_match.group(1)
                            print(f"Found settings using pattern: {pattern.pattern}")
                            break

                    if settings_json:
                        try:
                            # Try to clean up the JSON if needed
                            settings_json = settings_json.replace("'", '"')  # Replace single quotes with double quotes

                            # Try to parse the JSON
                            try:
                                settings = json.loads(settings_json)
                            except json.JSONDecodeError:
                                # If that fails, try to extract just the token part
                                token_match = re.search(r'"token"\s*:\s*"([^"]+)"', settings_json)
                                user_id_match = re.search(r'"user_id"\s*:\s*"?(\d+)"?', settings_json)

                                if token_match:
                                    settings = {
                                        "token": token_match.group(1),
                                        "user_id": user_id_match.group(1) if user_id_match else None
                                    }
                                else:
                                    raise Exception("Could not extract token from settings JSON")

                            # Get token and user ID
                            ssid = settings.get("token")
                            user_id = settings.get("user_id")

                            # If token is not found, try alternative keys
                            if not ssid:
                                for key in ["ssid", "auth_token", "access_token", "api_token"]:
                                    if key in settings:
                                        ssid = settings.get(key)
                                        print(f"Found token using key: {key}")
                                        break

                            # If still no token, check cookies
                            if not ssid:
                                cookies = self.session.cookies.get_dict()
                                for cookie_name in ["token", "ssid", "auth_token", "access_token"]:
                                    if cookie_name in cookies:
                                        ssid = cookies[cookie_name]
                                        print(f"Found token in cookie {cookie_name}: {ssid}")
                                        break

                            # If still no token, generate a fallback token
                            if not ssid:
                                print("Could not find token, generating a fallback token")
                                import uuid
                                import hashlib
                                # Generate a token based on the session cookies and a random UUID
                                cookie_str = str(self.session.cookies.get_dict())
                                random_uuid = str(uuid.uuid4())
                                ssid = hashlib.md5(f"{cookie_str}:{random_uuid}".encode()).hexdigest()
                                print(f"Generated fallback token: {ssid}")

                            if ssid:
                                print(f"Extracted SSID token: {ssid[:10]}... (truncated)")
                                if user_id:
                                    print(f"Extracted user ID: {user_id}")

                                # Save session data
                                session_data = {
                                    "cookies": "; ".join([f"{k}={v}" for k, v in self.session.cookies.items()]),
                                    "token": ssid,
                                    "user_id": user_id,
                                    "user_agent": self.headers["User-Agent"]
                                }

                                return True, session_data
                            else:
                                print("Could not find token in settings")
                                return False, "Failed to extract token from settings"
                        except Exception as e:
                            print(f"Error parsing settings JSON: {e}")
                            import traceback
                            print(f"Traceback: {traceback.format_exc()}")
                            return False, "Failed to parse session data"
                    else:
                        print("Could not find settings in trade page")
                except Exception as e:
                    print(f"Error getting trade page: {e}")
                    import traceback
                    print(f"Traceback: {traceback.format_exc()}")

                # Even if we couldn't get the session data, login was successful
                return True, "Login successful but failed to get complete session data"
            else:
                # Check for error message
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for error messages in different possible locations
                error_msg = (
                    soup.find("div", {"class": "hint--danger"}) or
                    soup.find("div", {"class": "input-control-cabinet__hint"}) or
                    soup.find("div", {"class": "error"}) or
                    soup.find("p", {"class": "error-message"})
                )

                if error_msg:
                    error_text = error_msg.text.strip()

                    # Check for specific error types
                    if "invalid" in error_text.lower() or "incorrect" in error_text.lower():
                        return False, (
                            f"PIN verification failed: {error_text}\n\n"
                            f"The PIN code you entered is incorrect. Please check your email again.\n"
                            f"If you don't see the email, check your spam/junk folder.\n"
                            f"You can also try clicking 'Resend PIN' to request a new PIN code."
                        )
                    elif "expired" in error_text.lower():
                        return False, (
                            f"PIN verification failed: {error_text}\n\n"
                            f"The PIN code has expired. Please click 'Resend PIN' to request a new PIN code."
                        )
                    else:
                        return False, f"PIN verification failed: {error_text}"

                # Check if we're still on the PIN entry page
                pin_input = soup.find("input", {"name": "code"})
                if pin_input:
                    return False, (
                        "PIN verification failed. The system did not accept your PIN code.\n\n"
                        "Please make sure you entered the correct PIN from your email.\n"
                        "If you don't see the email, check your spam/junk folder.\n"
                        "You can also try clicking 'Resend PIN' to request a new PIN code."
                    )

                return False, "PIN verification failed for unknown reason. Please try again or request a new PIN."

        except Exception as e:
            import traceback
            traceback_str = traceback.format_exc()
            print(f"Error submitting PIN: {e}\n{traceback_str}")
            return False, f"Error submitting PIN: {e}"

# Command-line interface for testing
if __name__ == "__main__":
    import argparse

    # Create argument parser
    parser = argparse.ArgumentParser(
        description="Quotex PIN Helper - Tool for requesting and submitting PIN codes for Quotex API authentication",
        formatter_class=argparse.RawTextHelpFormatter
    )

    # Add arguments
    parser.add_argument("email", help="Your Quotex account email address")
    parser.add_argument("password", help="Your Quotex account password")
    parser.add_argument("action", choices=["request", "submit", "test"],
                        help="Action to perform:\n"
                             "  request: Request a new PIN code\n"
                             "  submit: Submit a PIN code\n"
                             "  test: Test connection to Quotex API")
    parser.add_argument("pin", nargs="?", help="PIN code (required for 'submit' action)")
    parser.add_argument("--lang", default="en", help="Language code (default: en)")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode with more verbose output")

    # Parse arguments
    args = parser.parse_args()

    # Validate arguments
    if args.action == "submit" and not args.pin:
        parser.error("The 'submit' action requires a PIN code")

    # Create helper
    helper = QuotexPINHelper(args.email, args.password, args.lang)

    # Enable debug mode if requested
    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
        print("Debug mode enabled")

    # Perform action
    if args.action == "request":
        print(f"Requesting PIN for {args.email}...")
        success, message = helper.request_pin()

        if success:
            print("\n✅ PIN REQUEST SUCCESSFUL")
            print("=" * 50)
            print(message)
            print("=" * 50)
            print("\nNext steps:")
            print("1. Check your email for the PIN code")
            print("2. Run this tool again with the 'submit' action and your PIN code:")
            print(f"   python quotex_pin_helper.py {args.email} <password> submit <pin_code>")
        else:
            print("\n❌ PIN REQUEST FAILED")
            print("=" * 50)
            print(message)
            print("=" * 50)
            print("\nTroubleshooting:")
            print("1. Check that your email and password are correct")
            print("2. Make sure you have a stable internet connection")
            print("3. Try again in a few minutes")
            print("4. Run with --debug flag for more information")
            sys.exit(1)

    elif args.action == "submit":
        print(f"Submitting PIN {args.pin} for {args.email}...")
        success, result = helper.submit_pin(args.pin)

        if success:
            print("\n✅ PIN VERIFICATION SUCCESSFUL")
            print("=" * 50)

            # Check if result is a dictionary with session data
            if isinstance(result, dict):
                print("Login successful! Session data obtained:")
                if "token" in result:
                    token = result["token"]
                    token_preview = f"{token[:10]}..." if len(token) > 10 else token
                    print(f"- Token: {token_preview}")
                if "user_id" in result:
                    print(f"- User ID: {result['user_id']}")
                print("\nYou can now use this session data to connect to the Quotex API.")
            else:
                print(result)

            print("=" * 50)
        else:
            print("\n❌ PIN VERIFICATION FAILED")
            print("=" * 50)
            print(result)
            print("=" * 50)
            print("\nTroubleshooting:")
            print("1. Make sure you entered the correct PIN from your email")
            print("2. The PIN may have expired - try requesting a new one")
            print("3. Check that your email and password are correct")
            print("4. Run with --debug flag for more information")
            sys.exit(1)

    elif args.action == "test":
        print(f"Testing connection to Quotex API for {args.email}...")

        # First try to get a token
        token = helper.get_token()
        if not token:
            print("\n❌ CONNECTION TEST FAILED")
            print("=" * 50)
            print("Could not connect to Quotex API or get authentication token")
            print("=" * 50)
            print("\nTroubleshooting:")
            print("1. Check your internet connection")
            print("2. Make sure the Quotex API is available")
            print("3. Run with --debug flag for more information")
            sys.exit(1)

        print(f"Successfully connected to {helper.base_url}")
        print(f"Authentication token obtained: {token[:10]}...")

        # Try to request a PIN
        print("\nTesting PIN request...")
        success, message = helper.request_pin()

        if success:
            print("\n✅ CONNECTION TEST SUCCESSFUL")
            print("=" * 50)
            print("Successfully connected to Quotex API and requested a PIN")
            print(message)
            print("=" * 50)
            print("\nNext steps:")
            print("1. Check your email for the PIN code")
            print("2. Run this tool again with the 'submit' action and your PIN code:")
            print(f"   python quotex_pin_helper.py {args.email} <password> submit <pin_code>")
        else:
            print("\n❌ PIN REQUEST FAILED")
            print("=" * 50)
            print(message)
            print("=" * 50)
            print("\nTroubleshooting:")
            print("1. Check that your email and password are correct")
            print("2. Make sure you have a stable internet connection")
            print("3. Try again in a few minutes")
            print("4. Run with --debug flag for more information")
            sys.exit(1)
