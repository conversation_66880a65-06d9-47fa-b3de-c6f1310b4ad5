#!/usr/bin/env python
"""
Live Market Viewer with PyQt5

This application provides a real-time view of market data from Quotex,
with live candlestick charts and market information.
It focuses on OTC markets like USD/ARS (OTC) and USD/BRL (OTC).
"""

import os
import sys
import asyncio
import configparser
import threading
import time
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QComboBox, QPushButton,
                            QGridLayout, QGroupBox, QSplitter, QFrame,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject, QThread
from PyQt5.QtGui import QFont, QColor

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import mplfinance as mpf
import matplotlib.pyplot as plt

from quotexapi.stable_api import Quotex

# Load credentials from config.ini
config = configparser.RawConfigParser()
config_path = os.path.join('settings', 'config.ini')
if os.path.exists(config_path):
    config.read(config_path)
    email = config.get('settings', 'email', fallback=None)
    password = config.get('settings', 'password', fallback=None)
else:
    email = None
    password = None

# Initialize Quotex client
client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

# Signal class for thread communication
class WorkerSignals(QObject):
    candles_updated = pyqtSignal(object)
    markets_updated = pyqtSignal(object)
    connection_status = pyqtSignal(bool, str)
    error = pyqtSignal(str)

# Worker thread for background data fetching
class DataWorker(QThread):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.signals = WorkerSignals()
        self.running = True
        self.current_market = "USD/ARS (OTC)"
        self.timeframe = 60  # 1-minute candles by default
        self.candle_count = 100
        self.connected = False
        self.client = client
        self.markets_data = {}
        self.otc_markets = {}

    async def connect_to_api(self):
        """Connect to Quotex API"""
        try:
            check_connect, message = await self.client.connect()
            self.signals.connection_status.emit(check_connect, message)
            self.connected = check_connect

            if check_connect:
                # Get all assets data
                all_data = self.client.get_payment()

                # Get all asset codes
                codes_asset = await self.client.get_all_assets()

                # Create a mapping from display name to code
                asset_codes = {}
                for code, name in codes_asset.items():
                    asset_codes[name] = code

                # Filter for OTC markets only
                self.otc_markets = {name: data for name, data in all_data.items() if "(OTC)" in name}
                self.markets_data = {"all_data": all_data, "asset_codes": asset_codes, "otc_markets": self.otc_markets}

                # Emit markets data
                self.signals.markets_updated.emit(self.markets_data)

            return check_connect
        except Exception as e:
            self.signals.error.emit(f"Connection error: {str(e)}")
            return False

    async def fetch_candles(self):
        """Fetch candles for the current market"""
        try:
            if not self.connected:
                return

            # Convert display name to API name (e.g., "USD/ARS (OTC)" to "USDARS_otc")
            # First, handle the special format
            if "(" in self.current_market and ")" in self.current_market:
                # Extract the base part and the OTC part
                base_part = self.current_market.split("(")[0].strip()
                # Remove slashes and spaces from base part
                base_part = base_part.replace("/", "")
                # Add _otc suffix
                api_market_name = f"{base_part}_otc"
            else:
                # Regular conversion for non-OTC markets
                api_market_name = self.current_market.replace("/", "")

            # Debug output
            print(f"Fetching candles for market: {self.current_market} (API name: {api_market_name})")

            # Check if asset is open first
            asset_name, asset_data = await self.client.get_available_asset(api_market_name, force_open=True)
            print(f"Asset check result: {asset_name}, {asset_data}")

            # Get candles - use the original API market name if the asset_name doesn't contain "otc"
            end_from_time = time.time()
            candle_market_name = api_market_name if "otc" not in asset_name.lower() else asset_name
            print(f"Using market name for candles: {candle_market_name}")
            candles = await self.client.get_candles(candle_market_name, end_from_time, self.candle_count, self.timeframe)

            if candles and len(candles) > 0:
                print(f"Retrieved {len(candles)} candles for {self.current_market}")

                # Convert to DataFrame
                df = pd.DataFrame(candles)

                # Add human-readable timestamp
                df['timestamp'] = df['time'].apply(lambda x: datetime.fromtimestamp(x))

                # Set timestamp as index
                df.set_index('timestamp', inplace=True)

                # Rename columns to match mplfinance requirements
                df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'}, inplace=True)

                # Ensure all required columns exist
                for col in ['Open', 'High', 'Low', 'Close']:
                    if col not in df.columns:
                        print(f"Warning: {col} column missing, adding zeros")
                        df[col] = 0.0

                # Emit updated candles
                self.signals.candles_updated.emit(df)
            else:
                print(f"No candles returned for {self.current_market} (API name: {api_market_name})")
                self.signals.error.emit(f"No candle data retrieved for {self.current_market}")
        except Exception as e:
            print(f"Error fetching candles: {str(e)}")
            self.signals.error.emit(f"Error fetching candles: {str(e)}")

    async def run_async(self):
        """Main async loop"""
        # Connect to API
        connected = await self.connect_to_api()

        if not connected:
            self.signals.error.emit("Failed to connect to API. Check credentials.")
            return

        # Main data fetch loop
        while self.running:
            await self.fetch_candles()
            await asyncio.sleep(1)  # Update every second

    def run(self):
        """Thread entry point"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.run_async())
        finally:
            loop.close()

    def stop(self):
        """Stop the worker thread"""
        self.running = False
        self.wait()

# Candlestick chart widget
class CandlestickChart(FigureCanvas):
    def __init__(self, parent=None, width=10, height=8, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super().__init__(self.fig)
        self.setParent(parent)
        self.df = None
        self.market_name = ""

        # Set up the plot
        self.setup_plot()

    def setup_plot(self):
        """Set up the initial plot"""
        self.axes.set_title("Loading data...", fontsize=12)
        self.axes.set_xlabel("Time")
        self.axes.set_ylabel("Price")
        self.fig.tight_layout()
        self.draw()

    def update_chart(self, df, market_name):
        """Update the chart with new data"""
        self.df = df
        self.market_name = market_name

        try:
            # Clear the figure and create a new axes
            self.fig.clear()
            self.axes = self.fig.add_subplot(111)

            if self.df is not None and not self.df.empty:
                # Make sure we have all required columns with proper types
                for col in ['Open', 'High', 'Low', 'Close']:
                    if col not in self.df.columns:
                        self.df[col] = 0.0
                    else:
                        # Ensure numeric type
                        self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

                # Make sure we have enough valid data
                if len(self.df) >= 2:
                    # Create up/down colors for candlesticks
                    up_color = 'green'
                    down_color = 'red'

                    # Draw candlesticks manually
                    for i, (idx, row) in enumerate(self.df.iterrows()):
                        # Determine if this candle is up or down
                        is_up = row['Close'] >= row['Open']
                        color = up_color if is_up else down_color

                        # Plot the candle body
                        body_bottom = row['Open'] if is_up else row['Close']
                        body_top = row['Close'] if is_up else row['Open']
                        body_height = body_top - body_bottom

                        # Plot the candle wick
                        self.axes.plot([i, i], [row['Low'], row['High']], color='black', linewidth=1)

                        # Plot the candle body as a rectangle
                        self.axes.add_patch(plt.Rectangle((i-0.4, body_bottom), 0.8, body_height,
                                                        fill=True, color=color))

                    # Set x-axis labels
                    if len(self.df) > 10:
                        # Only show some of the dates to avoid overcrowding
                        step = max(1, len(self.df) // 10)
                        xticks = range(0, len(self.df), step)
                        self.axes.set_xticks(xticks)
                        self.axes.set_xticklabels([self.df.index[i].strftime('%H:%M') for i in xticks],
                                                rotation=45)
                    else:
                        self.axes.set_xticks(range(len(self.df)))
                        self.axes.set_xticklabels([idx.strftime('%H:%M') for idx in self.df.index],
                                                rotation=45)

                    # Set title and labels
                    self.axes.set_title(f"{market_name} - Live Chart")
                    self.axes.set_ylabel("Price")

                    # Set y-axis limits with some padding
                    min_price = self.df['Low'].min()
                    max_price = self.df['High'].max()
                    price_range = max_price - min_price
                    padding = price_range * 0.05  # 5% padding
                    self.axes.set_ylim(min_price - padding, max_price + padding)

                    # Add grid
                    self.axes.grid(True, alpha=0.3)
                else:
                    # Not enough data for candlestick chart, use line chart instead
                    self.axes.plot(range(len(self.df)), self.df['Close'], label='Close Price', color='blue')
                    self.axes.set_title(f"{market_name} - Live Chart (Limited Data)")
                    self.axes.set_ylabel("Price")
                    self.axes.legend()

                    # Set x-axis labels
                    self.axes.set_xticks(range(len(self.df)))
                    self.axes.set_xticklabels([idx.strftime('%H:%M') for idx in self.df.index],
                                            rotation=45)
            else:
                # No data, show message
                self.axes.text(0.5, 0.5, "No data available",
                              horizontalalignment='center', verticalalignment='center',
                              transform=self.axes.transAxes, fontsize=14)
                self.axes.set_title(f"{market_name} - Waiting for Data")

            # Adjust layout
            self.fig.tight_layout()

            # Redraw
            self.draw()
        except Exception as e:
            # If plotting fails, create a new axes and show error message
            self.fig.clear()
            self.axes = self.fig.add_subplot(111)
            self.axes.text(0.5, 0.5, f"Error plotting chart: {str(e)}",
                          horizontalalignment='center', verticalalignment='center',
                          transform=self.axes.transAxes, fontsize=12)
            self.axes.set_title(f"{market_name} - Chart Error")
            print(f"Error updating chart: {str(e)}")
            self.draw()

# Main application window
class LiveMarketViewer(QMainWindow):
    def __init__(self):
        super().__init__()

        # Set up the UI
        self.setWindowTitle("Live Market Viewer")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize variables
        self.current_market = "USD/ARS (OTC)"
        self.markets_data = {}
        self.candles_df = None

        # Set up the worker thread
        self.worker = DataWorker()
        self.worker.signals.candles_updated.connect(self.update_candles)
        self.worker.signals.markets_updated.connect(self.update_markets)
        self.worker.signals.connection_status.connect(self.update_connection_status)
        self.worker.signals.error.connect(self.show_error)

        # Create UI components
        self.create_ui()

        # Start the worker thread
        self.worker.start()

    def create_ui(self):
        """Create the user interface"""
        # Main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # Top controls
        controls_layout = QHBoxLayout()

        # Market selection
        market_label = QLabel("Market:")
        self.market_combo = QComboBox()
        self.market_combo.setMinimumWidth(200)
        self.market_combo.currentTextChanged.connect(self.change_market)

        # Timeframe selection
        timeframe_label = QLabel("Timeframe:")
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1 Minute", "5 Minutes", "15 Minutes", "30 Minutes", "1 Hour"])
        self.timeframe_combo.setCurrentIndex(0)
        self.timeframe_combo.currentTextChanged.connect(self.change_timeframe)

        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_data)

        # Status label
        self.status_label = QLabel("Connecting...")

        # Add controls to layout
        controls_layout.addWidget(market_label)
        controls_layout.addWidget(self.market_combo)
        controls_layout.addWidget(timeframe_label)
        controls_layout.addWidget(self.timeframe_combo)
        controls_layout.addWidget(self.refresh_button)
        controls_layout.addStretch()
        controls_layout.addWidget(self.status_label)

        # Main content area with splitter
        content_splitter = QSplitter(Qt.Horizontal)

        # Chart area
        chart_container = QWidget()
        chart_layout = QVBoxLayout()
        self.chart = CandlestickChart(self, width=8, height=6)
        chart_layout.addWidget(self.chart)
        chart_container.setLayout(chart_layout)

        # Market info area
        info_container = QWidget()
        info_layout = QVBoxLayout()

        # Market details group
        market_group = QGroupBox("Market Information")
        market_layout = QGridLayout()

        self.market_name_label = QLabel("Name: -")
        self.market_status_label = QLabel("Status: -")
        self.market_payout_1m_label = QLabel("Payout (1M): -")
        self.market_payout_5m_label = QLabel("Payout (5M): -")

        market_layout.addWidget(self.market_name_label, 0, 0)
        market_layout.addWidget(self.market_status_label, 1, 0)
        market_layout.addWidget(self.market_payout_1m_label, 2, 0)
        market_layout.addWidget(self.market_payout_5m_label, 3, 0)

        market_group.setLayout(market_layout)

        # Latest candles group
        candles_group = QGroupBox("Latest Candles")
        candles_layout = QVBoxLayout()

        self.candles_table = QTableWidget(0, 5)
        self.candles_table.setHorizontalHeaderLabels(["Time", "Open", "High", "Low", "Close"])
        self.candles_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        candles_layout.addWidget(self.candles_table)
        candles_group.setLayout(candles_layout)

        # Add groups to info layout
        info_layout.addWidget(market_group)
        info_layout.addWidget(candles_group)
        info_container.setLayout(info_layout)

        # Add widgets to splitter
        content_splitter.addWidget(chart_container)
        content_splitter.addWidget(info_container)
        content_splitter.setSizes([700, 300])

        # Add all components to main layout
        main_layout.addLayout(controls_layout)
        main_layout.addWidget(content_splitter)

        # Set the main layout
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def update_markets(self, markets_data):
        """Update markets data and UI"""
        self.markets_data = markets_data

        # Update market combo box
        self.market_combo.clear()

        # Add OTC markets first
        otc_markets = sorted(markets_data["otc_markets"].keys())
        for market in otc_markets:
            self.market_combo.addItem(market)

        # Set current market
        if self.current_market in otc_markets:
            self.market_combo.setCurrentText(self.current_market)
        elif len(otc_markets) > 0:
            self.current_market = otc_markets[0]
            self.market_combo.setCurrentText(self.current_market)

        # Update market info
        self.update_market_info()

    def update_market_info(self):
        """Update market information display"""
        if not self.markets_data or "all_data" not in self.markets_data:
            return

        if self.current_market in self.markets_data["all_data"]:
            market_data = self.markets_data["all_data"][self.current_market]

            # Update labels
            self.market_name_label.setText(f"Name: {self.current_market}")

            status = "🟢 Open" if market_data["open"] else "🔴 Closed"
            self.market_status_label.setText(f"Status: {status}")

            profit_1m = f"{market_data['profit']['1M']}%" if '1M' in market_data['profit'] else "N/A"
            self.market_payout_1m_label.setText(f"Payout (1M): {profit_1m}")

            profit_5m = f"{market_data['profit']['5M']}%" if '5M' in market_data['profit'] else "N/A"
            self.market_payout_5m_label.setText(f"Payout (5M): {profit_5m}")

    def update_candles(self, df):
        """Update candles data and UI"""
        self.candles_df = df

        # Update chart
        self.chart.update_chart(df, self.current_market)

        # Update candles table
        self.update_candles_table()

    def update_candles_table(self):
        """Update the candles table with latest data"""
        if self.candles_df is None or self.candles_df.empty:
            return

        # Get the last 10 candles
        last_candles = self.candles_df.tail(10).copy()
        last_candles = last_candles.sort_index(ascending=False)

        # Set table rows
        self.candles_table.setRowCount(len(last_candles))

        # Fill table
        for i, (idx, row) in enumerate(last_candles.iterrows()):
            # Time
            time_item = QTableWidgetItem(idx.strftime("%H:%M:%S"))
            self.candles_table.setItem(i, 0, time_item)

            # OHLC values
            self.candles_table.setItem(i, 1, QTableWidgetItem(f"{row['Open']:.5f}"))
            self.candles_table.setItem(i, 2, QTableWidgetItem(f"{row['High']:.5f}"))
            self.candles_table.setItem(i, 3, QTableWidgetItem(f"{row['Low']:.5f}"))

            # Close with color
            close_item = QTableWidgetItem(f"{row['Close']:.5f}")
            if row['Close'] >= row['Open']:
                close_item.setForeground(QColor('green'))
            else:
                close_item.setForeground(QColor('red'))
            self.candles_table.setItem(i, 4, close_item)

    def update_connection_status(self, connected, message):
        """Update connection status display"""
        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet("color: green")
        else:
            self.status_label.setText(f"Disconnected: {message}")
            self.status_label.setStyleSheet("color: red")

    def show_error(self, error_message):
        """Display error message"""
        self.status_label.setText(f"Error: {error_message}")
        self.status_label.setStyleSheet("color: red")

    def change_market(self, market_name):
        """Change the current market"""
        self.current_market = market_name
        self.worker.current_market = market_name
        self.update_market_info()

    def change_timeframe(self, timeframe_text):
        """Change the chart timeframe"""
        # Map timeframe text to seconds
        timeframe_map = {
            "1 Minute": 60,
            "5 Minutes": 300,
            "15 Minutes": 900,
            "30 Minutes": 1800,
            "1 Hour": 3600
        }

        if timeframe_text in timeframe_map:
            self.worker.timeframe = timeframe_map[timeframe_text]

    def refresh_data(self):
        """Manually refresh data"""
        self.status_label.setText("Refreshing data...")
        self.status_label.setStyleSheet("color: blue")

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop the worker thread
        self.worker.stop()
        event.accept()

# Main entry point
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LiveMarketViewer()
    window.show()
    sys.exit(app.exec_())
