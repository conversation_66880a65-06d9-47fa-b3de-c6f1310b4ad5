#!/usr/bin/env python
"""
Simple test script for Quotex API connection
This script tests the connection to the Quotex API and fetches available assets
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
    print("Successfully imported quotexapi module")
except ImportError as e:
    print(f"Error importing quotexapi module: {e}")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

# Read credentials from config.ini
def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""
    
    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")
            
            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')
            
            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")
    
    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')
    
    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password
    
    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")
    
    return email, password

# Test connection function
async def test_connection(email, password):
    """Test connection to Quotex API"""
    print(f"Testing connection with email: {email}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Create client
        print("Creating Quotex client...")
        client = Quotex(email, password)
        
        # Connect
        print("Connecting to Quotex API...")
        print("If a PIN verification is required, you'll need to enter it in the console.")
        print("Please check your email for the PIN code.")
        
        # Set a timeout for the connection
        start_time = time.time()
        timeout = 30  # 30 seconds timeout
        
        # Try to connect with timeout
        connected = False
        try:
            # Create a task with timeout
            connect_task = asyncio.create_task(client.connect())
            connected = await asyncio.wait_for(connect_task, timeout=timeout)
        except asyncio.TimeoutError:
            print(f"Connection timed out after {timeout} seconds")
            return False
        
        if connected:
            print(f"Successfully connected to Quotex API in {time.time() - start_time:.2f} seconds")
            
            # Test by getting assets
            print("Fetching available assets...")
            try:
                assets = await client.get_all_asset()
                print(f"Available assets: {assets}")
                print(f"Total assets: {len(assets) if assets else 0}")
                
                # Try to get more information about the first asset
                if assets:
                    first_asset = assets[0]
                    print(f"Getting details for asset: {first_asset}")
                    # Add code here to get more details about the asset
                
                return True
            except Exception as asset_error:
                print(f"Error fetching assets: {asset_error}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("Failed to connect to Quotex API")
            print("Please check your credentials and internet connection")
            return False
    except Exception as e:
        print(f"Error in test_connection: {e}")
        import traceback
        traceback.print_exc()
        return False

# Main function
async def main():
    """Main function"""
    print("=== Quotex API Connection Test ===")
    
    # Read credentials
    email, password = read_credentials()
    
    if not email or not password:
        print("No credentials provided. Exiting.")
        return
    
    # Test connection
    result = await test_connection(email, password)
    
    if result:
        print("Connection test successful!")
    else:
        print("Connection test failed!")

# Run the main function
if __name__ == "__main__":
    # Create a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Run the main function
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("Test interrupted by user")
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the event loop
        loop.close()
