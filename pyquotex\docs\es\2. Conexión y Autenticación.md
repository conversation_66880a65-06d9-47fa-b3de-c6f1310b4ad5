# Documentación de Conexión y Autenticación - PyQuotex

## Inicialización del Cliente

Para comenzar a utilizar PyQuotex, primero necesitas inicializar el cliente con tus credenciales:

```python
from quotexapi.stable_api import Quotex

cliente = Quotex(
    email="<EMAIL>",
    password="tu_contraseña",
    lang="es",  # Idioma por defecto (es=Español, en=Inglés, pt=Portugués)
)

# Opcional: Habilitar logs de debugging
cliente.debug_ws_enable = True
```

### Parámetros de Inicialización Opcionales

- `user_agent`: Personalizar el User-Agent (por defecto usa uno predefinido)
- `root_path`: Directorio raíz para almacenar archivos
- `user_data_dir`: Directorio para datos del usuario
- `asset_default`: Par de divisas por defecto (ej: "EURUSD")
- `period_default`: Periodo por defecto en segundos (ej: 60)

## Proceso de Conexión

La conexión se realiza de forma asíncrona. Aquí hay dos formas de establecer la conexión:

### 1. Conexión Simple

```python
import asyncio

async def conectar():
    check_connect, mensaje = await cliente.connect()
    if check_connect:
        print("¡Conexión exitosa!")
        # Tu código aquí
    else:
        print(f"Error de conexión: {mensaje}")

# Ejecutar
asyncio.run(conectar())
```

### 2. Conexión con Reintentos

```python
async def conectar_con_reintentos(intentos=5):
    check_connect, mensaje = await cliente.connect()
    
    if not check_connect:
        intento = 0
        while intento <= intentos:
            if not await cliente.check_connect():
                check_connect, mensaje = await cliente.connect()
                if check_connect:
                    print("¡Reconexión exitosa!")
                    break
                else:
                    intento += 1
                    print(f"Reintentando conexión {intento} de {intentos}")
            await asyncio.sleep(5)
    
    return check_connect, mensaje
```

## Manejo de Sesiones

PyQuotex maneja automáticamente las sesiones y guarda los datos en un archivo `session.json`. Puedes configurar manualmente los datos de sesión:

```python
cliente.set_session(
    user_agent="Mozilla/5.0...", 
    cookies="tus_cookies",  # Opcional
    ssid="tu_ssid"         # Opcional
)
```

### Estabelecer el tipo de cuenta al inicio

```python
# Estabelecer a cuenta de práctica
cliente.set_account_mode("PRACTICE")

# Estabelecer a cuenta real
cliente.set_account_mode("REAL")
```

### Cambio de Modo de Cuenta

```python
# Cambiar a cuenta de práctica
cliente.change_account("PRACTICE")

# Cambiar a cuenta real
cliente.change_account("REAL")
```

## Reconexión Automática

El sistema implementa una reconexión automática cuando detecta desconexiones. Sin embargo, también puedes manejarla manualmente:

```python
async def mantener_conexion():
    while True:
        try:
            if not await cliente.check_connect():
                print("Detectada desconexión, intentando reconectar...")
                check_connect, mensaje = await cliente.connect()
                if check_connect:
                    print("Reconexión exitosa")
                else:
                    print(f"Error en reconexión: {mensaje}")
                    await asyncio.sleep(5)
            await asyncio.sleep(1)
        except Exception as e:
            print(f"Error: {e}")
            await asyncio.sleep(5)
```

### Cierre de Conexión

Es importante cerrar la conexión cuando termines:

```python
cliente.close()
```

## Ejemplo Completo

```python
import asyncio
from quotexapi.stable_api import Quotex

async def ejemplo_completo():
    # Inicialización
    cliente = Quotex(
        email="<EMAIL>",
        password="tu_contraseña",
        lang="es"
    )
    
    try:
        # Conexión con reintentos
        check_connect, mensaje = await conectar_con_reintentos()
        
        if check_connect:
            # Verificar balance
            balance = await cliente.get_balance()
            print(f"Balance actual: {balance}")
            
            # Realizar operaciones...
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        cliente.close()

# Ejecutar
asyncio.run(ejemplo_completo())
```

## Notas Importantes

1. La biblioteca utiliza WebSocket para mantener una conexión en tiempo real.
2. Las sesiones se almacenan localmente para evitar inicios de sesión innecesarios.
3. Todos los métodos que interactúan con la API son asíncronos y deben ser manejados con `async/await`.
4. Es recomendable implementar manejo de errores adecuado para las reconexiones.
5. La autenticación de dos factores (2FA) se maneja automáticamente si está configurada en la cuenta.