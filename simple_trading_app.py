#!/usr/bin/env python
"""
Simple Trading Application
Clean, thread-safe implementation with manual asset selection
"""

import sys
import os
import json
import time
import asyncio
from datetime import datetime
from PyQt5 import QtWidgets, QtCore, QtGui

# Import chart widget
try:
    from chart_widgets import CandlestickChart
except ImportError:
    print("Warning: chart_widgets not found, using placeholder")
    class CandlestickChart(QtWidgets.QWidget):
        def __init__(self, title="Chart", background='#1E222D'):
            super().__init__()
            self.setMinimumSize(600, 400)
            self.setStyleSheet(f"background-color: {background}; border: 1px solid #333;")
            layout = QtWidgets.QVBoxLayout(self)
            label = QtWidgets.QLabel(f"{title}\n(Chart widget not available)")
            label.setAlignment(QtCore.Qt.AlignCenter)
            label.setStyleSheet("color: white; font-size: 14px;")
            layout.addWidget(label)

        def set_candles_data(self, data):
            pass

class SimpleTradingApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()

        # Basic properties
        self.setWindowTitle("Simple Trading System")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize data
        self.selected_asset = None
        self.selected_asset_id = None
        self.api_client = None
        self.api_connected = False
        self.trading_active = False
        self.candles_data = []

        # Load available assets
        self.available_assets = self.load_assets()

        # Create UI
        self.create_ui()

        # Simple timer for updates (only when trading)
        self.update_timer = QtCore.QTimer(self)
        self.update_timer.timeout.connect(self.update_data)

    def load_assets(self):
        """Load available assets from JSON file"""
        try:
            if os.path.exists('quotex_assets.json'):
                with open('quotex_assets.json', 'r') as f:
                    assets = json.load(f)
                print(f"Loaded {len(assets)} assets")
                return assets
            else:
                # Default assets
                return {
                    "EURUSD": 1,
                    "GBPUSD": 56,
                    "USDJPY": 63,
                    "XAUUSD": 2,
                    "USDCAD": 61,
                    "AUDUSD": 40
                }
        except Exception as e:
            print(f"Error loading assets: {e}")
            return {"EURUSD": 1}

    def create_ui(self):
        """Create the user interface"""
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)

        # Header
        header_widget = QtWidgets.QWidget()
        header_layout = QtWidgets.QHBoxLayout(header_widget)

        # Title
        title = QtWidgets.QLabel("📈 Simple Trading System")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Asset selection
        header_layout.addWidget(QtWidgets.QLabel("Asset:"))
        self.asset_combo = QtWidgets.QComboBox()
        self.asset_combo.addItem("-- Select Asset --")
        for asset in sorted(self.available_assets.keys()):
            self.asset_combo.addItem(asset)
        self.asset_combo.currentTextChanged.connect(self.on_asset_selected)
        header_layout.addWidget(self.asset_combo)

        # Login button
        self.login_btn = QtWidgets.QPushButton("🔑 Login")
        self.login_btn.clicked.connect(self.login)
        header_layout.addWidget(self.login_btn)

        # Trading button
        self.trading_btn = QtWidgets.QPushButton("▶️ Start")
        self.trading_btn.setEnabled(False)
        self.trading_btn.clicked.connect(self.toggle_trading)
        header_layout.addWidget(self.trading_btn)

        main_layout.addWidget(header_widget)

        # Content area
        content_splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)

        # Chart area
        self.chart = CandlestickChart(title="Select asset to view chart")
        content_splitter.addWidget(self.chart)

        # Info panel
        info_widget = QtWidgets.QWidget()
        info_widget.setMaximumWidth(250)
        info_layout = QtWidgets.QVBoxLayout(info_widget)

        # Asset info
        info_layout.addWidget(QtWidgets.QLabel("Asset Info:"))
        self.asset_info = QtWidgets.QLabel("No asset selected")
        self.asset_info.setWordWrap(True)
        info_layout.addWidget(self.asset_info)

        # Connection status
        info_layout.addWidget(QtWidgets.QLabel("Connection:"))
        self.connection_status = QtWidgets.QLabel("Not connected")
        self.connection_status.setStyleSheet("color: red;")
        info_layout.addWidget(self.connection_status)

        # Trading status
        info_layout.addWidget(QtWidgets.QLabel("Trading:"))
        self.trading_status = QtWidgets.QLabel("Stopped")
        self.trading_status.setStyleSheet("color: orange;")
        info_layout.addWidget(self.trading_status)

        # Price display
        info_layout.addWidget(QtWidgets.QLabel("Latest Price:"))
        self.price_display = QtWidgets.QLabel("--")
        self.price_display.setStyleSheet("font-size: 16px; font-weight: bold;")
        info_layout.addWidget(self.price_display)

        info_layout.addStretch()
        content_splitter.addWidget(info_widget)

        main_layout.addWidget(content_splitter)

        # Status bar
        self.status_bar = self.statusBar()
        self.status_label = QtWidgets.QLabel("Ready")
        self.status_bar.addWidget(self.status_label)

    def on_asset_selected(self, asset_name):
        """Handle asset selection"""
        if asset_name == "-- Select Asset --":
            self.selected_asset = None
            self.selected_asset_id = None
            self.trading_btn.setEnabled(False)
            self.asset_info.setText("No asset selected")
            return

        if asset_name in self.available_assets:
            self.selected_asset = asset_name
            self.selected_asset_id = self.available_assets[asset_name]
            self.trading_btn.setEnabled(self.api_connected)
            self.asset_info.setText(f"Asset: {asset_name}\nID: {self.selected_asset_id}")
            self.status_label.setText(f"Selected: {asset_name}")

    def login(self):
        """Simple login dialog"""
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Login to Quotex")
        dialog.setModal(True)
        layout = QtWidgets.QVBoxLayout(dialog)

        # Email
        layout.addWidget(QtWidgets.QLabel("Email:"))
        email_input = QtWidgets.QLineEdit()
        layout.addWidget(email_input)

        # Password
        layout.addWidget(QtWidgets.QLabel("Password:"))
        password_input = QtWidgets.QLineEdit()
        password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        layout.addWidget(password_input)

        # Status
        status_label = QtWidgets.QLabel("Enter credentials")
        layout.addWidget(status_label)

        # Buttons
        button_layout = QtWidgets.QHBoxLayout()
        cancel_btn = QtWidgets.QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)

        login_btn = QtWidgets.QPushButton("Login")

        def attempt_login():
            email = email_input.text().strip()
            password = password_input.text().strip()

            if not email or not password:
                status_label.setText("Please enter email and password")
                return

            status_label.setText("Connecting...")
            login_btn.setEnabled(False)

            # Try to connect
            success = self.connect_api(email, password)

            if success:
                status_label.setText("✅ Connected!")
                QtCore.QTimer.singleShot(1000, dialog.accept)
            else:
                status_label.setText("❌ Login failed")
                login_btn.setEnabled(True)

        login_btn.clicked.connect(attempt_login)
        button_layout.addWidget(login_btn)
        layout.addLayout(button_layout)

        dialog.exec_()

    def connect_api(self, email, password):
        """Connect to Quotex API"""
        try:
            from quotexapi.stable_api import Quotex

            client = Quotex(email, password)

            # Simple synchronous connection
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def connect():
                return await asyncio.wait_for(client.connect(), timeout=30.0)

            connected = loop.run_until_complete(connect())
            loop.close()

            if connected:
                self.api_client = client
                self.api_connected = True
                self.connection_status.setText("Connected")
                self.connection_status.setStyleSheet("color: green;")
                self.login_btn.setText("✅ Connected")
                self.login_btn.setEnabled(False)
                if self.selected_asset:
                    self.trading_btn.setEnabled(True)
                return True

        except Exception as e:
            print(f"Connection error: {e}")

        return False

    def toggle_trading(self):
        """Start/stop trading"""
        if not self.trading_active:
            self.start_trading()
        else:
            self.stop_trading()

    def start_trading(self):
        """Start trading"""
        if not self.selected_asset or not self.api_connected:
            return

        self.trading_active = True
        self.trading_btn.setText("⏸️ Stop")
        self.trading_status.setText(f"Trading {self.selected_asset}")
        self.trading_status.setStyleSheet("color: green;")

        # Start timer for updates every 10 seconds
        self.update_timer.start(10000)

        # Fetch initial data
        self.fetch_data()

    def stop_trading(self):
        """Stop trading"""
        self.trading_active = False
        self.trading_btn.setText("▶️ Start")
        self.trading_status.setText("Stopped")
        self.trading_status.setStyleSheet("color: orange;")
        self.update_timer.stop()

    def fetch_data(self):
        """Fetch candle data"""
        if not self.api_client or not self.selected_asset:
            return

        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def get_candles():
                end_time = time.time()
                return await asyncio.wait_for(
                    self.api_client.get_candles(self.selected_asset, end_time, 50, 60),
                    timeout=10.0
                )

            candles = loop.run_until_complete(get_candles())
            loop.close()

            if candles:
                self.candles_data = candles
                self.update_chart()

                # Update price
                if candles:
                    latest = candles[-1]
                    price = latest.get('close', 0)
                    self.price_display.setText(f"{price:.5f}")

                print(f"Updated {self.selected_asset}: {len(candles)} candles")

        except Exception as e:
            print(f"Error fetching data: {e}")

    def update_chart(self):
        """Update chart with data"""
        if not self.candles_data:
            return

        try:
            # Convert to chart format
            chart_data = []
            for candle in self.candles_data:
                if isinstance(candle, dict):
                    chart_data.append({
                        'timestamp': candle.get('timestamp', candle.get('time', 0)),
                        'open': float(candle.get('open', 0)),
                        'high': float(candle.get('high', 0)),
                        'low': float(candle.get('low', 0)),
                        'close': float(candle.get('close', 0)),
                        'volume': float(candle.get('volume', 0))
                    })

            if chart_data:
                self.chart.set_candles_data(chart_data)

        except Exception as e:
            print(f"Error updating chart: {e}")

    def update_data(self):
        """Timer callback to update data"""
        if self.trading_active:
            self.fetch_data()

def main():
    """Main function"""
    print("🚀 Starting Simple Trading System...")

    app = QtWidgets.QApplication(sys.argv)
    app.setApplicationName("Simple Trading System")

    try:
        window = SimpleTradingApp()
        window.show()

        print("✅ Application started!")
        print("Instructions:")
        print("1. Select an asset")
        print("2. Click Login")
        print("3. Click Start to begin trading")

        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
