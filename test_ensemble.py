#!/usr/bin/env python3
"""
Test script to verify ensemble model functionality
"""

import sys
import os
import pandas as pd
import numpy as np

def test_tensorflow_availability():
    """Test if TensorFlow is available and working"""
    print("🔍 Testing TensorFlow availability...")
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow version: {tf.__version__}")

        # Test model creation
        test_model = tf.keras.Sequential([
            tf.keras.layers.Dense(10, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        print("✅ TensorFlow model creation works")
        return True
    except ImportError as e:
        print(f"❌ TensorFlow import error: {e}")
        return False
    except Exception as e:
        print(f"❌ TensorFlow test error: {e}")
        return False

def test_model_manager():
    """Test ModelManager initialization and ensemble creation"""
    print("\n🔍 Testing ModelManager...")
    try:
        from Models.model_manager import ModelManager

        # Initialize model manager
        manager = ModelManager(model_dir='models')
        print("✅ ModelManager initialized")

        # Load models
        manager.load_models()
        print(f"✅ Models loaded: {list(manager.models.keys())}")

        # Create ensemble
        manager.create_ensemble()
        if manager.ensemble:
            print("✅ Ensemble created successfully")
            print(f"   Model types: {manager.ensemble.model_types}")
            print(f"   Weights: {manager.ensemble.weights}")
            return True
        else:
            print("❌ Ensemble creation failed")
            return False

    except Exception as e:
        print(f"❌ ModelManager test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ensemble_prediction():
    """Test ensemble prediction functionality"""
    print("\n🔍 Testing Ensemble Predictions...")
    try:
        from Models.model_manager import ModelManager
        from Models.Feature_Engineering import engineer_features

        # Create sample data
        sample_data = []
        for i in range(50):
            sample_data.append({
                'timestamp': pd.Timestamp.now() - pd.Timedelta(minutes=i),
                'open': 1.12 + np.random.normal(0, 0.001),
                'high': 1.12 + np.random.normal(0, 0.001) + 0.0005,
                'low': 1.12 + np.random.normal(0, 0.001) - 0.0005,
                'close': 1.12 + np.random.normal(0, 0.001),
                'volume': np.random.randint(100, 1000)
            })

        df = pd.DataFrame(sample_data)
        print(f"✅ Created sample data with {len(df)} rows")

        # Engineer features
        features_df = engineer_features(df)
        print(f"✅ Engineered features: {list(features_df.columns)}")

        # Initialize model manager
        manager = ModelManager(model_dir='models')
        manager.load_models()
        manager.create_ensemble()

        if manager.ensemble:
            # Prepare data for ensemble
            X = {}

            # For tabular models
            if len(features_df) > 0:
                # Select only numeric columns for tabular models
                numeric_features = features_df.select_dtypes(include=[np.number])
                latest_features = numeric_features.iloc[-1:].copy()
                X['tabular_data'] = latest_features
                print(f"✅ Prepared tabular data: {latest_features.shape} (columns: {len(latest_features.columns)})")

            # For sequence models
            if len(features_df) >= 30:
                # Select only numeric columns and convert to float32
                numeric_features = features_df.select_dtypes(include=[np.number])
                sequence_data = numeric_features.iloc[-30:].values.astype(np.float32).reshape(1, 30, -1)

                # Build models with correct input shape if they don't exist or have wrong shape
                n_features = sequence_data.shape[2]
                print(f"Detected {n_features} features for sequence models")

                # Use the model manager's rebuild function to fix shape mismatches
                manager.rebuild_models_for_features(n_features)

                # Test if models can handle the input shape now
                if 'lstm_gru' in manager.models:
                    try:
                        test_pred = manager.models['lstm_gru']['model'].predict(sequence_data, verbose=0)
                        print(f"✅ LSTM model can handle input shape: {sequence_data.shape}")
                    except Exception as e:
                        print(f"⚠️ LSTM model still has issues: {e}")

                if 'transformer' in manager.models:
                    try:
                        test_pred = manager.models['transformer']['model'].predict(sequence_data, verbose=0)
                        print(f"✅ Transformer model can handle input shape: {sequence_data.shape}")
                    except Exception as e:
                        print(f"⚠️ Transformer model still has issues: {e}")

                X['sequence_data'] = sequence_data
                print(f"✅ Prepared sequence data: {sequence_data.shape} (dtype: {sequence_data.dtype})")

            # For DQN models
            if len(features_df) >= 30:
                # Select only numeric columns for DQN state
                numeric_features = features_df.select_dtypes(include=[np.number])

                # DQN expects market features as (30, 10) - 30 time steps, 10 features each
                if len(numeric_features.columns) >= 10:
                    # Use the last 30 rows and first 10 features
                    market_sequence = numeric_features.iloc[-30:, :10].values.astype(np.float32)
                else:
                    # Pad with zeros if we don't have enough features
                    market_data = numeric_features.iloc[-30:].values.astype(np.float32)
                    # Pad to 10 features
                    padding = np.zeros((30, 10 - market_data.shape[1]), dtype=np.float32)
                    market_sequence = np.concatenate([market_data, padding], axis=1)

                portfolio_state = np.array([1.0, 0.0, 0.0], dtype=np.float32)  # [balance, position, profit]

                X['state'] = {
                    'market_features': market_sequence,  # Shape: (30, 10)
                    'portfolio_state': portfolio_state   # Shape: (3,)
                }
                print(f"✅ Prepared DQN state data (market: {market_sequence.shape}, portfolio: {portfolio_state.shape})")

                # Test DQN model if available
                if 'dqn' in manager.models:
                    try:
                        # Test if the DQN model can handle the input
                        dqn_agent = manager.models['dqn']['agent']
                        if hasattr(dqn_agent, 'model') and dqn_agent.model is not None:
                            # Test prediction with correct input format
                            test_market = np.expand_dims(market_sequence, axis=0)  # Shape: (1, 30, 10)
                            test_portfolio = np.expand_dims(portfolio_state, axis=0)  # Shape: (1, 3)
                            test_pred = dqn_agent.model.predict([test_market, test_portfolio], verbose=0)
                            print(f"✅ DQN model can handle input shapes: market {test_market.shape}, portfolio {test_portfolio.shape}")
                    except Exception as e:
                        print(f"⚠️ DQN model test failed: {e}")
                        # The model will be handled by the ensemble's error handling

            # Recreate ensemble with updated models
            manager.create_ensemble()

            # Make prediction
            result = manager.ensemble.predict(X, return_individual=True)
            print(f"✅ Ensemble prediction successful:")
            print(f"   Prediction: {result['prediction']}")
            print(f"   Confidence: {result['confidence']:.4f}")
            print(f"   Probability: {result['probability']:.4f}")

            if 'individual_predictions' in result:
                print(f"   Individual predictions: {len(result['individual_predictions'])}")
                for i, pred in enumerate(result['individual_predictions']):
                    print(f"     Model {i}: {pred}")

            return True
        else:
            print("❌ No ensemble available for testing")
            return False

    except Exception as e:
        print(f"❌ Ensemble prediction test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Starting Ensemble Model Tests")
    print("=" * 50)

    # Test 1: TensorFlow availability
    tf_available = test_tensorflow_availability()

    # Test 2: ModelManager
    manager_works = test_model_manager()

    # Test 3: Ensemble predictions
    prediction_works = test_ensemble_prediction()

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   TensorFlow Available: {'✅' if tf_available else '❌'}")
    print(f"   ModelManager Works: {'✅' if manager_works else '❌'}")
    print(f"   Ensemble Predictions: {'✅' if prediction_works else '❌'}")

    if tf_available and manager_works and prediction_works:
        print("\n🎉 All tests passed! Ensemble is ready for use.")
        return True
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
