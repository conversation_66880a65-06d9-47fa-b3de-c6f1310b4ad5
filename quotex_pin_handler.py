#!/usr/bin/env python
"""
Quotex PIN Handler
This module provides a direct way to handle PIN verification for the Quotex API
"""

import sys
import time
import asyncio
import builtins

class QuotexPINHandler:
    """Handler for Quotex PIN verification"""

    def __init__(self, client, pin=None):
        """Initialize the PIN handler"""
        self.client = client
        self.pin = pin
        self.original_input = builtins.input

    def set_pin(self, pin):
        """Set the PIN"""
        self.pin = pin

    def mock_input(self, prompt):
        """Mock the input function to return the PIN"""
        print(f"PIN prompt: {prompt}")
        if self.pin:
            print(f"Automatically entering PIN: {self.pin}")
            return self.pin
        else:
            # If no PIN is set, use the original input function
            return self.original_input(prompt)

    async def connect_with_pin(self, timeout=30.0):
        """Connect to Quotex API with PIN verification"""
        # Replace the input function
        builtins.input = self.mock_input

        try:
            # Try to connect with timeout
            try:
                # Set a timeout
                print(f"Connecting with PIN and {timeout} second timeout...")
                return await asyncio.wait_for(self.client.connect(), timeout=timeout)
            except asyncio.TimeoutError:
                print(f"Connection timed out after {timeout} seconds")
                return False
        finally:
            # Restore the original input function
            builtins.input = self.original_input

    async def connect_with_retry(self, max_retries=3, timeout=30.0):
        """Connect to Quotex API with PIN verification and retry"""
        for attempt in range(1, max_retries + 1):
            print(f"Connection attempt {attempt} of {max_retries}...")

            try:
                # Try to connect with PIN
                result = await self.connect_with_pin(timeout=timeout)

                if result:
                    print("Connection successful")
                    return True
                else:
                    print(f"Connection attempt {attempt} failed")

                    # Wait before retrying
                    if attempt < max_retries:
                        wait_time = 2 * attempt  # Exponential backoff
                        print(f"Waiting {wait_time} seconds before retrying...")
                        # Use time.sleep instead of asyncio.sleep to avoid event loop issues
                        import time
                        time.sleep(wait_time)
            except Exception as e:
                print(f"Error in connection attempt {attempt}: {e}")

                # Wait before retrying
                if attempt < max_retries:
                    wait_time = 2 * attempt  # Exponential backoff
                    print(f"Waiting {wait_time} seconds before retrying...")
                    # Use time.sleep instead of asyncio.sleep to avoid event loop issues
                    import time
                    time.sleep(wait_time)

        print(f"Failed to connect after {max_retries} attempts")
        return False

    def connect_sync(self, max_retries=3, timeout=30.0):
        """Synchronous version of connect_with_retry to avoid event loop issues"""
        import asyncio

        # Create a new event loop
        loop = asyncio.new_event_loop()

        try:
            # Run the async function in the new loop
            return loop.run_until_complete(self.connect_with_retry(max_retries, timeout))
        finally:
            # Close the loop
            loop.close()
