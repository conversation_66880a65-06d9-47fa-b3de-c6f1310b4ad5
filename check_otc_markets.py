#!/usr/bin/env python
"""
OTC Market Checker for Quotex

This script connects to the Quotex API and specifically checks for OTC markets
like usdars_otc and usdbrl_otc. It displays information about each OTC market
including whether it's open and its payout percentage.

It also provides functionality to test if a specific market is available
and retrieve sample candle data.
"""

import os
import sys
import asyncio
import configparser
from datetime import datetime, timedelta
import time
import pandas as pd
from quotexapi.stable_api import Quotex

# ASCII art banner
print("=" * 60)
print("QUOTEX OTC MARKET CHECKER")
print("=" * 60)

# Load credentials from config.ini
config = configparser.RawConfigParser()
config_path = os.path.join('settings', 'config.ini')
if os.path.exists(config_path):
    config.read(config_path)
    email = config.get('settings', 'email', fallback=None)
    password = config.get('settings', 'password', fallback=None)
else:
    email = None
    password = None

# Initialize Quotex client
client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

async def list_otc_markets():
    """List all available OTC markets with detailed information"""
    print("\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully. Retrieving OTC markets...\n")

        # Get all assets data (includes open/closed status and payout info)
        all_data = client.get_payment()

        # Get all asset codes
        codes_asset = await client.get_all_assets()

        # Create a mapping from display name to code
        asset_codes = {}
        for code, name in codes_asset.items():
            asset_codes[name] = code

        # Filter for OTC markets only
        otc_markets = {name: data for name, data in all_data.items() if "_otc" in name.lower()}

        # Print OTC assets
        print("-" * 80)
        print(f"{'Market Name':<30} {'Market Code':<20} {'Status':<10} {'Payout 1M':<10} {'Payout 5M':<10}")
        print("-" * 80)

        # Count statistics
        total_otc_markets = len(otc_markets)
        open_otc_markets = sum(1 for data in otc_markets.values() if data["open"])

        # Sort assets and display them
        for asset_name in sorted(otc_markets.keys()):
            asset_data = otc_markets[asset_name]
            code = asset_codes.get(asset_name, "Unknown")
            status = "🟢 Open" if asset_data["open"] else "🔴 Closed"
            profit_1m = f"{asset_data['profit']['1M']}%" if '1M' in asset_data['profit'] else "N/A"
            profit_5m = f"{asset_data['profit']['5M']}%" if '5M' in asset_data['profit'] else "N/A"
            
            print(f"{asset_name:<30} {code:<20} {status:<10} {profit_1m:<10} {profit_5m:<10}")

        print("-" * 80)
        print(f"Total OTC Markets: {total_otc_markets} (Open: {open_otc_markets}, Closed: {total_otc_markets - open_otc_markets})")
        print("-" * 80)

        # Return the OTC markets data for further use
        return otc_markets, asset_codes
    else:
        print(f"Connection failed: {message}")
        return None, None

async def test_market_availability(market_name):
    """Test if a specific market is available and retrieve sample candle data"""
    print(f"\nTesting availability of {market_name}...")
    
    # Check if asset is open
    asset_name, asset_data = await client.get_available_asset(market_name, force_open=True)
    
    print(f"Asset name: {asset_name}")
    print(f"Asset data: {asset_data}")
    
    if not asset_data or not asset_data[2]:
        print(f"WARNING: Market {market_name} appears to be closed or unavailable.")
        return False, None
    
    print(f"Market {market_name} is OPEN and available for trading.")
    
    # Try to get some candle data
    print(f"\nRetrieving sample candle data for {market_name}...")
    try:
        end_from_time = time.time()
        candles = await client.get_candles(market_name, end_from_time, 10, 60)  # Get 10 1-minute candles
        
        if candles and len(candles) > 0:
            print(f"Successfully retrieved {len(candles)} candles.")
            
            # Convert to DataFrame for better display
            df = pd.DataFrame(candles)
            
            # Add human-readable timestamp
            df['timestamp'] = df['time'].apply(lambda x: datetime.fromtimestamp(x).strftime('%Y-%m-%d %H:%M:%S'))
            
            # Add color
            df['color'] = df.apply(lambda row: 'green' if row['close'] >= row['open'] else 'red', axis=1)
            
            # Display sample data
            print("\nSample candle data:")
            print(df[['timestamp', 'open', 'high', 'low', 'close', 'color']].head())
            
            return True, df
        else:
            print("No candle data retrieved.")
            return False, None
    except Exception as e:
        print(f"Error retrieving candle data: {e}")
        return False, None

async def interactive_menu():
    """Interactive menu for testing OTC markets"""
    otc_markets, asset_codes = await list_otc_markets()
    
    if not otc_markets:
        print("No OTC markets data available. Exiting.")
        return
    
    while True:
        print("\n" + "=" * 60)
        print("OTC MARKET CHECKER - MENU")
        print("=" * 60)
        print("1. List all OTC markets again")
        print("2. Test specific OTC market availability")
        print("3. Test all OTC markets availability")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            await list_otc_markets()
        
        elif choice == '2':
            market = input("\nEnter market name to test (e.g., USDARS_otc): ").strip().upper()
            if not market:
                print("No market specified.")
                continue
                
            if "_OTC" not in market:
                market = f"{market}_OTC"
                
            success, candles = await test_market_availability(market)
            
            if success and candles is not None:
                save_option = input("\nDo you want to save this candle data to CSV? (y/n): ").strip().lower()
                if save_option == 'y':
                    filename = f"{market.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_candles.csv"
                    candles.to_csv(filename, index=False)
                    print(f"Data saved to {filename}")
        
        elif choice == '3':
            print("\nTesting all OTC markets availability...")
            available_markets = []
            
            for market in sorted(otc_markets.keys()):
                if otc_markets[market]["open"]:
                    print(f"\n{'-'*40}")
                    success, _ = await test_market_availability(market)
                    if success:
                        available_markets.append(market)
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(1)
            
            print("\n" + "=" * 60)
            print("SUMMARY OF AVAILABLE OTC MARKETS")
            print("=" * 60)
            if available_markets:
                for market in available_markets:
                    print(f"✅ {market}")
                print(f"\nTotal available OTC markets: {len(available_markets)}/{len(otc_markets)}")
            else:
                print("No OTC markets are currently available.")
        
        elif choice == '4':
            print("\nExiting program...")
            break
        
        else:
            print("\nInvalid choice. Please try again.")

async def main():
    try:
        print("\nConnecting to Quotex API...")
        check_connect, message = await client.connect()

        if check_connect:
            print("Connected successfully.")
            
            # Select account type
            account_type = input("Select account type (demo or real, default: demo): ").lower()
            if account_type == "real":
                print("Real account selected")
                client.set_account_mode("REAL")
            else:
                print("Demo account selected")
                client.set_account_mode("PRACTICE")  # Default is PRACTICE/DEMO
            
            await interactive_menu()
        else:
            print(f"Connection failed: {message}")
    
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")
    finally:
        # Ensure client is closed
        try:
            await client.close()
        except:
            pass
        print("\nExiting...")

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
