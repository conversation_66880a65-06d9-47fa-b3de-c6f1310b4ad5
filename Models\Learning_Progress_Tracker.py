"""
Learning Progress Tracker

This module tracks the learning progress of the trading system over time,
providing insights into how the models are improving day by day.
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import pickle

class LearningProgressTracker:
    """
    Tracks the learning progress of the trading system over time.
    """
    def __init__(self, save_dir='learning_progress'):
        """
        Initialize the learning progress tracker.
        
        Parameters:
        - save_dir: Directory to save progress data
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Initialize progress data
        self.daily_progress = {}
        self.model_weights_history = {}
        self.accuracy_history = []
        self.trade_history = []
        self.learning_iterations = 0
        
        # Load existing progress if available
        self.load_progress()
    
    def record_trade(self, prediction, actual, confidence, model_name, timestamp=None):
        """
        Record a trade result for tracking.
        
        Parameters:
        - prediction: Predicted direction (1 for UP, 0 for DOWN)
        - actual: Actual direction (1 for UP, 0 for DOWN)
        - confidence: Confidence level of the prediction
        - model_name: Name of the model that made the prediction
        - timestamp: Timestamp of the trade (default: current time)
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        # Create trade record
        trade = {
            'timestamp': timestamp,
            'prediction': prediction,
            'actual': actual,
            'correct': prediction == actual,
            'confidence': confidence,
            'model': model_name
        }
        
        # Add to trade history
        self.trade_history.append(trade)
        
        # Update daily progress
        date_key = timestamp.strftime('%Y-%m-%d')
        if date_key not in self.daily_progress:
            self.daily_progress[date_key] = {
                'trades': 0,
                'correct': 0,
                'accuracy': 0.0,
                'avg_confidence': 0.0,
                'models': {}
            }
        
        # Update daily stats
        daily = self.daily_progress[date_key]
        daily['trades'] += 1
        daily['correct'] += 1 if trade['correct'] else 0
        daily['accuracy'] = daily['correct'] / daily['trades']
        
        # Update confidence
        prev_avg = daily['avg_confidence']
        daily['avg_confidence'] = (prev_avg * (daily['trades'] - 1) + confidence) / daily['trades']
        
        # Update model-specific stats
        if model_name not in daily['models']:
            daily['models'][model_name] = {
                'trades': 0,
                'correct': 0,
                'accuracy': 0.0,
                'avg_confidence': 0.0
            }
        
        model_stats = daily['models'][model_name]
        model_stats['trades'] += 1
        model_stats['correct'] += 1 if trade['correct'] else 0
        model_stats['accuracy'] = model_stats['correct'] / model_stats['trades']
        
        prev_avg = model_stats['avg_confidence']
        model_stats['avg_confidence'] = (prev_avg * (model_stats['trades'] - 1) + confidence) / model_stats['trades']
        
        # Save progress
        self.save_progress()
        
        return trade['correct']
    
    def record_learning_iteration(self, metrics, model_weights=None):
        """
        Record a learning iteration with performance metrics.
        
        Parameters:
        - metrics: Dictionary of performance metrics
        - model_weights: Dictionary of model weights
        """
        timestamp = datetime.now()
        
        # Create learning record
        record = {
            'timestamp': timestamp,
            'metrics': metrics,
            'iteration': self.learning_iterations + 1
        }
        
        # Add to accuracy history
        self.accuracy_history.append(record)
        
        # Update model weights history if provided
        if model_weights:
            self.model_weights_history[timestamp.strftime('%Y-%m-%d %H:%M:%S')] = model_weights
        
        # Increment learning iterations
        self.learning_iterations += 1
        
        # Save progress
        self.save_progress()
    
    def get_daily_summary(self, days=7):
        """
        Get a summary of daily progress for the last N days.
        
        Parameters:
        - days: Number of days to include in the summary
        
        Returns:
        - summary: Dictionary of daily summaries
        """
        # Get dates for the last N days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days-1)
        
        # Create date range
        date_range = [start_date + timedelta(days=i) for i in range(days)]
        date_keys = [date.strftime('%Y-%m-%d') for date in date_range]
        
        # Create summary
        summary = {}
        for date_key in date_keys:
            if date_key in self.daily_progress:
                summary[date_key] = self.daily_progress[date_key]
            else:
                summary[date_key] = {
                    'trades': 0,
                    'correct': 0,
                    'accuracy': 0.0,
                    'avg_confidence': 0.0,
                    'models': {}
                }
        
        return summary
    
    def get_improvement_metrics(self, days=7):
        """
        Calculate improvement metrics over the specified period.
        
        Parameters:
        - days: Number of days to analyze
        
        Returns:
        - metrics: Dictionary of improvement metrics
        """
        summary = self.get_daily_summary(days)
        
        # Extract daily accuracies
        dates = list(summary.keys())
        accuracies = [summary[date]['accuracy'] if summary[date]['trades'] > 0 else 0 for date in dates]
        
        # Calculate improvement metrics
        metrics = {
            'start_accuracy': accuracies[0] if accuracies else 0,
            'end_accuracy': accuracies[-1] if accuracies else 0,
            'max_accuracy': max(accuracies) if accuracies else 0,
            'avg_accuracy': sum(accuracies) / len(accuracies) if accuracies else 0,
            'improvement': 0,
            'daily_improvement': 0,
            'trend': 'stable'
        }
        
        # Calculate improvement
        if len(accuracies) > 1 and accuracies[0] > 0:
            metrics['improvement'] = (accuracies[-1] - accuracies[0]) / accuracies[0] * 100
            metrics['daily_improvement'] = metrics['improvement'] / (len(accuracies) - 1)
        
        # Determine trend
        if metrics['improvement'] > 5:
            metrics['trend'] = 'improving'
        elif metrics['improvement'] < -5:
            metrics['trend'] = 'declining'
        
        return metrics
    
    def plot_learning_progress(self, save_path=None):
        """
        Plot learning progress over time.
        
        Parameters:
        - save_path: Path to save the plot (default: None, display only)
        
        Returns:
        - fig: Matplotlib figure object
        """
        # Create figure with multiple subplots
        fig, axs = plt.subplots(2, 1, figsize=(12, 10))
        
        # Plot 1: Daily accuracy
        summary = self.get_daily_summary(30)  # Last 30 days
        dates = list(summary.keys())
        accuracies = [summary[date]['accuracy'] if summary[date]['trades'] > 0 else None for date in dates]
        trades = [summary[date]['trades'] for date in dates]
        
        ax1 = axs[0]
        ax1.plot(dates, accuracies, 'o-', color='blue', label='Daily Accuracy')
        ax1.set_title('Daily Trading Accuracy')
        ax1.set_xlabel('Date')
        ax1.set_ylabel('Accuracy')
        ax1.set_ylim(0, 1)
        ax1.grid(True)
        
        # Add trade count as bar chart on secondary axis
        ax1_2 = ax1.twinx()
        ax1_2.bar(dates, trades, alpha=0.3, color='gray', label='Trade Count')
        ax1_2.set_ylabel('Number of Trades')
        
        # Combine legends
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax1_2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        # Plot 2: Model weights over time
        if self.model_weights_history:
            ax2 = axs[1]
            
            # Convert weights history to DataFrame
            weights_data = []
            for timestamp, weights in self.model_weights_history.items():
                for model, weight in weights.items():
                    weights_data.append({
                        'timestamp': timestamp,
                        'model': model,
                        'weight': weight
                    })
            
            weights_df = pd.DataFrame(weights_data)
            
            # Plot each model's weight
            for model in weights_df['model'].unique():
                model_data = weights_df[weights_df['model'] == model]
                ax2.plot(model_data['timestamp'], model_data['weight'], 'o-', label=model)
            
            ax2.set_title('Model Weights Evolution')
            ax2.set_xlabel('Time')
            ax2.set_ylabel('Weight')
            ax2.legend()
            ax2.grid(True)
        
        # Adjust layout
        plt.tight_layout()
        
        # Save or display
        if save_path:
            plt.savefig(save_path)
        
        return fig
    
    def generate_progress_report(self):
        """
        Generate a comprehensive progress report.
        
        Returns:
        - report: Dictionary containing the progress report
        """
        # Get improvement metrics
        short_term = self.get_improvement_metrics(7)  # Last week
        long_term = self.get_improvement_metrics(30)  # Last month
        
        # Calculate overall stats
        total_trades = len(self.trade_history)
        correct_trades = sum(1 for trade in self.trade_history if trade['correct'])
        overall_accuracy = correct_trades / total_trades if total_trades > 0 else 0
        
        # Calculate recent performance (last 50 trades)
        recent_trades = self.trade_history[-50:] if len(self.trade_history) >= 50 else self.trade_history
        recent_correct = sum(1 for trade in recent_trades if trade['correct'])
        recent_accuracy = recent_correct / len(recent_trades) if recent_trades else 0
        
        # Calculate model performance
        model_performance = {}
        for trade in self.trade_history:
            model = trade['model']
            if model not in model_performance:
                model_performance[model] = {
                    'trades': 0,
                    'correct': 0,
                    'accuracy': 0.0
                }
            
            model_performance[model]['trades'] += 1
            model_performance[model]['correct'] += 1 if trade['correct'] else 0
            model_performance[model]['accuracy'] = (
                model_performance[model]['correct'] / model_performance[model]['trades']
            )
        
        # Create report
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'overall': {
                'total_trades': total_trades,
                'correct_trades': correct_trades,
                'accuracy': overall_accuracy,
                'learning_iterations': self.learning_iterations
            },
            'recent': {
                'trades': len(recent_trades),
                'accuracy': recent_accuracy,
                'improvement': (recent_accuracy - overall_accuracy) / overall_accuracy * 100 if overall_accuracy > 0 else 0
            },
            'short_term': short_term,
            'long_term': long_term,
            'model_performance': model_performance,
            'daily_summary': self.get_daily_summary(7)
        }
        
        return report
    
    def save_progress(self):
        """
        Save progress data to disk.
        """
        # Create data to save
        data = {
            'daily_progress': self.daily_progress,
            'model_weights_history': self.model_weights_history,
            'accuracy_history': self.accuracy_history,
            'trade_history': self.trade_history,
            'learning_iterations': self.learning_iterations,
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save to file
        with open(os.path.join(self.save_dir, 'learning_progress.pkl'), 'wb') as f:
            pickle.dump(data, f)
        
        # Save report as JSON
        report = self.generate_progress_report()
        with open(os.path.join(self.save_dir, 'progress_report.json'), 'w') as f:
            json.dump(report, f, indent=4)
    
    def load_progress(self):
        """
        Load progress data from disk.
        """
        filepath = os.path.join(self.save_dir, 'learning_progress.pkl')
        if os.path.exists(filepath):
            try:
                with open(filepath, 'rb') as f:
                    data = pickle.load(f)
                
                self.daily_progress = data.get('daily_progress', {})
                self.model_weights_history = data.get('model_weights_history', {})
                self.accuracy_history = data.get('accuracy_history', [])
                self.trade_history = data.get('trade_history', [])
                self.learning_iterations = data.get('learning_iterations', 0)
                
                print(f"Loaded learning progress data from {filepath}")
                print(f"Total trades: {len(self.trade_history)}")
                print(f"Learning iterations: {self.learning_iterations}")
            except Exception as e:
                print(f"Error loading learning progress data: {e}")
