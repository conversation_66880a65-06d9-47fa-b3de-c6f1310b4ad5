#!/usr/bin/env python
"""
Test script for Quotex Direct Connector
This script tests the direct connector for Quotex API
"""

import sys
from PyQt5 import QtWidgets, QtCore
from quotex_direct_connector import QuotexDirectConnector

class TestDirectConnectorApp(QtWidgets.QWidget):
    """Test application for Quotex direct connector"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.connector = QuotexDirectConnector()
        
        # Connect signals
        self.connector.connection_started.connect(self.on_connection_started)
        self.connector.connection_success.connect(self.on_connection_success)
        self.connector.connection_failed.connect(self.on_connection_failed)
        
    def init_ui(self):
        """Initialize the UI"""
        self.setWindowTitle("Quotex Direct Connector Test")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        
        # Create layout
        layout = QtWidgets.QVBoxLayout(self)
        
        # Add header
        header_label = QtWidgets.QLabel("Quotex Direct Connector Test")
        header_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3;")
        header_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # Add instructions
        instructions = QtWidgets.QLabel(
            "This application tests the direct connector for Quotex API.\n\n"
            "1. Click 'Connect to API' to start the connection process\n"
            "2. Enter your email and password in the dialog\n"
            "3. If PIN verification is required, enter the PIN in the dialog\n"
            "4. If successful, the application will connect to the Quotex API"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Add status label
        self.status_label = QtWidgets.QLabel("Ready to test")
        self.status_label.setStyleSheet("color: blue;")
        layout.addWidget(self.status_label)
        
        # Add console output
        self.console = QtWidgets.QTextEdit()
        self.console.setReadOnly(True)
        self.console.setStyleSheet("background-color: #000; color: #0f0; font-family: monospace;")
        layout.addWidget(self.console)
        
        # Add buttons
        button_layout = QtWidgets.QHBoxLayout()
        
        # Connect button
        self.connect_button = QtWidgets.QPushButton("Connect to API")
        self.connect_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.connect_button.clicked.connect(self.connect_to_api)
        
        # Set Credentials button
        self.credentials_button = QtWidgets.QPushButton("Set Credentials")
        self.credentials_button.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        self.credentials_button.clicked.connect(self.set_credentials)
        
        # Exit button
        self.exit_button = QtWidgets.QPushButton("Exit")
        self.exit_button.clicked.connect(self.close)
        
        button_layout.addWidget(self.connect_button)
        button_layout.addWidget(self.credentials_button)
        button_layout.addWidget(self.exit_button)
        
        layout.addLayout(button_layout)
        
        # Show the window
        self.show()
        
    def log(self, message):
        """Add a message to the console"""
        import time
        self.console.append(f"{time.strftime('%H:%M:%S')} - {message}")
        self.console.verticalScrollBar().setValue(self.console.verticalScrollBar().maximum())
        
    def connect_to_api(self):
        """Connect to Quotex API"""
        self.log("Connecting to Quotex API...")
        self.status_label.setText("Connecting to Quotex API...")
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        
        # Disable connect button
        self.connect_button.setEnabled(False)
        
        # Connect to API
        self.connector.connect_to_api(self)
        
    def set_credentials(self):
        """Set API credentials"""
        self.log("Setting API credentials...")
        self.connector.show_credentials_dialog(self)
        
    def on_connection_started(self):
        """Handle connection started signal"""
        self.log("Connection process started...")
        
    def on_connection_success(self, client):
        """Handle connection success signal"""
        self.log("Successfully connected to Quotex API")
        self.status_label.setText("Connected to Quotex API")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        
        # Enable connect button
        self.connect_button.setEnabled(True)
        
        # Show success message
        QtWidgets.QMessageBox.information(
            self,
            "Connection Successful",
            "Successfully connected to Quotex API.\n\n"
            "You can now use the API client for data retrieval."
        )
        
    def on_connection_failed(self, error_message):
        """Handle connection failed signal"""
        self.log(f"Connection failed: {error_message}")
        self.status_label.setText(f"Connection failed: {error_message}")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        
        # Enable connect button
        self.connect_button.setEnabled(True)
        
        # Show error message
        QtWidgets.QMessageBox.critical(
            self,
            "Connection Failed",
            f"Failed to connect to Quotex API.\n\n"
            f"Error: {error_message}\n\n"
            f"Please check your credentials and try again."
        )

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    ex = TestDirectConnectorApp()
    sys.exit(app.exec_())
