import numpy as np
import pandas as pd
import ta
from ta.volatility import BollingerBands, AverageTrueRange
from ta.trend import MACD, SMAIndicator, EMAIndicator, ADXIndicator, CCIIndicator, PSARIndicator
from ta.momentum import RSIIndicator, StochasticOscillator, WilliamsRIndicator, ROCIndicator
from ta.volume import MFIIndicator, OnBalanceVolumeIndicator
from ta.others import DailyReturnIndicator
from scipy import stats
import warnings
from datetime import datetime, timedelta

# Suppress warnings
warnings.filterwarnings('ignore')

def engineer_features(df, min_periods=5):
    """
    Engineer features for prediction models with safeguards for small datasets

    Parameters:
    - df: DataFrame with candle data
    - min_periods: Minimum periods for rolling calculations (default: 5)

    Returns:
    - DataFrame with engineered features
    """
    # Make a copy to avoid modifying the original
    df_copy = df.copy()

    # Convert time column to proper datetime if it exists
    if 'time' in df_copy.columns:
        # Check if time is already a datetime
        if not pd.api.types.is_datetime64_any_dtype(df_copy['time']):
            # Check if time is numeric (timestamp)
            if pd.api.types.is_numeric_dtype(df_copy['time']):
                # Convert from timestamp to datetime
                df_copy['time'] = pd.to_datetime(df_copy['time'], unit='s')
            else:
                # Try to parse as string datetime
                try:
                    df_copy['time'] = pd.to_datetime(df_copy['time'])
                except:
                    # If parsing fails, create a simple index
                    print("Warning: Could not parse time column, using index instead")
                    df_copy['time'] = pd.to_datetime(pd.date_range(start='2023-01-01', periods=len(df_copy), freq='1min'))

    # Ensure we have numeric columns
    for col in ['open', 'high', 'low', 'close']:
        if col in df_copy.columns:
            df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

    # Get the number of available candles
    n_candles = len(df_copy)

    # Adjust lookback periods based on available data
    lookback_periods = min(21, n_candles - 1)  # Use at most 21 or n_candles-1, whichever is smaller

    # Basic price features
    if all(col in df_copy.columns for col in ['open', 'high', 'low', 'close']):
        # Calculate returns
        df_copy['returns'] = df_copy['close'].pct_change()

        # Calculate Simple Moving Averages (SMA) - using the exact names expected by models
        sma_periods = [5, 10]

        for period in sma_periods:
            if period <= lookback_periods:
                df_copy[f'sma_{period}'] = df_copy['close'].rolling(window=period, min_periods=min(min_periods, period)).mean()

                # Calculate close to SMA ratios
                df_copy[f'close_sma_{period}_ratio'] = df_copy['close'] / df_copy[f'sma_{period}']

        # Calculate candle features that models expect
        # Candle size (high-low range as percentage of open)
        df_copy['candle_size'] = ((df_copy['high'] - df_copy['low']) / df_copy['open'] * 100).fillna(0)

        # Body size (close-open as percentage of open)
        df_copy['body_size'] = (np.abs(df_copy['close'] - df_copy['open']) / df_copy['open'] * 100).fillna(0)

        # Upper shadow (high - max(open,close) as percentage of open)
        df_copy['upper_shadow'] = ((df_copy['high'] - np.maximum(df_copy['open'], df_copy['close'])) / df_copy['open'] * 100).fillna(0)

        # Lower shadow (min(open,close) - low as percentage of open)
        df_copy['lower_shadow'] = ((np.minimum(df_copy['open'], df_copy['close']) - df_copy['low']) / df_copy['open'] * 100).fillna(0)

        # Color numeric (1 for bullish/green, 0 for bearish/red)
        df_copy['color_numeric'] = (df_copy['close'] > df_copy['open']).astype(int)

        # Calculate RSI with adjusted period (optional - not in required features but useful)
        rsi_period = min(14, lookback_periods)
        if rsi_period > 1:
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period, min_periods=min(min_periods, rsi_period)).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period, min_periods=min(min_periods, rsi_period)).mean()

            # Avoid division by zero
            rs = gain / loss.replace(0, np.nan)
            df_copy['rsi'] = 100 - (100 / (1 + rs))
            df_copy['rsi'].fillna(50, inplace=True)  # Fill NaN with neutral value

        # Calculate MACD with adjusted periods (optional - not in required features but useful)
        fast_period = min(12, lookback_periods)
        slow_period = min(26, lookback_periods)

        if fast_period > 0 and slow_period > fast_period:
            ema_fast = df_copy['close'].ewm(span=fast_period, min_periods=min(min_periods, fast_period)).mean()
            ema_slow = df_copy['close'].ewm(span=slow_period, min_periods=min(min_periods, slow_period)).mean()
            df_copy['macd'] = ema_fast - ema_slow

            # MACD signal line
            signal_period = min(9, lookback_periods)
            if signal_period > 0:
                df_copy['macd_signal'] = df_copy['macd'].ewm(span=signal_period, min_periods=min(min_periods, signal_period)).mean()
                df_copy['macd_hist'] = df_copy['macd'] - df_copy['macd_signal']

    # Fill NaN values using modern pandas methods
    df_copy = df_copy.bfill()  # backward fill
    df_copy = df_copy.ffill()  # forward fill
    df_copy = df_copy.fillna(0)  # fill remaining NaN with 0

    return df_copy
