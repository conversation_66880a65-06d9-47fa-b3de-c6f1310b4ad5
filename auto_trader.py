#!/usr/bin/env python
"""
Advanced Auto Trader with Integrated Models, Self-Learning, and Future Candle Prediction

This script integrates multiple machine learning models (LSTM-GRU, Transformer, XGBoost, DQN)
to predict market movements, implements self-learning capabilities to improve over time,
and predicts upcoming candles through sophisticated analysis.

Features:
- Multi-model integration with ensemble prediction
- Self-learning capabilities that improve from trading mistakes
- Future candle prediction with confidence scoring
- Adaptive trading strategies based on market conditions
- Historical data caching to improve performance
- Works with or without TensorFlow
"""

import os
import sys
import time
import csv
import asyncio
import pyfiglet
import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
from quotexapi.stable_api import Quotex
from quotexapi.utils.processor import process_candles, get_color

# Import model manager
from Models.model_manager import ModelManager, TENSORFLOW_AVAILABLE

# Import feature engineering
from Models.Feature_Engineering import engineer_features

# Import trade analysis
from trade_analysis import analyze_trade_opportunity, analyze_multiple_timeframes, get_risk_reward_ratio

# Import TensorFlow check and self-learning modules
from tensorflow_check import setup_tensorflow, is_tensorflow_available
from Models.Enhanced_Self_Learning import EnhancedModelTrainer
from Models.XGBoost_Self_Learning import XGBoostSelfLearner

# Import TensorFlow configuration
from tensorflow_config import configure_tensorflow, get_model_compile_options

# ASCII art banner
custom_font = pyfiglet.Figlet(font="slant")
ascii_art = custom_font.renderText("Auto Trader")
print(ascii_art)
print("Developed by: Your Name")
print("Based on PyQuotex by Cleiton Leonel Creton")
print("-" * 60)

# Load credentials from config.ini
import configparser

# Use raw ConfigParser to avoid interpolation issues with % in passwords
config = configparser.RawConfigParser()
config_path = os.path.join('settings', 'config.ini')
if os.path.exists(config_path):
    config.read(config_path)
    email = config.get('settings', 'email', fallback=None)
    password = config.get('settings', 'password', fallback=None)
else:
    email = None
    password = None

client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

# Trading settings
ASSET = "EURUSD"  # Trading on EURUSD
AMOUNT = 10  # Default trade amount
EXPIRATION = 60  # Default expiration time in seconds
MAX_TRADES = 999999  # Effectively unlimited trades
MAX_MONITORING_TIME = 3600 * 24 * 365  # Maximum time to monitor in seconds (1 year)

# Model settings
MODEL_DIR = 'models'  # Directory for model files
DEFAULT_MODEL = 'ensemble'  # Default model to use (ensemble, lstm_gru, transformer, xgboost, dqn)
MIN_CONFIDENCE = 0.65  # Minimum confidence threshold for trading
MIN_CANDLES = 30  # Minimum number of candles needed for prediction
ENABLE_SELF_LEARNING = True  # Whether to enable self-learning capabilities
TENSORFLOW_REQUIRED = False  # Whether to require TensorFlow for operation (set to False for compatibility)

# Future prediction settings
PREDICT_FUTURE_CANDLES = True  # Whether to predict future candles
FUTURE_CANDLES_COUNT = 3  # Number of future candles to predict
PREDICTION_HORIZON = [1, 3, 5]  # Prediction horizons in minutes
ACCURACY_THRESHOLD = 0.70  # Minimum accuracy threshold for trading based on future predictions
CONFIRMATION_COUNT = 2  # Number of models that must agree for a trade signal

# Self-learning settings
LEARNING_MODE = 'active'  # 'active' (immediate updates) or 'batch' (periodic updates)
MISTAKE_THRESHOLD = 2  # Number of consecutive mistakes before triggering special learning
LEARNING_RATE = 0.25  # How quickly the model adapts to new information (0-1)
MEMORY_RETENTION = 0.7  # How much weight to give to historical vs. recent data (0-1)
RETRAINING_INTERVAL = 12  # Hours between model retraining (if batch mode)
FEEDBACK_WINDOW = 10  # Number of recent trades to use for feedback learning
ACCURACY_IMPROVEMENT_TARGET = 0.05  # Target improvement in accuracy per learning cycle

# Risk management settings
USE_RISK_MANAGEMENT = True  # Whether to use risk management
MAX_DAILY_LOSS = 50  # Maximum daily loss in currency units
MAX_CONSECUTIVE_LOSSES = 3  # Maximum number of consecutive losses before stopping
PROFIT_TARGET = 100  # Daily profit target in currency units
MARTINGALE_ENABLED = False  # Whether to use martingale strategy
MARTINGALE_MULTIPLIER = 2.0  # Multiplier for martingale strategy

# Data caching settings
USE_DATA_CACHE = True  # Whether to use data caching
CACHE_DIR = 'data_cache'  # Directory for cached data
CACHE_EXPIRY = 720  # Cache expiry in hours (30 days)
INITIAL_HISTORY_DAYS = 60  # Initial history to fetch (2 months)
FORCE_RETRAIN = False  # Whether to force model retraining even if models exist

# Model configuration
MODEL_CONFIG = {
    'lstm_seq_length': 30,
    'transformer_seq_length': 30,
    'dqn_window_size': 30,
    'ensemble_window_size': 30,
    'model_weights': {
        'lstm_gru': 0.3,
        'transformer': 0.3,
        'xgboost': 0.3,
        'dqn': 0.1
    },
    # Future prediction configuration
    'predict_future': PREDICT_FUTURE_CANDLES,
    'future_candles_count': FUTURE_CANDLES_COUNT,
    'prediction_horizons': PREDICTION_HORIZON,
    'accuracy_threshold': ACCURACY_THRESHOLD,
    'confirmation_count': CONFIRMATION_COUNT,
    # Self-learning configuration
    'adaptation_rate': LEARNING_RATE,
    'confidence_threshold': MIN_CONFIDENCE,
    'memory_factor': MEMORY_RETENTION,
    'retraining_threshold': 0.55,
    'optimization_interval': 10,
    'mistake_threshold': MISTAKE_THRESHOLD,
    'feedback_window': FEEDBACK_WINDOW
}

# Create directories if they don't exist
os.makedirs(MODEL_DIR, exist_ok=True)
os.makedirs(CACHE_DIR, exist_ok=True)

# Configure TensorFlow for optimal performance
print("\nConfiguring TensorFlow for optimal performance...")
tf_config = configure_tensorflow(
    memory_limit=None,  # No memory limit
    use_mixed_precision=True,  # Use mixed precision for better performance
    log_device_placement=False  # Don't log device placement (reduces console output)
)

# Get model compilation options
compile_options = get_model_compile_options(
    use_xla=True,  # Use XLA compilation
    use_auto_mixed_precision=True  # Use auto mixed precision
)

# Update model configuration with TensorFlow settings
MODEL_CONFIG.update({
    'compile_options': compile_options,
    'tf_config': tf_config
})

# Initialize model manager with updated configuration
model_manager = ModelManager(model_dir=MODEL_DIR, config=MODEL_CONFIG)

# Explicitly load models
print("Loading models...")
try:
    loaded_models = model_manager.load_models()
    if loaded_models:
        print(f"Successfully loaded {len(loaded_models)} models:")
        for model_name in loaded_models:
            print(f"- {model_name}")
    else:
        print("No models were loaded. Check model files in the models directory.")
except Exception as e:
    print(f"Error loading models: {e}")
    import traceback
    traceback.print_exc()

# CSV settings
CSV_FILENAME = f"{ASSET.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_candles.csv"

# Initialize trading statistics
trading_stats = {
    'trades_executed': 0,
    'wins': 0,
    'losses': 0,
    'consecutive_wins': 0,
    'consecutive_losses': 0,
    'profit_loss': 0.0,
    'start_balance': 0.0,
    'current_balance': 0.0,
    'max_drawdown': 0.0,
    'max_profit': 0.0,
    'model_performance': {},
    'trade_history': []
}

async def select_account():
    """Select between demo and real accounts"""
    account_type = input("Select account type (demo or real): ").lower()

    if account_type == "real":
        print("Real account selected")
        client.set_account_mode("REAL")
    else:
        print("Demo account selected")
        client.set_account_mode("PRACTICE")  # Default is PRACTICE/DEMO

    return account_type

async def get_asset_info(asset):
    """Get asset information"""
    asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
    print(f"{asset_name} ({asset_data})")
    return asset_name, asset_data

async def load_and_cache_candles(asset, end_from_time=None, count=None, timeframe=60, is_initial_load=False):
    """
    Enhanced function to load candles from API and cache them for future use

    Parameters:
    - asset: Asset name
    - end_from_time: End time for candles (default: current time)
    - count: Number of candles to fetch (default: calculated based on INITIAL_HISTORY_DAYS)
    - timeframe: Timeframe in seconds (default: 60 seconds)
    - is_initial_load: Whether this is the initial data load (default: False)

    Returns:
    - candles: List of candle data
    - metadata: Dictionary with metadata about the loaded data
    """
    # Create cache directory if it doesn't exist
    os.makedirs(CACHE_DIR, exist_ok=True)

    # Set default end_from_time to current time if not provided
    if end_from_time is None:
        end_from_time = time.time()

    # For initial load, calculate count based on INITIAL_HISTORY_DAYS if not provided
    if is_initial_load and count is None:
        # Calculate number of candles needed for INITIAL_HISTORY_DAYS
        # For 1-minute candles: 60 days * 24 hours * 60 minutes = 86400 candles
        # For 5-minute candles: 60 days * 24 hours * 12 = 17280 candles
        minutes_per_candle = timeframe // 60
        count = INITIAL_HISTORY_DAYS * 24 * 60 // minutes_per_candle
        print(f"Initial load: Calculated {count} candles needed for {INITIAL_HISTORY_DAYS} days of {minutes_per_candle}-minute candles")
    elif count is None:
        # Default to 300 candles for regular loads
        count = 300

    # Generate cache key - include date only for non-initial loads
    date_part = "" if is_initial_load else f"_{datetime.now().strftime('%Y%m%d')}"
    cache_key = f"{asset}_{timeframe}_history{date_part}"
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.pkl")

    # Metadata to return
    metadata = {
        'source': 'cache',
        'cache_file': cache_file,
        'is_initial_load': is_initial_load,
        'candles_requested': count,
        'candles_loaded': 0,
        'oldest_candle_time': None,
        'newest_candle_time': None,
        'timeframe': timeframe
    }

    # Check if cache exists and is fresh
    if USE_DATA_CACHE and os.path.exists(cache_file):
        # Check if cache is expired
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        cache_age_hours = (datetime.now() - file_time).total_seconds() / 3600

        if cache_age_hours < CACHE_EXPIRY:
            print(f"Loading candles from cache: {cache_file}")
            print(f"Cache age: {cache_age_hours:.1f} hours (expires after {CACHE_EXPIRY} hours)")

            cached_data = joblib.load(cache_file)
            existing_candles = cached_data.get('candles', [])
            last_update_time = cached_data.get('last_update_time', file_time.timestamp())

            metadata['candles_in_cache'] = len(existing_candles)

            if existing_candles:
                metadata['oldest_candle_time'] = datetime.fromtimestamp(existing_candles[0]['time'])
                metadata['newest_candle_time'] = datetime.fromtimestamp(existing_candles[-1]['time'])

            # For initial load, we want all cached data plus any new data
            if is_initial_load:
                # Check if we need to fetch additional candles to reach the desired count
                if len(existing_candles) < count:
                    # Calculate how many more candles we need
                    additional_count = count - len(existing_candles)
                    print(f"Cache has {len(existing_candles)} candles, need {count}")
                    print(f"Fetching {additional_count} additional candles")

                    # Fetch additional candles
                    new_candles = await client.get_candles(asset, end_from_time, additional_count, timeframe)

                    if new_candles:
                        # Combine and save to cache
                        combined_candles = existing_candles + new_candles

                        # Update metadata
                        metadata['source'] = 'cache+api'
                        metadata['candles_loaded'] = len(combined_candles)
                        metadata['candles_from_api'] = len(new_candles)
                        metadata['oldest_candle_time'] = datetime.fromtimestamp(combined_candles[0]['time'])
                        metadata['newest_candle_time'] = datetime.fromtimestamp(combined_candles[-1]['time'])

                        # Save updated cache
                        joblib.dump({
                            'candles': combined_candles,
                            'last_update_time': time.time()
                        }, cache_file)

                        print(f"Updated cache with {len(new_candles)} new candles")
                        return combined_candles, metadata
                    else:
                        print("No new candles fetched from API")

                # If we have enough candles or couldn't fetch more, return what we have
                metadata['candles_loaded'] = len(existing_candles)
                return existing_candles, metadata
            else:
                # For regular loads, check if we need to fetch new data since last update
                current_time = time.time()
                time_since_update = current_time - last_update_time
                candles_needed = int(time_since_update / timeframe) + 5  # Add buffer

                if candles_needed > 0:
                    print(f"Fetching {candles_needed} new candles since last update")
                    new_candles = await client.get_candles(asset, current_time, candles_needed, timeframe)

                    if new_candles:
                        # Find where new candles start (avoid duplicates)
                        last_cached_time = existing_candles[-1]['time'] if existing_candles else 0
                        unique_new_candles = [c for c in new_candles if c['time'] > last_cached_time]

                        if unique_new_candles:
                            # Combine and save to cache
                            combined_candles = existing_candles + unique_new_candles

                            # Trim to keep cache size reasonable (keep last 30 days)
                            max_candles = 30 * 24 * 60 // (timeframe // 60)
                            if len(combined_candles) > max_candles:
                                combined_candles = combined_candles[-max_candles:]

                            # Update metadata
                            metadata['source'] = 'cache+api'
                            metadata['candles_loaded'] = len(combined_candles)
                            metadata['candles_from_api'] = len(unique_new_candles)
                            metadata['oldest_candle_time'] = datetime.fromtimestamp(combined_candles[0]['time'])
                            metadata['newest_candle_time'] = datetime.fromtimestamp(combined_candles[-1]['time'])

                            # Save updated cache
                            joblib.dump({
                                'candles': combined_candles,
                                'last_update_time': current_time
                            }, cache_file)

                            print(f"Updated cache with {len(unique_new_candles)} new candles")
                            return combined_candles[-count:], metadata

                # If no new candles or couldn't fetch more, return what we have
                metadata['candles_loaded'] = min(len(existing_candles), count)
                return existing_candles[-count:], metadata

    # If no cache, expired, or forced refresh, fetch from API
    print(f"No valid cache found. Fetching {count} candles from API")
    metadata['source'] = 'api'

    try:
        candles = await client.get_candles(asset, end_from_time, count, timeframe)

        if candles:
            metadata['candles_loaded'] = len(candles)
            metadata['oldest_candle_time'] = datetime.fromtimestamp(candles[0]['time'])
            metadata['newest_candle_time'] = datetime.fromtimestamp(candles[-1]['time'])

            # Save to cache if enabled
            if USE_DATA_CACHE:
                joblib.dump({
                    'candles': candles,
                    'last_update_time': time.time()
                }, cache_file)
                print(f"Saved {len(candles)} candles to cache: {cache_file}")
        else:
            print("Warning: No candles returned from API")
            metadata['candles_loaded'] = 0
            candles = []

        return candles, metadata
    except Exception as e:
        print(f"Error fetching candles from API: {e}")
        metadata['error'] = str(e)
        return [], metadata

async def predict_future_candles(candles, horizons=None, models=None):
    """
    Enhanced prediction of future candles using multiple models and horizons
    with advanced confidence scoring and market condition analysis.
    Improved to provide more accurate predictions with continuous learning.

    Parameters:
    - candles: List of candle data
    - horizons: List of prediction horizons in minutes
    - models: List of models to use for prediction

    Returns:
    - future_predictions: Dictionary of predictions for each horizon and model
    """
    if horizons is None:
        horizons = PREDICTION_HORIZON

    if models is None:
        # Use all available models
        if TENSORFLOW_AVAILABLE:
            models = ['lstm_gru', 'transformer', 'xgboost', 'dqn', 'ensemble']
        else:
            models = ['xgboost']  # Only XGBoost if TensorFlow is not available

    # Convert candles to DataFrame for feature engineering
    df_candles = pd.DataFrame(candles)

    # Apply feature engineering to get more advanced features
    print("Engineering features for future candle prediction...")
    featured_candles = engineer_features(df_candles)

    # Check if we have enough data for feature calculations
    if len(featured_candles) < 30:
        print(f"Warning: Not enough data for reliable future predictions. Have {len(featured_candles)} candles, need at least 30.")
        # Return empty predictions dictionary
        return {horizon: {'aggregated': {'direction': None, 'error': 'Not enough data'}} for horizon in horizons}

    # Analyze market conditions to adjust prediction confidence
    volatility = featured_candles['volatility_20'].iloc[-1]
    trend_strength = featured_candles['adx_14'].iloc[-1] / 100.0  # Normalize to 0-1
    is_ranging = featured_candles['neutral_regime'].iloc[-1] == 1

    # Calculate additional market metrics for better prediction
    if 'rsi_14' in featured_candles.columns:
        rsi = featured_candles['rsi_14'].iloc[-1]
        overbought = rsi > 70
        oversold = rsi < 30
    else:
        rsi = 50
        overbought = False
        oversold = False

    # Check for potential reversal patterns
    potential_reversal = False
    if len(featured_candles) >= 3:
        # Check for three consecutive candles in same direction
        last_3_returns = featured_candles['returns'].iloc[-3:].values
        if (all(r > 0 for r in last_3_returns) and overbought) or (all(r < 0 for r in last_3_returns) and oversold):
            potential_reversal = True
            print(f"Detected potential reversal pattern: {'Bearish' if all(r > 0 for r in last_3_returns) else 'Bullish'}")

    # Determine market regime
    if featured_candles['bullish_regime'].iloc[-1] == 1:
        market_regime = "bullish"
    elif featured_candles['bearish_regime'].iloc[-1] == 1:
        market_regime = "bearish"
    else:
        market_regime = "neutral"

    print(f"Market analysis - Regime: {market_regime}, Volatility: {volatility:.4f}, Trend strength: {trend_strength:.4f}, RSI: {rsi:.1f}")
    if overbought:
        print("Market is OVERBOUGHT - potential for downward movement")
    elif oversold:
        print("Market is OVERSOLD - potential for upward movement")
    if potential_reversal:
        print("POTENTIAL REVERSAL PATTERN DETECTED - market may change direction soon")

    # Initialize predictions dictionary with enhanced structure
    future_predictions = {
        horizon: {
            model: None for model in models
        } for horizon in horizons
    }

    # Add aggregated predictions for each horizon with enhanced metrics
    for horizon in horizons:
        future_predictions[horizon]['aggregated'] = {
            'direction': None,
            'probability': 0.0,
            'confidence': 0.0,
            'agreement': 0.0,
            'models_agree': 0,
            'market_regime': market_regime,
            'volatility': float(volatility),
            'trend_strength': float(trend_strength),
            'is_ranging': bool(is_ranging),
            'rsi': float(rsi),
            'overbought': overbought,
            'oversold': oversold,
            'potential_reversal': potential_reversal
        }

    # Make predictions for each model and horizon
    for model_name in models:
        if model_name not in model_manager.models and model_name != 'ensemble':
            print(f"Model {model_name} not available, skipping")
            continue

        for horizon in horizons:
            # Create a copy of featured candles for prediction
            candles_copy = featured_candles.copy()

            # Add horizon information to additional_features
            additional_features = {
                'prediction_horizon': horizon,
                'is_future_prediction': True,
                'market_regime': market_regime,
                'volatility': volatility,
                'trend_strength': trend_strength
            }

            # Make prediction
            try:
                prediction = model_manager.predict(candles_copy, additional_features=additional_features, model_name=model_name)

                # Store prediction if successful
                if prediction.get('prediction') is not None:
                    # Enhanced confidence adjustment based on market conditions and patterns
                    adjusted_confidence = prediction['confidence']

                    # Store original confidence for reference
                    original_confidence = adjusted_confidence

                    # In high volatility, reduce confidence
                    if volatility > 0.02:  # High volatility threshold
                        volatility_factor = 1.0 - min(0.3, (volatility - 0.02) * 5)  # Reduce by up to 30%
                        adjusted_confidence *= volatility_factor
                        print(f"  Adjusted for high volatility: {original_confidence:.4f} → {adjusted_confidence:.4f}")

                    # In strong trends, increase confidence if prediction aligns with trend
                    if trend_strength > 0.3:  # Strong trend threshold
                        if (market_regime == "bullish" and prediction['direction'] == "UP") or \
                           (market_regime == "bearish" and prediction['direction'] == "DOWN"):
                            trend_boost = 1.0 + min(0.2, (trend_strength - 0.3) * 0.5)  # Boost by up to 20%
                            pre_boost = adjusted_confidence
                            adjusted_confidence *= trend_boost
                            print(f"  Boosted for trend alignment: {pre_boost:.4f} → {adjusted_confidence:.4f}")

                    # In ranging markets, reduce confidence
                    if is_ranging:
                        pre_ranging = adjusted_confidence
                        adjusted_confidence *= 0.9  # 10% reduction in ranging markets
                        print(f"  Reduced for ranging market: {pre_ranging:.4f} → {adjusted_confidence:.4f}")

                    # Adjust for overbought/oversold conditions
                    if overbought and prediction['direction'] == "UP":
                        pre_ob = adjusted_confidence
                        adjusted_confidence *= 0.85  # 15% reduction for overbought UP predictions
                        print(f"  Reduced for overbought UP prediction: {pre_ob:.4f} → {adjusted_confidence:.4f}")
                    elif oversold and prediction['direction'] == "DOWN":
                        pre_os = adjusted_confidence
                        adjusted_confidence *= 0.85  # 15% reduction for oversold DOWN predictions
                        print(f"  Reduced for oversold DOWN prediction: {pre_os:.4f} → {adjusted_confidence:.4f}")

                    # Adjust for potential reversal patterns
                    if potential_reversal:
                        # If we detect a potential reversal, boost confidence for predictions against the trend
                        if (market_regime == "bullish" and prediction['direction'] == "DOWN") or \
                           (market_regime == "bearish" and prediction['direction'] == "UP"):
                            pre_rev = adjusted_confidence
                            adjusted_confidence *= 1.15  # 15% boost for counter-trend predictions during potential reversal
                            print(f"  Boosted for potential reversal: {pre_rev:.4f} → {adjusted_confidence:.4f}")

                    # Adjust based on horizon - longer horizons have lower confidence
                    horizon_factor = 1.0 - (horizon - 1) * 0.05  # 5% reduction per minute into future
                    pre_horizon = adjusted_confidence
                    adjusted_confidence *= max(0.7, horizon_factor)  # Don't reduce below 70% of current value
                    print(f"  Adjusted for horizon {horizon}: {pre_horizon:.4f} → {adjusted_confidence:.4f}")

                    # Cap confidence at 0.95
                    if adjusted_confidence > 0.95:
                        print(f"  Capping confidence: {adjusted_confidence:.4f} → 0.95")
                        adjusted_confidence = 0.95

                    # Print overall adjustment
                    print(f"  Overall confidence adjustment: {original_confidence:.4f} → {adjusted_confidence:.4f}")

                    # Add explanation for adjustments
                    confidence_adjustments = []
                    if volatility > 0.02:
                        confidence_adjustments.append(f"high volatility ({volatility:.4f})")
                    if trend_strength > 0.3:
                        confidence_adjustments.append(f"strong trend ({trend_strength:.4f})")
                    if is_ranging:
                        confidence_adjustments.append("ranging market")
                    if overbought and prediction['direction'] == "UP":
                        confidence_adjustments.append("overbought market")
                    if oversold and prediction['direction'] == "DOWN":
                        confidence_adjustments.append("oversold market")
                    if potential_reversal:
                        confidence_adjustments.append("potential reversal pattern")

                    adjustment_explanation = ", ".join(confidence_adjustments) if confidence_adjustments else "standard factors"

                    # Update prediction with adjusted confidence
                    prediction['original_confidence'] = prediction['confidence']
                    prediction['confidence'] = float(adjusted_confidence)
                    prediction['market_adjusted'] = True

                    # Store the enhanced prediction
                    future_predictions[horizon][model_name] = prediction

                    # Update aggregated prediction
                    aggregated = future_predictions[horizon]['aggregated']

                    # Count models that agree on direction with weighted voting
                    if aggregated['direction'] is None:
                        # First prediction for this horizon
                        aggregated['direction'] = prediction['direction']
                        aggregated['probability'] = prediction['probability']
                        aggregated['confidence'] = prediction['confidence']
                        aggregated['models_agree'] = 1
                    elif aggregated['direction'] == prediction['direction']:
                        # Model agrees with current direction - weighted by confidence
                        weight_current = aggregated['confidence'] * aggregated['models_agree']
                        weight_new = prediction['confidence']

                        # Update weighted average
                        aggregated['probability'] = (aggregated['probability'] * weight_current +
                                                    prediction['probability'] * weight_new) / (weight_current + weight_new)
                        aggregated['confidence'] = (aggregated['confidence'] * weight_current +
                                                   prediction['confidence'] * weight_new) / (weight_current + weight_new)
                        aggregated['models_agree'] += 1
                    else:
                        # Model disagrees, check if we need to flip direction
                        if prediction['confidence'] > aggregated['confidence'] and aggregated['models_agree'] <= 1:
                            # New prediction is more confident and we only have one model agreeing so far
                            aggregated['direction'] = prediction['direction']
                            aggregated['probability'] = prediction['probability']
                            aggregated['confidence'] = prediction['confidence']
                            aggregated['models_agree'] = 1
                        elif prediction['confidence'] > aggregated['confidence'] * 1.2:
                            # New prediction is significantly more confident (20% higher)
                            # This is a strong disagreement that overrides previous consensus
                            aggregated['direction'] = prediction['direction']
                            aggregated['probability'] = prediction['probability']
                            aggregated['confidence'] = prediction['confidence']
                            aggregated['models_agree'] = 1
                        elif prediction['confidence'] > ACCURACY_THRESHOLD:
                            # Strong disagreement from a confident model
                            # Count as negative agreement
                            aggregated['models_agree'] -= 0.5

                    # Calculate agreement ratio with available models
                    available_models = sum(1 for m in models if m in model_manager.models or m == 'ensemble')
                    aggregated['agreement'] = aggregated['models_agree'] / available_models

                    # Add market condition impact assessment
                    if aggregated['direction'] == "UP" and market_regime == "bullish":
                        aggregated['market_alignment'] = "aligned"
                    elif aggregated['direction'] == "DOWN" and market_regime == "bearish":
                        aggregated['market_alignment'] = "aligned"
                    elif market_regime == "neutral":
                        aggregated['market_alignment'] = "neutral"
                    else:
                        aggregated['market_alignment'] = "contrary"
            except Exception as e:
                print(f"Error predicting future candle at horizon {horizon} with model {model_name}: {e}")

    return future_predictions

async def analyze_strategy(candles, model_name=DEFAULT_MODEL, min_confidence=MIN_CONFIDENCE):
    """
    Enhanced analysis of candles using machine learning models with advanced future candle prediction
    and market condition analysis for more accurate trading signals

    Parameters:
    - candles: List of candle data
    - model_name: Name of model to use (default: DEFAULT_MODEL)
    - min_confidence: Minimum confidence threshold for trading

    Returns:
    - direction: Trading direction ('call', 'put', or None)
    - reason: Reason for the trading decision
    - details: Dictionary with detailed analysis information
    """
    if len(candles) < MIN_CANDLES:
        return None, f"Not enough candles for analysis (need {MIN_CANDLES})", None

    # Convert candles to DataFrame for better analysis
    df_candles = pd.DataFrame(candles)

    # Print basic candle information
    last_candles = candles[-3:]  # Show last 3 candles
    colors = [get_color(candle) for candle in last_candles]
    print(f"Last 3 candles: {colors}")

    # Print the current time
    current_time = datetime.now()
    print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Load models if not already loaded
    if not hasattr(model_manager, 'models') or not model_manager.models:
        print("Models not loaded yet, loading models...")
        try:
            loaded_models = model_manager.load_models()
            if loaded_models:
                print(f"Successfully loaded {len(loaded_models)} models:")
                for model_name in loaded_models:
                    print(f"- {model_name}")
            else:
                print("No models were loaded. Check model files in the models directory.")
                return None, "No models available", None
        except Exception as e:
            print(f"Error loading models: {e}")
            import traceback
            traceback.print_exc()
            return None, f"Error loading models: {e}", None

    # Apply feature engineering for better prediction
    print("Engineering features for current candle prediction...")
    featured_candles = engineer_features(df_candles)

    # Check if we have enough data for feature calculations
    required_columns = ['volatility_20', 'adx_14', 'neutral_regime']
    if any(col not in featured_candles.columns for col in required_columns):
        print(f"Warning: Missing required columns in featured data. Available columns: {featured_candles.columns.tolist()}")
        return None, "Missing required features for analysis", None

    if len(featured_candles) < 30:
        print(f"Warning: Not enough data for reliable predictions. Have {len(featured_candles)} candles, need at least 30.")
        return None, f"Not enough data for reliable predictions (need at least 30 candles)", None

    # Analyze market conditions
    volatility = featured_candles['volatility_20'].iloc[-1]
    trend_strength = featured_candles['adx_14'].iloc[-1] / 100.0  # Normalize to 0-1
    is_ranging = featured_candles['neutral_regime'].iloc[-1] == 1

    # Determine market regime
    if featured_candles['bullish_regime'].iloc[-1] == 1:
        market_regime = "bullish"
    elif featured_candles['bearish_regime'].iloc[-1] == 1:
        market_regime = "bearish"
    else:
        market_regime = "neutral"

    print(f"Market analysis - Regime: {market_regime}, Volatility: {volatility:.4f}, Trend strength: {trend_strength:.4f}")

    # Adjust confidence threshold based on market conditions
    dynamic_confidence = min_confidence

    # In high volatility, increase threshold
    if volatility > 0.02:
        volatility_adjustment = min(0.1, (volatility - 0.02) * 5)  # Up to 0.1 increase
        dynamic_confidence += volatility_adjustment
        print(f"Increased confidence threshold by {volatility_adjustment:.4f} due to high volatility")

    # In ranging markets, increase threshold
    if is_ranging:
        dynamic_confidence += 0.05
        print(f"Increased confidence threshold by 0.05 due to ranging market")

    # In strong trends, slightly decrease threshold if aligned with trend
    if trend_strength > 0.4 and not is_ranging:
        dynamic_confidence -= 0.03
        print(f"Decreased confidence threshold by 0.03 due to strong trend")

    print(f"Dynamic confidence threshold: {dynamic_confidence:.4f} (base: {min_confidence:.4f})")

    # Make prediction for current candle with market context
    print(f"Making prediction with {model_name} model...")
    additional_features = {
        'market_regime': market_regime,
        'volatility': volatility,
        'trend_strength': trend_strength,
        'is_ranging': is_ranging
    }

    # Define TensorFlow-based models in order of preference
    tf_models = ['lstm_gru', 'transformer', 'dqn', 'ensemble']

    # Check if the selected model is available
    if model_name == 'ensemble' and (not hasattr(model_manager, 'ensemble') or model_manager.ensemble is None):
        print(f"Ensemble model not available, trying to find a TensorFlow model")
        # Try to use any available TensorFlow model
        for model in tf_models:
            if model != 'ensemble' and model in model_manager.models:
                print(f"Using {model} model instead")
                model_name = model
                break
        else:
            # If no TensorFlow model is available, use XGBoost as last resort
            if 'xgboost' in model_manager.models:
                print("No TensorFlow models available, using XGBoost")
                model_name = 'xgboost'
            elif model_manager.models:
                # Use any available model
                available_model = list(model_manager.models.keys())[0]
                print(f"Using {available_model} model as fallback")
                model_name = available_model
            else:
                print("No models available, cannot make prediction")
                return None, "No models available", None
    elif model_name not in model_manager.models and model_name != 'ensemble':
        print(f"Model {model_name} not available, trying to find a TensorFlow model")
        # Try to use any available TensorFlow model
        for model in tf_models:
            if model != model_name and (model in model_manager.models or (model == 'ensemble' and hasattr(model_manager, 'ensemble') and model_manager.ensemble is not None)):
                print(f"Using {model} model instead")
                model_name = model
                break
        else:
            # If no TensorFlow model is available, use any available model
            if model_manager.models:
                available_model = list(model_manager.models.keys())[0]
                print(f"Using {available_model} model as fallback")
                model_name = available_model
            else:
                print("No models available, cannot make prediction")
                return None, "No models available", None

    # Make prediction with the selected or fallback model
    current_prediction = model_manager.predict(featured_candles, model_name=model_name, additional_features=additional_features)

    # Check if prediction was successful
    if current_prediction.get('prediction') is None:
        error = current_prediction.get('error', 'Unknown error')
        print(f"Prediction failed: {error}")

        # Try other models if the current one failed
        if error != "No models available":
            print("Trying alternative models...")
            # Try each model in order of preference
            all_models = tf_models + ['xgboost']
            for alt_model in all_models:
                if alt_model != model_name and (alt_model in model_manager.models or (alt_model == 'ensemble' and hasattr(model_manager, 'ensemble') and model_manager.ensemble is not None)):
                    print(f"Trying {alt_model} model...")
                    alt_prediction = model_manager.predict(featured_candles, model_name=alt_model, additional_features=additional_features)
                    if alt_prediction.get('prediction') is not None:
                        print(f"Successfully made prediction with {alt_model} model")
                        return alt_prediction.get('direction').lower(), f"{alt_model} model predicts {alt_prediction.get('direction')} with {alt_prediction.get('confidence'):.4f} confidence", alt_prediction

        return None, f"Prediction failed: {error}", None

    # Extract prediction details
    direction = current_prediction['direction']
    probability = current_prediction['probability']
    confidence = current_prediction['confidence']
    model = current_prediction.get('model', model_name)

    print(f"Model: {model}")
    print(f"Current candle prediction: {direction}")
    print(f"Probability: {probability:.4f}")
    print(f"Confidence: {confidence:.4f}")

    # Perform comprehensive trade analysis before making a decision
    print("\nPerforming comprehensive trade analysis...")
    take_trade, trade_analysis, adjusted_confidence = analyze_trade_opportunity(candles, current_prediction, dynamic_confidence)

    # Print trade analysis results
    print(f"Trade analysis result: {'TAKE TRADE' if take_trade else 'DO NOT TAKE TRADE'}")
    print(f"Adjusted confidence: {adjusted_confidence:.4f} (original: {confidence:.4f})")
    print(f"Confirmations: {len(trade_analysis['confirmations'])}")
    for confirmation in trade_analysis['confirmations']:
        print(f"  ✓ {confirmation}")
    print(f"Warnings: {len(trade_analysis['warnings'])}")
    for warning in trade_analysis['warnings']:
        print(f"  ⚠ {warning}")

    # Create details dictionary to return comprehensive analysis
    details = {
        'timestamp': current_time.isoformat(),
        'market_conditions': {
            'regime': market_regime,
            'volatility': float(volatility),
            'trend_strength': float(trend_strength),
            'is_ranging': bool(is_ranging)
        },
        'current_prediction': {
            'model': model,
            'direction': direction,
            'probability': float(probability),
            'confidence': float(confidence),
            'adjusted_confidence': float(adjusted_confidence)
        },
        'dynamic_threshold': float(dynamic_confidence),
        'trade_analysis': trade_analysis,
        'future_predictions': {}
    }

    # If future candle prediction is enabled, predict future candles
    if PREDICT_FUTURE_CANDLES:
        print("\nPredicting future candles...")
        future_predictions = await predict_future_candles(candles)

        # Store future predictions in details
        details['future_predictions'] = future_predictions

        # Print future predictions
        for horizon in sorted(future_predictions.keys()):
            aggregated = future_predictions[horizon]['aggregated']
            if aggregated['direction'] is not None:
                print(f"\nHorizon: {horizon} minute(s)")
                print(f"Direction: {aggregated['direction']}")
                print(f"Confidence: {aggregated['confidence']:.4f}")
                print(f"Agreement: {aggregated['agreement']:.2f} ({aggregated['models_agree']} models)")
                if 'market_alignment' in aggregated:
                    print(f"Market alignment: {aggregated['market_alignment']}")

                # Print individual model predictions
                for model_name in future_predictions[horizon]:
                    if model_name != 'aggregated' and future_predictions[horizon][model_name] is not None:
                        pred = future_predictions[horizon][model_name]
                        print(f"  {model_name}: {pred['direction']} (conf: {pred['confidence']:.4f})")

        # Check if future predictions confirm current prediction with enhanced criteria
        confirmed_horizons = 0
        strong_confirmations = 0
        total_horizons = len(future_predictions)

        # Track alignment with market conditions
        aligned_with_market = 0
        contrary_to_market = 0

        for horizon in future_predictions:
            aggregated = future_predictions[horizon]['aggregated']

            # Basic confirmation
            if (aggregated['direction'] == direction and
                aggregated['confidence'] >= dynamic_confidence and
                aggregated['models_agree'] >= CONFIRMATION_COUNT):
                confirmed_horizons += 1

                # Strong confirmation has high confidence and agreement
                if aggregated['confidence'] >= dynamic_confidence + 0.1 and aggregated['agreement'] >= 0.7:
                    strong_confirmations += 1

            # Track market alignment
            if 'market_alignment' in aggregated:
                if aggregated['market_alignment'] == 'aligned':
                    aligned_with_market += 1
                elif aggregated['market_alignment'] == 'contrary':
                    contrary_to_market += 1

        # Calculate confirmation ratio
        confirmation_ratio = confirmed_horizons / total_horizons if total_horizons > 0 else 0
        strong_confirmation_ratio = strong_confirmations / total_horizons if total_horizons > 0 else 0
        market_alignment_ratio = aligned_with_market / total_horizons if total_horizons > 0 else 0

        print(f"\nFuture confirmation ratio: {confirmation_ratio:.2f} ({confirmed_horizons}/{total_horizons} horizons)")
        print(f"Strong confirmation ratio: {strong_confirmation_ratio:.2f} ({strong_confirmations}/{total_horizons} horizons)")
        print(f"Market alignment ratio: {market_alignment_ratio:.2f} ({aligned_with_market}/{total_horizons} horizons)")

        # Store confirmation metrics in details
        details['confirmation_metrics'] = {
            'confirmed_horizons': confirmed_horizons,
            'strong_confirmations': strong_confirmations,
            'total_horizons': total_horizons,
            'confirmation_ratio': float(confirmation_ratio),
            'strong_confirmation_ratio': float(strong_confirmation_ratio),
            'market_alignment_ratio': float(market_alignment_ratio),
            'aligned_with_market': aligned_with_market,
            'contrary_to_market': contrary_to_market
        }

        # Advanced confidence adjustment based on future predictions and market alignment
        base_weight = 0.7  # Weight for current prediction
        future_weight = 0.2  # Weight for future confirmation
        market_weight = 0.1  # Weight for market alignment

        # Calculate adjusted confidence
        adjusted_confidence = (
            confidence * base_weight +
            confidence * future_weight * confirmation_ratio +
            confidence * market_weight * market_alignment_ratio
        )

        # Boost confidence for strong confirmations
        if strong_confirmations >= 2:
            strong_boost = 0.05 * strong_confirmations / total_horizons
            adjusted_confidence += strong_boost
            print(f"Applied strong confirmation boost: +{strong_boost:.4f}")

        # Cap adjusted confidence at 0.95
        adjusted_confidence = min(0.95, adjusted_confidence)

        print(f"Adjusted confidence: {adjusted_confidence:.4f} (original: {confidence:.4f})")
        details['adjusted_confidence'] = float(adjusted_confidence)

        # Check if we have strong future confirmation with enhanced criteria
        if (confirmed_horizons >= 2 and confirmation_ratio >= 0.5) or strong_confirmations >= 1:
            print("Strong future confirmation detected!")
            future_confidence = adjusted_confidence  # Use adjusted confidence from future predictions

            # Create detailed reason with future predictions
            future_info = f"with {confirmed_horizons}/{total_horizons} future horizons confirming"
            if strong_confirmations > 0:
                future_info += f" ({strong_confirmations} strong)"
            if aligned_with_market > 0:
                future_info += f", {aligned_with_market}/{total_horizons} aligned with market"

            # Combine trade analysis with future predictions for final decision

            # Calculate final confidence - weight future predictions more heavily when they're strong
            if strong_confirmations >= 1:
                # Strong future confirmation gets more weight
                final_confidence = (adjusted_confidence * 0.3) + (future_confidence * 0.7)
                print(f"Using weighted confidence with emphasis on strong future predictions: {final_confidence:.4f}")

                # Take trade if trade analysis agrees or future confirmation is very strong
                if take_trade or strong_confirmations >= 2:
                    print("Taking trade based on strong future confirmation")

                    # Take opposite trades from model predictions
                    # If model predicts UP, take DOWN trade and vice versa
                    if direction == "UP":
                        return "put", f"{model} model predicts UP with {final_confidence:.4f} confidence, taking OPPOSITE (PUT) trade, confirmed by {future_info}", details
                    elif direction == "DOWN":
                        return "call", f"{model} model predicts DOWN with {final_confidence:.4f} confidence, taking OPPOSITE (CALL) trade, confirmed by {future_info}", details
                else:
                    # Trade analysis doesn't agree and future confirmation isn't very strong
                    print("Trade analysis does not confirm the signal despite strong future confirmation")
                    return None, f"Trade analysis rejected the signal despite strong future confirmation", details
            else:
                # No strong future confirmation, fall back to regular decision making
                if take_trade:
                    # Trade analysis suggests taking the trade
                    final_confidence = (adjusted_confidence + future_confidence) / 2

                    # Take opposite trades from model predictions
                    # If model predicts UP, take DOWN trade and vice versa
                    if direction == "UP":
                        return "put", f"{model} model predicts UP with {final_confidence:.4f} confidence, taking OPPOSITE (PUT) trade, confirmed by trade analysis and {future_info}", details
                    elif direction == "DOWN":
                        return "call", f"{model} model predicts DOWN with {final_confidence:.4f} confidence, taking OPPOSITE (CALL) trade, confirmed by trade analysis and {future_info}", details
                else:
                    # Trade analysis suggests not taking the trade and no strong future confirmation
                    print("Trade analysis does not confirm the signal and future confirmation is not strong")
                    return None, f"Trade analysis rejected the signal with weak future confirmation", details
        else:
            # No strong future confirmation, rely more heavily on trade analysis
            if take_trade:
                # Trade analysis suggests taking the trade despite weak future confirmation
                print("Trade analysis confirms the signal despite weak future confirmation")

                # Create reason with trade analysis details
                analysis_info = f"with {len(trade_analysis['confirmations'])} confirmations and {len(trade_analysis['warnings'])} warnings"

                # Take opposite trades from model predictions
                # If model predicts UP, take DOWN trade and vice versa
                if direction == "UP":
                    return "put", f"{model} model predicts UP with {adjusted_confidence:.4f} confidence, taking OPPOSITE (PUT) trade, confirmed by trade analysis {analysis_info}", details
                elif direction == "DOWN":
                    return "call", f"{model} model predicts DOWN with {adjusted_confidence:.4f} confidence, taking OPPOSITE (CALL) trade, confirmed by trade analysis {analysis_info}", details
            else:
                # Both analyses suggest not taking the trade
                print("Both trade analysis and future predictions suggest not taking the trade")
                return None, f"Signal rejected by both trade analysis and future predictions", details
    else:
        # Future prediction is disabled, rely solely on trade analysis
        if take_trade:
            # Trade analysis suggests taking the trade
            print("Trade analysis confirms the signal")

            # Create reason with trade analysis details
            analysis_info = f"with {len(trade_analysis['confirmations'])} confirmations and {len(trade_analysis['warnings'])} warnings"

            # Take opposite trades from model predictions
            # If model predicts UP, take DOWN trade and vice versa
            if direction == "UP":
                return "put", f"{model} model predicts UP with {adjusted_confidence:.4f} confidence, taking OPPOSITE (PUT) trade, confirmed by trade analysis {analysis_info}", details
            elif direction == "DOWN":
                return "call", f"{model} model predicts DOWN with {adjusted_confidence:.4f} confidence, taking OPPOSITE (CALL) trade, confirmed by trade analysis {analysis_info}", details
        else:
            # Trade analysis suggests not taking the trade
            print("Trade analysis rejects the signal")
            return None, f"Signal rejected by trade analysis ({len(trade_analysis['warnings'])} warnings)", details

    # If we somehow get here, don't take the trade
    return None, "No clear signal after all analyses", details

async def execute_trade(asset, direction, amount, expiration, prediction_details=None):
    """
    Execute a trade based on strategy with enhanced tracking for self-learning

    Parameters:
    - asset: Asset to trade
    - direction: Trading direction ('call' or 'put')
    - amount: Trade amount
    - expiration: Expiration time in seconds
    - prediction_details: Dictionary with prediction details for self-learning

    Returns:
    - success: Whether the trade was successful
    - profit_loss: Profit or loss from the trade
    - trade_data: Dictionary with trade details for self-learning
    """
    print(f"Entering trade!")
    print(f"Starting strategy analysis.")

    # Get the last few candles for display
    end_from_time = time.time()
    candles = await client.get_candles(asset, end_from_time, 300, 60)

    if len(candles) >= 2:
        print(f"Second-to-last candle: {'Green' if get_color(candles[-2]) == 'green' else 'Red'}")
        print(f"Last candle: {'Green' if get_color(candles[-1]) == 'green' else 'Red'}")

    print(f"Direction: {direction.capitalize()}")

    # Apply risk management if enabled
    if USE_RISK_MANAGEMENT:
        # Check if we've hit the maximum consecutive losses
        if trading_stats['consecutive_losses'] >= MAX_CONSECUTIVE_LOSSES:
            print(f"Maximum consecutive losses reached ({MAX_CONSECUTIVE_LOSSES}). Skipping trade.")
            return False, 0, {'error': 'max_consecutive_losses_reached'}

        # Check if we've hit the maximum daily loss
        if trading_stats['profit_loss'] <= -MAX_DAILY_LOSS:
            print(f"Maximum daily loss reached (${MAX_DAILY_LOSS}). Skipping trade.")
            return False, 0, {'error': 'max_daily_loss_reached'}

        # Check if we've hit the profit target
        if trading_stats['profit_loss'] >= PROFIT_TARGET:
            print(f"Profit target reached (${PROFIT_TARGET}). Skipping trade.")
            return False, 0, {'error': 'profit_target_reached'}

        # Apply martingale strategy if enabled
        if MARTINGALE_ENABLED and trading_stats['consecutive_losses'] > 0:
            adjusted_amount = amount * (MARTINGALE_MULTIPLIER ** trading_stats['consecutive_losses'])
            adjusted_amount = min(adjusted_amount, amount * 8)  # Cap at 8x the original amount
            print(f"Applying martingale strategy. Adjusted amount: ${adjusted_amount:.2f}")
            amount = adjusted_amount

    # Record trade start time
    trade_start_time = datetime.now()

    # Create trade record for tracking
    trade_data = {
        'id': trading_stats['trades_executed'] + 1,
        'asset': asset,
        'direction': direction,
        'amount': amount,
        'expiration': expiration,
        'start_time': trade_start_time.isoformat(),
        'prediction': prediction_details,
        'result': None,
        'profit_loss': None,
        'end_time': None,
        'duration': None,
        'market_conditions': {}
    }

    # Add market conditions if available
    if prediction_details and 'market_conditions' in prediction_details:
        trade_data['market_conditions'] = prediction_details['market_conditions']

    # Check if asset is open
    asset_name, asset_data = await client.get_available_asset(asset, force_open=True)

    if asset_data[2]:  # Check if asset is open
        print("OK: Asset is open.")

        # Execute the trade
        status, buy_info = await client.buy(amount, asset_name, direction, expiration)

        if status:
            print(f"Trade executed successfully. ID: {buy_info['id']}")
            print("Waiting for result...")

            # Update trade data
            trade_data['status'] = 'executed'
            trade_data['buy_info'] = buy_info

            # Wait for the result
            remaining = expiration
            while remaining > 0:
                print(f"\rRemaining {remaining} seconds...", end="")
                await asyncio.sleep(1)
                remaining -= 1

            # Record trade end time
            trade_end_time = datetime.now()
            trade_data['end_time'] = trade_end_time.isoformat()
            trade_data['duration'] = (trade_end_time - trade_start_time).total_seconds()

            # Check the result
            if await client.check_win(buy_info["id"]):
                profit = client.get_profit()
                print(f"\nWin!!! Profit: {profit}")

                # Update trade data
                trade_data['result'] = 'win'
                trade_data['profit_loss'] = profit

                # Update trading statistics
                trading_stats['wins'] += 1
                trading_stats['consecutive_wins'] += 1
                trading_stats['consecutive_losses'] = 0
                trading_stats['profit_loss'] += profit

                # Update max profit if needed
                if trading_stats['profit_loss'] > trading_stats['max_profit']:
                    trading_stats['max_profit'] = trading_stats['profit_loss']

                # Apply self-learning if enabled
                if ENABLE_SELF_LEARNING and LEARNING_MODE == 'active':
                    print("Applying active learning from successful trade...")
                    # Try to use enhanced trainer first if available
                    if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                        print("Using enhanced self-learning system...")
                        model_manager.enhanced_trainer.learn_from_trade(trade_data, True)
                    else:
                        # Fall back to original method
                        model_manager.learn_from_trade(trade_data, True)

                return True, profit, trade_data
            else:
                loss = -amount  # Loss is the negative of the amount
                print(f"\nLoss!!! Loss: {loss}")

                # Update trade data
                trade_data['result'] = 'loss'
                trade_data['profit_loss'] = loss

                # Update trading statistics
                trading_stats['losses'] += 1
                trading_stats['consecutive_losses'] += 1
                trading_stats['consecutive_wins'] = 0
                trading_stats['profit_loss'] += loss

                # Update max drawdown if needed
                if trading_stats['profit_loss'] < trading_stats['max_drawdown']:
                    trading_stats['max_drawdown'] = trading_stats['profit_loss']

                # Apply self-learning if enabled
                if ENABLE_SELF_LEARNING and LEARNING_MODE == 'active':
                    print("Applying active learning from failed trade...")
                    # Try to use enhanced trainer first if available
                    if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                        print("Using enhanced self-learning system...")
                        model_manager.enhanced_trainer.learn_from_trade(trade_data, False)
                    else:
                        # Fall back to original method
                        model_manager.learn_from_trade(trade_data, False)

                # Check if we need special learning due to consecutive losses
                if ENABLE_SELF_LEARNING and trading_stats['consecutive_losses'] >= MISTAKE_THRESHOLD:
                    print(f"Detected {trading_stats['consecutive_losses']} consecutive losses. Triggering special learning...")
                    # Try to use enhanced trainer first if available
                    if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                        print("Using enhanced special learning...")
                        model_manager.enhanced_trainer.special_learning_from_mistakes(trading_stats['consecutive_losses'])
                    else:
                        # Fall back to original method
                        model_manager.special_learning_from_mistakes(trading_stats['consecutive_losses'])

                return False, loss, trade_data
        else:
            print(f"Trade execution failed: {buy_info}")

            # Update trade data
            trade_data['status'] = 'failed'
            trade_data['error'] = str(buy_info)
            trade_data['end_time'] = datetime.now().isoformat()

            return False, 0, trade_data
    else:
        print("ERROR: Asset is closed.")

        # Update trade data
        trade_data['status'] = 'failed'
        trade_data['error'] = 'asset_closed'
        trade_data['end_time'] = datetime.now().isoformat()

        return False, 0, trade_data

def save_candles_to_csv(candles, filename):
    """Save candles data to CSV file"""
    # Check if file exists to determine if we need to write headers
    file_exists = os.path.isfile(filename)

    # Define fieldnames for CSV
    fieldnames = ['timestamp', 'time', 'open', 'high', 'low', 'close', 'color', 'ticks']

    # Open file in append mode
    with open(filename, 'a', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Write header if file doesn't exist
        if not file_exists:
            writer.writeheader()

        # Write candle data
        for candle in candles:
            # Add color and timestamp if not present
            if 'color' not in candle:
                candle['color'] = get_color(candle)
            if 'timestamp' not in candle:
                candle['timestamp'] = datetime.fromtimestamp(candle['time']).strftime('%Y-%m-%d %H:%M:%S')

            # Write row
            writer.writerow(candle)

    return len(candles)

async def select_model():
    """Select which model to use for trading"""
    print("\nAvailable models:")

    # Check which models are actually available
    available_models = []
    if hasattr(model_manager, 'models'):
        available_models = list(model_manager.models.keys())

    # Check if ensemble is available
    ensemble_available = hasattr(model_manager, 'ensemble') and model_manager.ensemble is not None

    # Define TensorFlow-based models in order of preference
    tf_models = ['lstm_gru', 'transformer', 'dqn']

    if TENSORFLOW_AVAILABLE:
        if ensemble_available:
            print("1. Ensemble (combines all models)")
        print("2. LSTM-GRU Deep Learning") if 'lstm_gru' in available_models else print("2. LSTM-GRU Deep Learning (not loaded)")
        print("3. Transformer") if 'transformer' in available_models else print("3. Transformer (not loaded)")
        print("4. XGBoost") if 'xgboost' in available_models else print("4. XGBoost (not loaded)")
        print("5. DQN Reinforcement Learning") if 'dqn' in available_models else print("5. DQN Reinforcement Learning (not loaded)")

        choice = input("\nSelect model (1-5, default: 1): ").strip()

        if not choice or choice == '1':
            if ensemble_available:
                return 'ensemble'
            else:
                # Prioritize TensorFlow models over XGBoost
                for model in tf_models:
                    if model in available_models:
                        print(f"Ensemble model not available, falling back to {model}")
                        return model

                # If no TensorFlow models are available, use XGBoost as last resort
                if 'xgboost' in available_models:
                    print("No TensorFlow models available, falling back to XGBoost")
                    return 'xgboost'
                elif available_models:
                    print(f"No preferred models available, falling back to {available_models[0]}")
                    return available_models[0]
                else:
                    print("No models available, please train models first")
                    # Force training of TensorFlow models
                    print("Will attempt to train TensorFlow models")
                    return 'lstm_gru'  # Default to LSTM-GRU for training
        elif choice == '2':
            return 'lstm_gru'
        elif choice == '3':
            return 'transformer'
        elif choice == '4':
            return 'xgboost'
        elif choice == '5':
            return 'dqn'
        else:
            print("Invalid choice or model not available")
            # Prioritize TensorFlow models
            for model in tf_models:
                if model in available_models:
                    print(f"Using {model} model")
                    return model

            # If no TensorFlow models are available, use any available model
            if available_models:
                print(f"Using {available_models[0]} model")
                return available_models[0]
            else:
                print("No models available, will attempt to train TensorFlow models")
                return 'lstm_gru'  # Default to LSTM-GRU for training
    else:
        print("TensorFlow is not available. Attempting to set up TensorFlow...")
        # Try to set up TensorFlow
        tf_available = setup_tensorflow(required=True)

        if tf_available:
            print("TensorFlow setup successful! Using TensorFlow models.")
            # Recursively call select_model to show updated options
            return await select_model()
        else:
            print("TensorFlow setup failed. Only XGBoost model can be used.")
            if 'xgboost' in available_models:
                print("4. XGBoost")
                return 'xgboost'
            else:
                print("XGBoost model not available, please train models first")
                return 'xgboost'  # Default fallback

async def enable_self_learning():
    """Enable self-learning capabilities with enhanced TensorFlow integration"""
    global ENABLE_SELF_LEARNING, LEARNING_MODE, LEARNING_RATE, MEMORY_RETENTION, MISTAKE_THRESHOLD, RETRAINING_INTERVAL, FEEDBACK_WINDOW
    global model_manager, enhanced_trainer

    print("\n" + "=" * 50)
    print("SELF-LEARNING CONFIGURATION")
    print("=" * 50)
    print("Self-learning allows the models to continuously improve based on trading results.")
    print("The system will learn from mistakes and adapt to changing market conditions.")

    # Check if TensorFlow is available or can be installed
    tf_available = is_tensorflow_available()

    if not tf_available and TENSORFLOW_REQUIRED:
        print("\nTensorFlow is required for enhanced self-learning capabilities.")
        print("Attempting to set up TensorFlow...")

        # Try to set up TensorFlow
        tf_available = setup_tensorflow(required=TENSORFLOW_REQUIRED)

        if not tf_available:
            print("\nWARNING: TensorFlow setup failed but is required for self-learning.")
            print("The system will continue with limited functionality.")
            print("Some advanced features may not work correctly.")

    # Default to enabled
    ENABLE_SELF_LEARNING = True
    print("\nSelf-learning is enabled by default for maximum accuracy improvement.")

    # Configure learning mode
    print("\nLearning Modes:")
    print("1. Active Learning - Updates models immediately after each trade (recommended)")
    print("2. Batch Learning - Updates models periodically in the background")

    mode_input = input("\nSelect learning mode (1/2, default: 1): ").strip()
    if not mode_input or mode_input == '1':
        LEARNING_MODE = 'active'
        print("Active learning mode selected")
    else:
        LEARNING_MODE = 'batch'
        print("Batch learning mode selected")

        # Configure retraining interval for batch mode
        interval_input = input(f"\nEnter hours between model retraining (default: {RETRAINING_INTERVAL}): ").strip()
        if interval_input:
            try:
                RETRAINING_INTERVAL = float(interval_input)
                if RETRAINING_INTERVAL < 1:
                    print("Interval too short, setting to minimum (1 hour)")
                    RETRAINING_INTERVAL = 1
            except ValueError:
                print(f"Invalid interval, using default ({RETRAINING_INTERVAL} hours)")

    # Configure learning parameters
    print("\nAdvanced Learning Parameters (press Enter to use defaults):")

    # Learning rate
    rate_input = input(f"Learning rate (0.1-0.5, default: {LEARNING_RATE}): ").strip()
    if rate_input:
        try:
            LEARNING_RATE = float(rate_input)
            if LEARNING_RATE < 0.1:
                print("Learning rate too low, setting to minimum (0.1)")
                LEARNING_RATE = 0.1
            elif LEARNING_RATE > 0.5:
                print("Learning rate too high, setting to maximum (0.5)")
                LEARNING_RATE = 0.5
        except ValueError:
            print(f"Invalid learning rate, using default ({LEARNING_RATE})")

    # Memory retention
    memory_input = input(f"Memory retention (0.5-0.9, default: {MEMORY_RETENTION}): ").strip()
    if memory_input:
        try:
            MEMORY_RETENTION = float(memory_input)
            if MEMORY_RETENTION < 0.5:
                print("Memory retention too low, setting to minimum (0.5)")
                MEMORY_RETENTION = 0.5
            elif MEMORY_RETENTION > 0.9:
                print("Memory retention too high, setting to maximum (0.9)")
                MEMORY_RETENTION = 0.9
        except ValueError:
            print(f"Invalid memory retention, using default ({MEMORY_RETENTION})")

    # Mistake threshold
    mistake_input = input(f"Consecutive mistake threshold (2-5, default: {MISTAKE_THRESHOLD}): ").strip()
    if mistake_input:
        try:
            MISTAKE_THRESHOLD = int(mistake_input)
            if MISTAKE_THRESHOLD < 2:
                print("Threshold too low, setting to minimum (2)")
                MISTAKE_THRESHOLD = 2
            elif MISTAKE_THRESHOLD > 5:
                print("Threshold too high, setting to maximum (5)")
                MISTAKE_THRESHOLD = 5
        except ValueError:
            print(f"Invalid threshold, using default ({MISTAKE_THRESHOLD})")

    # Feedback window
    feedback_input = input(f"Feedback window size (5-20, default: {FEEDBACK_WINDOW}): ").strip()
    if feedback_input:
        try:
            FEEDBACK_WINDOW = int(feedback_input)
            if FEEDBACK_WINDOW < 5:
                print("Window too small, setting to minimum (5)")
                FEEDBACK_WINDOW = 5
            elif FEEDBACK_WINDOW > 20:
                print("Window too large, setting to maximum (20)")
                FEEDBACK_WINDOW = 20
        except ValueError:
            print(f"Invalid window size, using default ({FEEDBACK_WINDOW})")

    # Update model configuration with new values
    MODEL_CONFIG.update({
        'adaptation_rate': LEARNING_RATE,
        'memory_factor': MEMORY_RETENTION,
        'mistake_threshold': MISTAKE_THRESHOLD,
        'feedback_window': FEEDBACK_WINDOW
    })

    # Initialize enhanced self-learning system
    print("\nInitializing enhanced self-learning system...")

    try:
        # Create enhanced trainer
        enhanced_trainer = EnhancedModelTrainer(
            data_collector=None,  # Will be set later
            feature_engineer=engineer_features,
            adaptation_rate=LEARNING_RATE,
            confidence_threshold=MIN_CONFIDENCE,
            memory_factor=MEMORY_RETENTION,
            retraining_threshold=0.6,
            optimization_interval=5,
            feedback_window=FEEDBACK_WINDOW
        )

        # Store the enhanced trainer in the model manager
        model_manager.enhanced_trainer = enhanced_trainer

        # Initialize the original self-learning system for compatibility
        model_trainer = model_manager.initialize_self_learning()

        if model_trainer is None and not tf_available:
            print("WARNING: Basic self-learning initialized without TensorFlow.")
            print("Some advanced learning features will be limited.")
        else:
            print("Enhanced self-learning system initialized successfully!")

            # Check for existing learning progress
            if os.path.exists('learning_progress/progress_report.json'):
                try:
                    with open('learning_progress/progress_report.json', 'r') as f:
                        progress_report = json.load(f)

                    # Display learning progress
                    print("\n" + "=" * 50)
                    print("LEARNING PROGRESS SUMMARY")
                    print("=" * 50)
                    print(f"Total trades recorded: {progress_report['overall']['total_trades']}")
                    print(f"Overall accuracy: {progress_report['overall']['accuracy']:.4f}")
                    print(f"Recent accuracy: {progress_report['recent']['accuracy']:.4f}")

                    # Show improvement metrics
                    if progress_report['short_term']['improvement'] != 0:
                        print(f"\nWeek-over-week improvement: {progress_report['short_term']['improvement']:.2f}%")
                        print(f"Learning trend: {progress_report['short_term']['trend']}")

                    # Show model performance
                    if progress_report['model_performance']:
                        print("\nModel Performance:")
                        for model, stats in progress_report['model_performance'].items():
                            print(f"  {model}: {stats['accuracy']:.4f} accuracy ({stats['correct']}/{stats['trades']} correct)")

                    # Show daily summary
                    if progress_report['daily_summary']:
                        print("\nDaily Progress:")
                        for date, stats in progress_report['daily_summary'].items():
                            if stats['trades'] > 0:
                                print(f"  {date}: {stats['accuracy']:.4f} accuracy ({stats['correct']}/{stats['trades']} correct)")

                    # Check if progress chart exists
                    if os.path.exists('learning_progress/progress_chart.png'):
                        print("\nLearning progress chart available at: learning_progress/progress_chart.png")
                except Exception as e:
                    print(f"Error reading learning progress: {e}")

        # Start continuous learning if in batch mode
        if LEARNING_MODE == 'batch':
            print(f"\nStarting continuous learning in background (interval: {RETRAINING_INTERVAL} hours)...")

            # Try to use enhanced trainer first
            if hasattr(enhanced_trainer, 'start_continuous_learning'):
                learning_thread = enhanced_trainer.start_continuous_learning(RETRAINING_INTERVAL)
                if learning_thread:
                    print("Enhanced continuous learning started")
                    model_manager.learning_thread = learning_thread
                else:
                    # Fall back to original trainer
                    learning_thread = model_manager.start_continuous_learning(RETRAINING_INTERVAL)
                    if learning_thread:
                        print("Basic continuous learning started")
                    else:
                        print("Failed to start continuous learning")
            else:
                # Fall back to original trainer
                learning_thread = model_manager.start_continuous_learning(RETRAINING_INTERVAL)
                if learning_thread:
                    print("Basic continuous learning started")
                else:
                    print("Failed to start continuous learning")

        print("\nSelf-learning system initialized and ready")
        print("-" * 50)

    except Exception as e:
        print(f"Error initializing enhanced self-learning: {e}")
        import traceback
        traceback.print_exc()

        print("\nFalling back to basic self-learning...")
        model_trainer = model_manager.initialize_self_learning()

        if model_trainer is None:
            print("Failed to initialize even basic self-learning.")
            print("Self-learning will be disabled.")
            ENABLE_SELF_LEARNING = False

    return ENABLE_SELF_LEARNING

async def configure_future_predictions():
    """Configure future candle prediction settings"""
    global PREDICT_FUTURE_CANDLES, FUTURE_CANDLES_COUNT, PREDICTION_HORIZON, ACCURACY_THRESHOLD, CONFIRMATION_COUNT

    if not TENSORFLOW_AVAILABLE:
        print("\nFuture candle prediction with multiple models requires TensorFlow, which is not available.")
        print("Future candle prediction will be limited to XGBoost model only.")

        # Set minimal future prediction settings for XGBoost only
        PREDICT_FUTURE_CANDLES = True
        CONFIRMATION_COUNT = 1  # Only one model available

        # Update model configuration
        MODEL_CONFIG.update({
            'predict_future': PREDICT_FUTURE_CANDLES,
            'future_candles_count': FUTURE_CANDLES_COUNT,
            'prediction_horizons': PREDICTION_HORIZON,
            'accuracy_threshold': ACCURACY_THRESHOLD,
            'confirmation_count': CONFIRMATION_COUNT
        })

        return PREDICT_FUTURE_CANDLES

    print("\n" + "=" * 50)
    print("FUTURE CANDLE PREDICTION CONFIGURATION")
    print("=" * 50)
    print("Future candle prediction allows the system to predict multiple candles ahead")
    print("and use those predictions to confirm trading signals.")

    enable_input = input("\nEnable future candle prediction? (y/n, default: y): ").strip().lower()

    if enable_input != 'n':
        PREDICT_FUTURE_CANDLES = True
        print("\nFuture candle prediction enabled!")

        # Configure prediction horizons
        print("\nPrediction horizons are the number of minutes into the future to predict.")
        print("Enter comma-separated values (e.g., 1,3,5)")

        horizons_input = input(f"Enter prediction horizons (default: {','.join(map(str, PREDICTION_HORIZON))}): ").strip()
        if horizons_input:
            try:
                horizons = [int(h.strip()) for h in horizons_input.split(',')]
                if all(h > 0 for h in horizons):
                    PREDICTION_HORIZON = horizons
                else:
                    print("All horizons must be positive, using default")
            except ValueError:
                print("Invalid horizons, using default")

        # Configure confirmation count
        print("\nConfirmation count is the minimum number of models that must agree on a prediction")
        print("for it to be considered valid.")

        confirmation_input = input(f"Enter confirmation count (2-4, default: {CONFIRMATION_COUNT}): ").strip()
        if confirmation_input:
            try:
                count = int(confirmation_input)
                if 2 <= count <= 4:
                    CONFIRMATION_COUNT = count
                else:
                    print("Count must be between 2 and 4, using default")
            except ValueError:
                print("Invalid count, using default")

        # Configure accuracy threshold
        print("\nAccuracy threshold is the minimum confidence required for future predictions")
        print("to be considered in trading decisions.")

        threshold_input = input(f"Enter accuracy threshold (0.6-0.9, default: {ACCURACY_THRESHOLD}): ").strip()
        if threshold_input:
            try:
                threshold = float(threshold_input)
                if 0.6 <= threshold <= 0.9:
                    ACCURACY_THRESHOLD = threshold
                else:
                    print("Threshold must be between 0.6 and 0.9, using default")
            except ValueError:
                print("Invalid threshold, using default")

        # Update model configuration
        MODEL_CONFIG.update({
            'predict_future': PREDICT_FUTURE_CANDLES,
            'future_candles_count': FUTURE_CANDLES_COUNT,
            'prediction_horizons': PREDICTION_HORIZON,
            'accuracy_threshold': ACCURACY_THRESHOLD,
            'confirmation_count': CONFIRMATION_COUNT
        })

        print("\nFuture prediction settings:")
        print(f"Prediction horizons: {PREDICTION_HORIZON}")
        print(f"Confirmation count: {CONFIRMATION_COUNT}")
        print(f"Accuracy threshold: {ACCURACY_THRESHOLD}")
    else:
        PREDICT_FUTURE_CANDLES = False
        print("Future candle prediction disabled")

    return PREDICT_FUTURE_CANDLES

async def set_trading_parameters():
    """Set trading parameters"""
    global AMOUNT, EXPIRATION, MAX_TRADES, MIN_CONFIDENCE

    try:
        # Set trade amount
        amount_input = input(f"\nEnter trade amount (default: {AMOUNT}): ").strip()
        if amount_input:
            AMOUNT = float(amount_input)

        # Set expiration time
        expiration_input = input(f"Enter expiration time in seconds (default: {EXPIRATION}): ").strip()
        if expiration_input:
            EXPIRATION = int(expiration_input)

        # Maximum trades is now unlimited
        print("Trading will continue indefinitely until the program is stopped.")

        # Set confidence threshold
        confidence_input = input(f"Enter minimum confidence threshold (0.5-1.0, default: {MIN_CONFIDENCE}): ").strip()
        if confidence_input:
            MIN_CONFIDENCE = float(confidence_input)
            if MIN_CONFIDENCE < 0.5 or MIN_CONFIDENCE > 1.0:
                print("Invalid confidence threshold, using default")
                MIN_CONFIDENCE = 0.65

        print("\nTrading parameters:")
        print(f"Trade amount: {AMOUNT}")
        print(f"Expiration time: {EXPIRATION} seconds")
        print(f"Trading mode: Unlimited (will trade indefinitely)")
        print(f"Minimum confidence: {MIN_CONFIDENCE}")

        # Ask if user wants to configure future predictions
        future_input = input("\nDo you want to configure future candle prediction? (y/n, default: y): ").strip().lower()

        if future_input != 'n':
            await configure_future_predictions()

        # Ask if user wants to enable self-learning
        self_learning_input = input("\nDo you want to configure self-learning? (y/n, default: n): ").strip().lower()

        if self_learning_input == 'y':
            await enable_self_learning()

    except ValueError as e:
        print(f"Error setting parameters: {e}")
        print("Using default values")

async def train_models_with_historical_data(asset, timeframe=60):
    """
    Train models using historical data

    Parameters:
    - asset: Asset to train on
    - timeframe: Timeframe in seconds

    Returns:
    - success: Whether training was successful
    - models: Dictionary of trained models
    """
    print("\n" + "=" * 60)
    print("TRAINING MODELS WITH HISTORICAL DATA")
    print("=" * 60)

    # Check if models are already trained and we're not forcing retraining
    models_exist = model_manager.check_models_exist()
    if models_exist and not FORCE_RETRAIN:
        print("Models already exist and FORCE_RETRAIN is False.")
        print("Loading existing models...")
        models = model_manager.load_models()
        return True, models

    # Load historical data
    print(f"Loading {INITIAL_HISTORY_DAYS} days of historical data for {asset}...")
    candles, metadata = await load_and_cache_candles(asset, timeframe=timeframe, is_initial_load=True)

    if not candles:
        print("Error: Failed to load historical data")
        return False, None

    print(f"Successfully loaded {len(candles)} candles from {metadata['oldest_candle_time']} to {metadata['newest_candle_time']}")

    # Convert candles to DataFrame
    print("Converting candles to DataFrame...")
    df_candles = pd.DataFrame(candles)

    # Apply feature engineering
    print("Applying feature engineering...")
    featured_data = engineer_features(df_candles)

    # Save engineered data to cache for faster loading next time
    engineered_cache_file = os.path.join(CACHE_DIR, f"{asset}_{timeframe}_engineered.pkl")
    joblib.dump(featured_data, engineered_cache_file)
    print(f"Saved engineered data to cache: {engineered_cache_file}")

    # Train models
    print("Training models...")
    try:
        global TENSORFLOW_AVAILABLE
        # Check if TensorFlow is available
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available. Attempting to set up TensorFlow...")
            # Try to set up TensorFlow
            tf_available = setup_tensorflow(required=True)

            if tf_available:
                print("TensorFlow setup successful! Using TensorFlow models for training.")
                TENSORFLOW_AVAILABLE = True
            else:
                print("TensorFlow setup failed. Will use limited model training.")

        # Check if we have an enhanced trainer
        if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
            print("Using enhanced training system with TensorFlow integration...")

            # Set data collector for enhanced trainer if needed
            if model_manager.enhanced_trainer.data_collector is None:
                # Create a simple data collector object
                class SimpleDataCollector:
                    async def fetch_historical_data(self, lookback_days=30):
                        return featured_data

                    def preprocess_data(self, data):
                        return data

                model_manager.enhanced_trainer.data_collector = SimpleDataCollector()

            # Use enhanced trainer for initial training
            if hasattr(model_manager.enhanced_trainer, 'initial_training'):
                print("Using enhanced initial training with TensorFlow...")
                models = model_manager.enhanced_trainer.initial_training(featured_data)
            else:
                # Fall back to regular trainer
                print("Enhanced trainer doesn't support initial_training, falling back...")
                model_trainer = model_manager.initialize_self_learning()
                models = model_trainer.initial_training(featured_data)
        else:
            # Initialize regular self-learning system
            print("Using standard training system...")
            model_trainer = model_manager.initialize_self_learning()

            if model_trainer is None:
                if TENSORFLOW_AVAILABLE:
                    print("Failed to initialize self-learning system. Creating a new enhanced trainer...")
                    # Try to create a new enhanced trainer
                    from Models.Enhanced_Self_Learning import EnhancedModelTrainer

                    # Create enhanced trainer
                    enhanced_trainer = EnhancedModelTrainer(
                        data_collector=None,  # Will be set later
                        feature_engineer=engineer_features,
                        adaptation_rate=LEARNING_RATE,
                        confidence_threshold=MIN_CONFIDENCE,
                        memory_factor=MEMORY_RETENTION,
                        retraining_threshold=0.6,
                        optimization_interval=5,
                        feedback_window=FEEDBACK_WINDOW
                    )

                    # Create a simple data collector object
                    class SimpleDataCollector:
                        async def fetch_historical_data(self, lookback_days=30):
                            return featured_data

                        def preprocess_data(self, data):
                            return data

                    enhanced_trainer.data_collector = SimpleDataCollector()

                    # Store the enhanced trainer in the model manager
                    model_manager.enhanced_trainer = enhanced_trainer

                    print("Using newly created enhanced trainer for initial training...")
                    models = enhanced_trainer.initial_training(featured_data)
                else:
                    print("Failed to initialize self-learning system. Using basic model training.")
                    # Basic model training (fallback)
                    models = model_manager.train_models(featured_data)
            else:
                # Advanced training with self-learning
                print("Using advanced training with self-learning capabilities...")
                models = model_trainer.initial_training(featured_data)

        if models:
            print("Model training completed successfully!")
            return True, models
        else:
            print("Error: Model training failed")
            return False, None
    except Exception as e:
        print(f"Error during model training: {e}")
        return False, None

async def monitor_candles():
    """Monitor candles in real-time and save to CSV"""
    global FEEDBACK_WINDOW
    print(f"\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully.")

        # Get asset information
        asset_name, asset_data = await get_asset_info(ASSET)

        # Get initial balance
        initial_balance = await client.get_balance()
        print(f"Initial Balance: {initial_balance}")

        # Select model to use
        model_name = await select_model()

        # Set trading parameters
        await set_trading_parameters()

        # Train models with historical data (or load existing models)
        print("\nPreparing models...")
        training_success, models = await train_models_with_historical_data(ASSET, timeframe=60)

        if not training_success:
            print("Warning: Model training failed. Attempting to load existing models...")
            # Try to load models anyway
            models = model_manager.load_models()

            if not models:
                print("Error: Failed to load models. Cannot continue.")
                return

        # Trading statistics
        trades_executed = 0
        wins = 0
        losses = 0
        model_performance = {}
        trade_results = []  # List of trade results (True for win, False for loss)
        trade_reasons = []  # List of trade reasons (direction, reason)

        print(f"\nStarting to monitor {asset_name} candles...")
        print(f"Saving candles to: {CSV_FILENAME}")
        print(f"Using model: {model_name}")
        print(f"Minimum confidence: {MIN_CONFIDENCE}")
        print(f"Trading mode: Unlimited (will trade indefinitely)")
        print("-" * 60)

        # Initialize candles history
        # First load historical data from cache
        print("Loading historical data from cache...")
        candles_history, metadata = await load_and_cache_candles(ASSET, timeframe=60, is_initial_load=False)

        if candles_history:
            print(f"Loaded {len(candles_history)} candles from cache")
            print(f"Oldest candle: {metadata['oldest_candle_time']}")
            print(f"Newest candle: {metadata['newest_candle_time']}")

            # Save loaded candles to CSV
            saved_count = save_candles_to_csv(candles_history[-10:], CSV_FILENAME)  # Save last 10 candles
            print(f"Saved {saved_count} candles to {CSV_FILENAME}")
        else:
            print("No historical data found in cache. Starting with empty history.")
            candles_history = []

        # Start time for monitoring duration
        start_time = time.time()
        last_candle_time = candles_history[-1]['time'] if candles_history else 0

        # Start monitoring
        try:
            while (time.time() - start_time) < MAX_MONITORING_TIME:  # Removed trade limit check
                # Get current time
                current_time = time.time()

                # Get the latest candles - only fetch what we need
                candles_needed = 10  # Fetch a small batch to check for new candles
                candles = await client.get_candles(ASSET, current_time, candles_needed, 60)

                if len(candles) > 0:
                    # Process candles if needed
                    if not candles[0].get("open"):
                        candles = process_candles(candles, 60)

                    # Find new candles (those with time > last_candle_time)
                    new_candles = [c for c in candles if c['time'] > last_candle_time]

                    if new_candles:
                        for new_candle in new_candles:
                            # Add color to candle
                            new_candle['color'] = get_color(new_candle)

                            # Add to history
                            candles_history.append(new_candle)
                            last_candle_time = new_candle['time']

                            # Save to CSV
                            saved_count = save_candles_to_csv([new_candle], CSV_FILENAME)

                            # Print information
                            print(f"\nNew candle: {datetime.fromtimestamp(new_candle['time'])}")
                            print(f"Open: {new_candle['open']}, Close: {new_candle['close']}, Color: {new_candle['color']}")
                            print(f"Saved to {CSV_FILENAME} (Total candles: {len(candles_history)})")

                            # Learn from the new candle if self-learning is enabled
                            if ENABLE_SELF_LEARNING and len(candles_history) >= 5:  # Reduced minimum requirement to 5 candles
                                print("\nLearning from new candle...")
                                # Try to use enhanced trainer first if available
                                if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                                    # Get previous candles for context - use all available up to 30
                                    max_previous = min(30, len(candles_history)-1)
                                    previous_candles = candles_history[-max_previous:-1] if max_previous > 0 else []

                                    print(f"Using {len(previous_candles)} previous candles for context")

                                    # Learn from the new candle with previous candles for context
                                    learning_result = model_manager.enhanced_trainer.learn_from_candle(
                                        new_candle,
                                        previous_candles=previous_candles
                                    )

                                    if learning_result:
                                        print("✅ Successfully learned from new candle")
                                    else:
                                        print("⚠️ Could not learn from new candle - will try again with more data")
                                else:
                                    # Initialize enhanced trainer if not available
                                    try:
                                        print("Enhanced trainer not available, attempting to initialize...")
                                        from Models.Enhanced_Self_Learning import EnhancedModelTrainer

                                        # Create enhanced trainer
                                        enhanced_trainer = EnhancedModelTrainer(
                                            data_collector=None,  # Will be set later
                                            feature_engineer=engineer_features,
                                            adaptation_rate=LEARNING_RATE,
                                            confidence_threshold=MIN_CONFIDENCE,
                                            memory_factor=MEMORY_RETENTION,
                                            retraining_threshold=0.6,
                                            optimization_interval=5,
                                            feedback_window=FEEDBACK_WINDOW
                                        )

                                        # Store the enhanced trainer in the model manager
                                        model_manager.enhanced_trainer = enhanced_trainer
                                        print("Successfully initialized enhanced trainer")

                                        # Try learning with the new trainer
                                        max_previous = min(30, len(candles_history)-1)
                                        previous_candles = candles_history[-max_previous:-1] if max_previous > 0 else []

                                        learning_result = model_manager.enhanced_trainer.learn_from_candle(
                                            new_candle,
                                            previous_candles=previous_candles
                                        )

                                        if learning_result:
                                            print("✅ Successfully learned from new candle with newly initialized trainer")
                                        else:
                                            print("⚠️ Could not learn from new candle with new trainer")
                                    except Exception as e:
                                        print(f"Error initializing enhanced trainer: {e}")
                                        print("Falling back to basic learning mechanism")

                            # Update cache with new candle
                            cache_key = f"{ASSET}_{60}_history"
                            cache_file = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
                            if os.path.exists(cache_file):
                                try:
                                    cached_data = joblib.load(cache_file)
                                    cached_candles = cached_data.get('candles', [])
                                    cached_candles.append(new_candle)
                                    joblib.dump({
                                        'candles': cached_candles,
                                        'last_update_time': time.time()
                                    }, cache_file)
                                except Exception as e:
                                    print(f"Error updating cache: {e}")
                    else:
                        # No new candles, just wait
                        await asyncio.sleep(1)

                    # Analyze strategy if we have enough candles
                    if len(candles_history) >= MIN_CANDLES:
                        # Use enhanced analyze_strategy that returns detailed analysis
                        direction, reason, details = await analyze_strategy(candles_history, model_name, MIN_CONFIDENCE)

                        if direction:
                            print(f"\nSignal detected: {direction} - {reason}")

                            # Execute trade with prediction details for self-learning
                            success, profit, trade_data = await execute_trade(ASSET, direction, AMOUNT, EXPIRATION, details)

                            # Record trade in trading statistics
                            trading_stats['trade_history'].append(trade_data)

                            # Record trade result and reason
                            trade_results.append(success)
                            trade_reasons.append((direction, reason))

                            # Record model performance
                            model_used = details['current_prediction']['model'] if details else reason.split()[0]
                            if model_used not in model_performance:
                                model_performance[model_used] = {'wins': 0, 'losses': 0}

                            trades_executed += 1
                            if success:
                                wins += 1
                                model_performance[model_used]['wins'] += 1

                                # Update win rate in model performance
                                model_performance[model_used]['win_rate'] = model_performance[model_used]['wins'] / (
                                    model_performance[model_used]['wins'] + model_performance[model_used]['losses']
                                ) if (model_performance[model_used]['wins'] + model_performance[model_used]['losses']) > 0 else 0

                                # Print updated model performance
                                print(f"\nModel performance update:")
                                for model, perf in model_performance.items():
                                    win_rate = perf.get('win_rate', 0)
                                    print(f"  {model}: {perf['wins']} wins, {perf.get('losses', 0)} losses, {win_rate:.2f} win rate")
                            else:
                                losses += 1
                                if model_used in model_performance:
                                    model_performance[model_used]['losses'] = model_performance[model_used].get('losses', 0) + 1

                                    # Update win rate in model performance
                                    model_performance[model_used]['win_rate'] = model_performance[model_used]['wins'] / (
                                        model_performance[model_used]['wins'] + model_performance[model_used]['losses']
                                    ) if (model_performance[model_used]['wins'] + model_performance[model_used]['losses']) > 0 else 0

                            # Update trading statistics
                            trading_stats['model_performance'] = model_performance

                            # Apply batch learning if enabled
                            if ENABLE_SELF_LEARNING and LEARNING_MODE == 'batch' and trades_executed % 5 == 0:
                                print("\nApplying batch learning from recent trades...")
                                # Try to use enhanced trainer first if available
                                if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                                    print("Using enhanced batch learning...")
                                    # Learn from recent trades
                                    model_manager.enhanced_trainer.batch_learn_from_trades(trading_stats['trade_history'][-FEEDBACK_WINDOW:])

                                    # Also learn from recent candles
                                    if len(candles_history) >= 50:
                                        print("Also learning from recent candles...")
                                        recent_candles = candles_history[-50:]  # Get the last 50 candles
                                        candle_learning_result = model_manager.enhanced_trainer.learn_from_candles_batch(recent_candles)
                                        if candle_learning_result:
                                            print("✅ Successfully learned from recent candles batch")
                                        else:
                                            print("⚠️ Could not learn from recent candles batch")
                                else:
                                    # Fall back to original method
                                    model_manager.batch_learn_from_trades(trading_stats['trade_history'][-5:])

                            # Print trading summary
                            win_rate = wins / trades_executed if trades_executed > 0 else 0
                            print(f"\nTrading summary: {wins}/{trades_executed} wins ({win_rate:.2f}), P/L: ${trading_stats['profit_loss']:.2f}")
                            print(f"Consecutive wins: {trading_stats['consecutive_wins']}, Consecutive losses: {trading_stats['consecutive_losses']}")
                            print(f"Max profit: ${trading_stats['max_profit']:.2f}, Max drawdown: ${trading_stats['max_drawdown']:.2f}")

                            # Wait a bit before next analysis to avoid rapid trading
                            print("\nWaiting 60 seconds before next analysis...")
                            await asyncio.sleep(60)

                            # Print statistics
                            print(f"\nTrades executed: {trades_executed}")
                            print(f"Wins: {wins}, Losses: {losses}")
                            if trades_executed > 0:
                                print(f"Win rate: {(wins/trades_executed)*100:.2f}%")

                            # Print model performance
                            print("\nModel Performance:")
                            for model, stats in model_performance.items():
                                total = stats['wins'] + stats['losses']
                                if total > 0:
                                    win_rate = (stats['wins'] / total) * 100
                                    print(f"{model}: {stats['wins']} wins, {stats['losses']} losses, {win_rate:.2f}% win rate")

                            # Get current balance
                            current_balance = await client.get_balance()
                            print(f"Current Balance: {current_balance} (Change: {current_balance - initial_balance})")

                            # Wait for the next candle
                            await asyncio.sleep(60)

                # Enhanced periodic learning from all candles (every 3 minutes)
                current_minute = datetime.now().minute
                if ENABLE_SELF_LEARNING and current_minute % 3 == 0 and len(candles_history) >= 30:  # Reduced minimum requirement
                    # Check if we haven't done this learning in the last minute
                    current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M')
                    last_learning_time = getattr(model_manager, 'last_periodic_learning_time', '')

                    if current_time_str != last_learning_time:
                        print(f"\n[{current_time_str}] Performing enhanced periodic learning from all candles...")
                        # Store the current time to avoid repeated learning in the same minute
                        model_manager.last_periodic_learning_time = current_time_str

                        # Try to use enhanced trainer first if available
                        if hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                            # Learn from a batch of recent candles - use all available up to 200
                            max_candles = min(200, len(candles_history))
                            recent_candles = candles_history[-max_candles:]
                            print(f"Learning from batch of {len(recent_candles)} recent candles...")

                            learning_result = model_manager.enhanced_trainer.learn_from_candles_batch(recent_candles)

                            if learning_result:
                                print("✅ Successfully performed periodic learning from candles")

                                # Generate and save progress report
                                try:
                                    report = model_manager.enhanced_trainer.progress_tracker.generate_progress_report()
                                    print("\nLearning Progress Summary:")
                                    print(f"Overall accuracy: {report['overall']['accuracy']:.4f}")
                                    print(f"Recent accuracy: {report['recent']['accuracy']:.4f}")

                                    # Show improvement metrics
                                    if report['short_term']['improvement'] != 0:
                                        print(f"Week-over-week improvement: {report['short_term']['improvement']:.2f}%")
                                        print(f"Learning trend: {report['short_term']['trend']}")

                                    # Save progress chart
                                    model_manager.enhanced_trainer.progress_tracker.plot_learning_progress(
                                        save_path='learning_progress/progress_chart.png'
                                    )
                                except Exception as e:
                                    print(f"Error generating progress report: {e}")
                            else:
                                print("⚠️ Could not perform periodic learning from candles")

                                # Try initializing a new trainer if learning failed
                                try:
                                    print("Attempting to reinitialize enhanced trainer...")
                                    from Models.Enhanced_Self_Learning import EnhancedModelTrainer

                                    # Create enhanced trainer with updated parameters
                                    enhanced_trainer = EnhancedModelTrainer(
                                        data_collector=None,
                                        feature_engineer=engineer_features,
                                        adaptation_rate=LEARNING_RATE * 1.2,  # Increase learning rate
                                        confidence_threshold=MIN_CONFIDENCE,
                                        memory_factor=MEMORY_RETENTION,
                                        retraining_threshold=0.55,  # Lower threshold to encourage retraining
                                        optimization_interval=3,  # More frequent optimization
                                        feedback_window=FEEDBACK_WINDOW
                                    )

                                    # Store the enhanced trainer in the model manager
                                    model_manager.enhanced_trainer = enhanced_trainer
                                    print("Successfully reinitialized enhanced trainer with improved parameters")
                                except Exception as e:
                                    print(f"Error reinitializing enhanced trainer: {e}")
                        else:
                            # Initialize enhanced trainer if not available
                            try:
                                print("Enhanced trainer not available, attempting to initialize...")
                                from Models.Enhanced_Self_Learning import EnhancedModelTrainer

                                # Create enhanced trainer
                                enhanced_trainer = EnhancedModelTrainer(
                                    data_collector=None,
                                    feature_engineer=engineer_features,
                                    adaptation_rate=LEARNING_RATE,
                                    confidence_threshold=MIN_CONFIDENCE,
                                    memory_factor=MEMORY_RETENTION,
                                    retraining_threshold=0.6,
                                    optimization_interval=5,
                                    feedback_window=FEEDBACK_WINDOW
                                )

                                # Store the enhanced trainer in the model manager
                                model_manager.enhanced_trainer = enhanced_trainer
                                print("Successfully initialized enhanced trainer")
                            except Exception as e:
                                print(f"Error initializing enhanced trainer: {e}")

                # Wait before checking again
                await asyncio.sleep(5)

        except KeyboardInterrupt:
            print("\nMonitoring interrupted by user.")

        # Final statistics
        print("\n" + "=" * 60)
        print("MONITORING SESSION SUMMARY")
        print("=" * 60)
        print(f"Total candles captured: {len(candles_history)}")
        print(f"Data saved to: {CSV_FILENAME}")
        print(f"Monitoring duration: {time.time() - start_time:.2f} seconds")

        if trades_executed > 0:
            print("\nTRADING STATISTICS:")
            print(f"Trades executed: {trades_executed}")
            print(f"Wins: {wins}, Losses: {losses}")
            print(f"Win rate: {(wins/trades_executed)*100:.2f}%")

            # Print model performance
            print("\nMODEL PERFORMANCE:")
            for model, stats in model_performance.items():
                total = stats['wins'] + stats['losses']
                if total > 0:
                    win_rate = (stats['wins'] / total) * 100
                    print(f"{model}: {stats['wins']} wins, {stats['losses']} losses, {win_rate:.2f}% win rate")

            # Get final balance
            final_balance = await client.get_balance()
            print(f"Initial Balance: {initial_balance}")
            print(f"Final Balance: {final_balance}")
            print(f"Profit/Loss: {final_balance - initial_balance}")

            # Display learning progress if self-learning is enabled
            if ENABLE_SELF_LEARNING and hasattr(model_manager, 'enhanced_trainer') and model_manager.enhanced_trainer:
                try:
                    # Get progress tracker from enhanced trainer
                    progress_tracker = model_manager.enhanced_trainer.progress_tracker

                    # Generate progress report
                    report = progress_tracker.generate_progress_report()

                    print("\n" + "=" * 60)
                    print("LEARNING PROGRESS SUMMARY")
                    print("=" * 60)
                    print(f"Total trades recorded: {report['overall']['total_trades']}")
                    print(f"Overall accuracy: {report['overall']['accuracy']:.4f}")
                    print(f"Recent accuracy: {report['recent']['accuracy']:.4f}")
                    print(f"Learning iterations: {report['overall']['learning_iterations']}")

                    # Show improvement metrics
                    if report['short_term']['improvement'] != 0:
                        print(f"\nWeek-over-week improvement: {report['short_term']['improvement']:.2f}%")
                        print(f"Learning trend: {report['short_term']['trend']}")

                    # Show daily summary
                    if report['daily_summary']:
                        print("\nDaily Progress:")
                        for date, stats in sorted(report['daily_summary'].items()):
                            if stats['trades'] > 0:
                                print(f"  {date}: {stats['accuracy']:.4f} accuracy ({stats['correct']}/{stats['trades']} correct)")

                    # Generate and save progress chart
                    try:
                        progress_tracker.plot_learning_progress(save_path='learning_progress/progress_chart.png')
                        print("\nLearning progress chart updated: learning_progress/progress_chart.png")
                    except Exception as e:
                        print(f"Error saving progress chart: {e}")
                except Exception as e:
                    print(f"Error displaying learning progress: {e}")

            # Print future prediction information if enabled
            if PREDICT_FUTURE_CANDLES:
                print("\nFUTURE PREDICTION INFORMATION:")
                print(f"Prediction horizons: {PREDICTION_HORIZON}")
                print(f"Confirmation threshold: {CONFIRMATION_COUNT} models")
                print(f"Accuracy threshold: {ACCURACY_THRESHOLD}")

                # Count trades with future confirmation
                future_confirmed_trades = sum(1 for reason in [reason for _, reason in trade_reasons]
                                             if "future horizons confirming" in reason)

                if future_confirmed_trades > 0:
                    future_win_rate = (sum(1 for success, reason in zip(trade_results, trade_reasons)
                                          if success and "future horizons confirming" in reason[1]) /
                                      future_confirmed_trades) * 100

                    print(f"Trades with future confirmation: {future_confirmed_trades}")
                    print(f"Future confirmation win rate: {future_win_rate:.2f}%")

                    # Compare with non-future confirmed trades
                    non_future_trades = trades_executed - future_confirmed_trades
                    if non_future_trades > 0:
                        non_future_win_rate = (sum(1 for success, reason in zip(trade_results, trade_reasons)
                                                 if success and "future horizons confirming" not in reason[1]) /
                                             non_future_trades) * 100

                        print(f"Trades without future confirmation: {non_future_trades}")
                        print(f"Non-future confirmation win rate: {non_future_win_rate:.2f}%")

                        # Show improvement
                        if future_win_rate > non_future_win_rate:
                            print(f"Future prediction improved win rate by {future_win_rate - non_future_win_rate:.2f}%")

            # Print self-learning information if enabled
            if ENABLE_SELF_LEARNING and hasattr(model_manager, 'model_trainer'):
                print("\nSELF-LEARNING INFORMATION:")
                performance_summary = model_manager.model_trainer.get_performance_summary()

                if 'overall_accuracy' in performance_summary:
                    print(f"Overall accuracy: {performance_summary['overall_accuracy']:.4f}")

                if 'high_confidence_accuracy' in performance_summary:
                    print(f"High confidence accuracy: {performance_summary['high_confidence_accuracy']:.4f}")

                if 'ensemble_weights' in performance_summary:
                    print("\nCurrent ensemble weights:")
                    for model_type, weight in performance_summary['ensemble_weights'].items():
                        print(f"  {model_type}: {weight:.4f}")

                # Ask if user wants to stop continuous learning
                if hasattr(model_manager, 'learning_thread') and model_manager.learning_thread.is_alive():
                    stop_learning = input("\nStop continuous learning? (y/n, default: n): ").strip().lower()
                    if stop_learning == 'y':
                        model_manager.stop_continuous_learning()
                        print("Continuous learning stopped")
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def main():
    try:
        # Select account type
        account_type = await select_account()

        # Start monitoring candles
        await monitor_candles()
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    # Check if we're running in a PyQt application
    from PyQt5.QtWidgets import QApplication

    # If QApplication.instance() returns a value, we're in a PyQt app
    if QApplication.instance():
        # Import our async utilities
        try:
            from async_utils import async_helper

            # Use our async helper to run the main function
            async_helper.run_coroutine(main())
        except ImportError:
            # If async_utils is not available, create a simple helper
            print("Warning: async_utils not available, using simple async helper")

            # Create a timer to run the main function
            from PyQt5.QtCore import QTimer

            # Create a function to run the main coroutine
            def run_main():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(main())
                finally:
                    loop.close()

            # Use a timer to run the function after the event loop starts
            QTimer.singleShot(0, run_main)
    else:
        # Standard asyncio approach for non-PyQt applications
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(main())
        finally:
            loop.close()
