# Instalação e Configuração do PyQuotex

## Índice
- [Requisitos do Sistema](#requisitos-do-sistema)
- [Instalação](#instalação)
  - [Via pip](#via-pip)
  - [Via GitHub](#via-github)
- [Configuração Inicial](#configuração-inicial)
- [Gerenciamento de Credenciais](#gerenciamento-de-credenciais)
- [Configuração SSL/TLS](#configuração-ssltls)

## Requisitos do Sistema

Para utilizar o PyQuotex, você precisará de:

- Python 3.8 ou superior
- OpenSSL na versão mais recente
- Sistema operacional compatível:
  - Linux
  - Windows
  - macOS

### Dependências Principais
```
websocket-client>=1.8.0
requests>=2.31.0
beautifulsoup4>=4.12.2
```

### Dependências Opcionais
```
playwright>=1.44.0
numpy>=2.2.3,<3.0.0
playwright-stealth>=1.0.6,<2.0.0
```

## Instalação

### Via pip
Você pode instalar o PyQuotex diretamente do GitHub usando pip:

```bash
pip install git+https://github.com/cleitonleonel/pyquotex.git
```

### Via GitHub e Poetry
Você também pode clonar o repositório e realizar uma instalação local:
- Primeiro [Instale Poetry](https://python-poetry.org/docs/#installing-with-the-official-installer)

```bash
git clone https://github.com/cleitonleonel/pyquotex.git
cd pyquotex
poetry install
```

### Instalação de Navegadores para Playwright
Após instalar o PyQuotex, você precisa instalar os navegadores necessários para o Playwright:

```bash
playwright install
```

## Configuração Inicial

Para começar a usar o PyQuotex, primeiro você deve importar e configurar o cliente:

```python
from quotexapi.stable_api import Quotex

client = Quotex(
    email="<EMAIL>",
    password="sua_senha",
    lang="pt"  # Idioma padrão: "pt" (português)
)

# Habilitar modo debug (opcional)
client.debug_ws_enable = True
```

## Gerenciamento de Credenciais

Existem duas formas principais de gerenciar as credenciais:

### 1. Arquivo de Configuração
O PyQuotex procurará automaticamente um arquivo `config.ini` na pasta `settings`. Se não existir, ele o criará solicitando as credenciais:

```ini
[settings]
email=<EMAIL>
password=sua_senha
```

### 2. Configuração Direta
Você pode fornecer as credenciais diretamente ao criar a instância do cliente:

```python
cliente = Quotex(
    email="<EMAIL>",
    password="sua_senha"
)
```

## Configuração SSL/TLS

### Windows
Para Windows, é necessário instalar a versão mais recente do OpenSSL:
1. Baixe o instalador em [Openssl-Windows](https://slproweb.com/products/Win32OpenSSL.html)
2. Instale seguindo as instruções do instalador

### Linux
Em sistemas Linux, atualize o OpenSSL usando o gerenciador de pacotes:

```bash
sudo apt update
sudo apt install openssl
```

### Configuração SSL no Código
O PyQuotex gerencia automaticamente a configuração SSL, mas você pode personalizá-la:

```python
import ssl
import certifi

# Configuração do contexto SSL para usar TLS 1.3
ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1 | ssl.OP_NO_TLSv1_2
ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
ssl_context.load_verify_locations(certifi.where())
```

### Gerenciamento de Certificados
O PyQuotex utiliza certificados SSL para conexões seguras:

```python
import os
import certifi

# Configurar o caminho do certificado
cert_path = os.path.join("../", "quotex.pem")
os.environ['SSL_CERT_FILE'] = cert_path
os.environ['WEBSOCKET_CLIENT_CA_BUNDLE'] = cert_path
```

---

Para mais informações e suporte, você pode se juntar ao [grupo do Telegram](https://t.me/+Uzcmc-NZvN4xNTQx) da comunidade.