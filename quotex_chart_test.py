#!/usr/bin/env python
"""
Simple test application for Quotex chart with live candle formation
"""

import os
import sys
import time
import numpy as np
from datetime import datetime
from PyQt5 import QtCore, QtGui, QtWidgets
import pyqtgraph as pg

class TimeAxisItem(pg.AxisItem):
    """Custom axis item for displaying time values"""

    def __init__(self, *args, **kwargs):
        super(TimeAxisItem, self).__init__(*args, **kwargs)
        self.time_values = {}  # Maps x-values to timestamps

    def set_time_values(self, time_values):
        """Set the time values for the axis"""
        self.time_values = time_values
        self.update()

    def tickStrings(self, values, *_):
        """Return the strings that should be placed next to ticks"""
        result = []
        for v in values:
            # Find the closest time value
            if int(v) in self.time_values:
                # Get the timestamp for this position
                timestamp = self.time_values[int(v)]
                # Format the timestamp as HH:MM
                time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M")
                result.append(time_str)
            else:
                result.append("")
        return result

class CandlestickItem(pg.GraphicsObject):
    """Custom graphics item for displaying a candlestick chart"""

    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data  # data must have fields: time, open, high, low, close, color
        self.picture = QtGui.QPicture()
        self.generatePicture()

    def generatePicture(self):
        """Pre-render the candlestick chart as a QPicture for performance"""
        painter = QtGui.QPainter(self.picture)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        # Width of candlestick body (adjust for better appearance)
        w = 0.35  # slightly narrower for cleaner look

        # Use simple index-based positioning for all candles
        for i, candle in enumerate(self.data):
            # Always use index for positioning to ensure candles are visible
            t = i

            # Ensure we have all required values
            if not all(k in candle for k in ['open', 'high', 'low', 'close']):
                continue

            open_val = candle['open']
            high = candle['high']
            low = candle['low']
            close_val = candle['close']

            # Determine candle color
            if 'color' in candle:
                color = candle['color']
            else:
                color = 'green' if close_val > open_val else 'red' if close_val < open_val else 'gray'

            # Set colors based on candle direction - using more professional colors
            if color == 'green':
                # Professional green for bullish candles
                body_color = QtGui.QColor(0, 180, 100)  # Softer green
                wick_color = QtGui.QColor(0, 200, 120)  # Slightly brighter for wicks
            elif color == 'red':
                # Professional red for bearish candles
                body_color = QtGui.QColor(220, 60, 60)  # Softer red
                wick_color = QtGui.QColor(240, 80, 80)  # Slightly brighter for wicks
            else:
                # Gray for neutral candles
                body_color = QtGui.QColor(150, 150, 150)
                wick_color = QtGui.QColor(170, 170, 170)

            # Set pen for wicks
            wick_pen = pg.mkPen(wick_color, width=1)
            painter.setPen(wick_pen)

            # Draw candle wicks first (so they appear behind the body)
            painter.drawLine(QtCore.QPointF(t, low), QtCore.QPointF(t, high))

            # Set pen and brush for body
            body_pen = pg.mkPen(body_color, width=1)
            painter.setPen(body_pen)
            painter.setBrush(pg.mkBrush(body_color))

            # Draw candle body
            if close_val > open_val:
                # Bullish candle (filled)
                painter.drawRect(QtCore.QRectF(t-w, open_val, w*2, close_val-open_val))
            elif close_val < open_val:
                # Bearish candle (filled)
                painter.drawRect(QtCore.QRectF(t-w, close_val, w*2, open_val-close_val))
            else:
                # Doji candle (open == close)
                painter.drawLine(
                    QtCore.QPointF(t-w, open_val),
                    QtCore.QPointF(t+w, close_val)
                )

            # If this is a live candle, add a special indicator
            if 'source' in candle and candle['source'] == 'live_generated':
                # Draw a small dot at the close price to indicate it's live
                live_pen = pg.mkPen(QtGui.QColor(66, 153, 225), width=2)
                painter.setPen(live_pen)
                painter.setBrush(pg.mkBrush(QtGui.QColor(66, 153, 225)))
                painter.drawEllipse(QtCore.QPointF(t, close_val), 0.1, 0.0001)

        painter.end()

    def paint(self, painter, *_):
        """Paint the pre-rendered picture"""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        """Return the bounding rectangle of the picture"""
        return QtCore.QRectF(self.picture.boundingRect())

    def update_data(self, data):
        """Update the candlestick data and redraw"""
        self.data = data
        self.generatePicture()
        self.update()

class CandlestickChart(pg.PlotWidget):
    """Widget for displaying candlestick charts with time axis"""

    def __init__(self, parent=None, background='#1E222D', title="Candlestick Chart"):
        # Create custom time axis
        time_axis = TimeAxisItem(orientation='bottom')

        # Initialize with custom axis
        super(CandlestickChart, self).__init__(parent=parent, background=background, axisItems={'bottom': time_axis})

        # Store reference to time axis
        self.time_axis = time_axis

        # Set up the plot
        self.setTitle(title, color='#ffffff', size='12pt')

        # Get the plot item
        plot_item = self.getPlotItem()

        # Apply grid with improved appearance
        plot_item.showGrid(x=False, y=True, alpha=0.1)  # Only show horizontal grid lines

        # Set axis pens
        axis_pen = pg.mkPen('#444455')
        plot_item.getAxis('left').setPen(axis_pen)
        plot_item.getAxis('bottom').setPen(axis_pen)

        # Set tick fonts
        plot_item.getAxis('left').setTickFont(QtGui.QFont('Arial', 8))
        plot_item.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 8))

        # Set labels
        self.setLabel('left', '', color='#777788')
        self.setLabel('bottom', '', color='#777788')

        # Set view box background
        self.getPlotItem().getViewBox().setBackgroundColor('#1E222D')

        # Remove border around the plot
        self.getPlotItem().getViewBox().setBorder(None)

        # Initialize empty candlestick item
        self.candle_item = None

        # Add price line (horizontal line at current price)
        self.price_line = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen(color='#ffffff', width=1, style=QtCore.Qt.DashLine)
        )
        self.addItem(self.price_line)
        self.price_line.setVisible(False)  # Hide until we have data

        # Add price label
        self.price_label = pg.TextItem(text="", color='#ffffff', anchor=(0, 0.5), fill='#2A2F3A')
        self.addItem(self.price_label)
        self.price_label.setVisible(False)  # Hide until we have data

        # Add current time line (vertical line at current time)
        self.time_line = pg.InfiniteLine(
            angle=90,
            movable=False,
            pen=pg.mkPen(color='#ffffff', width=1, style=QtCore.Qt.DotLine)
        )
        self.addItem(self.time_line)
        self.time_line.setVisible(False)  # Hide until we have data

        # Flag for live mode
        self.live_mode_active = False

    def set_candle_data(self, candles):
        """Set candlestick data and update the chart"""
        if not candles or len(candles) == 0:
            print("No candles to display")
            return

        # Print debug info
        print(f"Setting candle data with {len(candles)} candles")
        if len(candles) > 0:
            first_candle = candles[0]
            last_candle = candles[-1]
            print(f"First candle: {first_candle.get('timestamp', 'N/A')} - {first_candle.get('open', 0):.5f} -> {first_candle.get('close', 0):.5f}")
            print(f"Last candle: {last_candle.get('timestamp', 'N/A')} - {last_candle.get('open', 0):.5f} -> {last_candle.get('close', 0):.5f}")

        # Validate and process candle data
        validated_candles = []
        for candle in candles:
            # Ensure all required fields are present
            if not all(k in candle for k in ['open', 'high', 'low', 'close']):
                print(f"Warning: Skipping candle missing required OHLC data: {candle}")
                continue

            # Create a copy to avoid modifying the original
            processed_candle = candle.copy()

            # Ensure numeric values for OHLC
            for key in ['open', 'high', 'low', 'close']:
                try:
                    processed_candle[key] = float(processed_candle[key])
                except (ValueError, TypeError):
                    print(f"Warning: Invalid {key} value in candle: {processed_candle[key]}")
                    processed_candle[key] = 0.0

            # Add to validated list
            validated_candles.append(processed_candle)

        if not validated_candles:
            print("No valid candles to display after validation")
            return

        # Use validated candles from now on
        candles = validated_candles

        # Remove existing candle item if it exists
        if self.candle_item is not None:
            self.removeItem(self.candle_item)

        # Create new candle item
        self.candle_item = CandlestickItem(candles)
        self.addItem(self.candle_item)

        # Update time axis with timestamps
        time_values = {}
        for i, candle in enumerate(candles):
            if 'time' in candle:
                time_values[i] = candle['time']
        self.time_axis.set_time_values(time_values)

        # Update price line and label
        if candles:
            last_candle = candles[-1]
            current_price = last_candle['close']
            self.price_line.setPos(current_price)
            self.price_line.setVisible(True)

            self.price_label.setText(f"{current_price:.5f}")
            self.price_label.setPos(len(candles) - 1, current_price)
            self.price_label.setVisible(True)

            # Update time line to show current time
            current_time = datetime.now()
            # Find the closest candle to current time
            closest_candle_idx = len(candles) - 1
            for i, candle in enumerate(candles):
                if 'time' in candle and candle['time'] > current_time.timestamp():
                    closest_candle_idx = max(0, i - 1)
                    break

            self.time_line.setPos(closest_candle_idx)
            self.time_line.setVisible(True)

        # Auto-range to show all data
        self.autoRange()

        # Set X range to show all candles with better spacing
        if len(candles) > 1:
            x_min = -0.5
            x_max = len(candles) - 1

            # Adjust padding based on number of candles
            if len(candles) <= 20:
                x_padding = (x_max - x_min) * 0.2  # More padding for fewer candles
            elif len(candles) <= 50:
                x_padding = (x_max - x_min) * 0.15
            else:
                x_padding = (x_max - x_min) * 0.1  # Less padding for many candles

            self.setXRange(x_min, x_max + x_padding)

        # Set Y range to show prices with some padding
        if candles:
            # Find min and max prices
            min_price = min(candle['low'] for candle in candles)
            max_price = max(candle['high'] for candle in candles)

            # Add padding
            price_range = max_price - min_price

            # Use more padding for smaller price ranges to avoid zooming in too much
            if price_range < 0.001:
                padding = 0.001  # Minimum padding for very small ranges
            else:
                padding = price_range * 0.15  # Slightly more padding (15% instead of 10%)

            self.setYRange(min_price - padding, max_price + padding)

            # Add some visual enhancements

            # Add a horizontal line at the middle price point for reference
            mid_price = (min_price + max_price) / 2
            if not hasattr(self, 'mid_line'):
                self.mid_line = pg.InfiniteLine(
                    angle=0,
                    movable=False,
                    pen=pg.mkPen(color='#555555', width=1, style=QtCore.Qt.DotLine)
                )
                self.addItem(self.mid_line)
            self.mid_line.setPos(mid_price)
            self.mid_line.setVisible(True)

            # Add live mode indicator if needed
            if 'source' in candles[-1] and candles[-1]['source'] == 'live_generated':
                if not hasattr(self, 'live_indicator'):
                    self.live_indicator = pg.TextItem(
                        text="LIVE",
                        color='#4299E1',
                        anchor=(1, 0),
                        fill='#2A2F3A'
                    )
                    self.addItem(self.live_indicator)

                # Position in top-right corner
                self.live_indicator.setPos(len(candles) - 1, max_price + padding * 0.8)
                self.live_indicator.setVisible(True)
            elif hasattr(self, 'live_indicator'):
                self.live_indicator.setVisible(False)

class QuotexChartTest(QtWidgets.QMainWindow):
    """Simple test application for Quotex chart with live candle formation"""

    def __init__(self):
        super(QuotexChartTest, self).__init__()

        # Set up the UI
        self.setWindowTitle("Quotex Chart Test")
        self.setGeometry(100, 100, 1200, 800)

        # Set professional dark theme
        self.setStyleSheet("""
            QMainWindow, QWidget {
                background-color: #1E222D;
                color: #E0E0E0;
            }
            QPushButton {
                background-color: #2D3748;
                color: #E0E0E0;
                border: 1px solid #4A5568;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3A4A64;
                border: 1px solid #5A6A88;
            }
            QPushButton:pressed {
                background-color: #2A3548;
            }
        """)

        # Create central widget and layout
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)

        # Create controls layout
        controls_layout = QtWidgets.QHBoxLayout()

        # Add fetch button
        self.fetch_button = QtWidgets.QPushButton("Generate Data")
        self.fetch_button.clicked.connect(self.generate_data)
        controls_layout.addWidget(self.fetch_button)

        # Add live mode checkbox
        self.live_mode_check = QtWidgets.QCheckBox("Live Mode")
        self.live_mode_check.setChecked(True)
        self.live_mode_check.stateChanged.connect(self.toggle_live_mode)
        self.live_mode_check.setStyleSheet("QCheckBox { color: #4299E1; font-weight: bold; }")
        controls_layout.addWidget(self.live_mode_check)

        # Add status label
        self.status_label = QtWidgets.QLabel("Ready")
        controls_layout.addWidget(self.status_label)

        # Add controls to main layout
        main_layout.addLayout(controls_layout)

        # Create chart
        self.chart = CandlestickChart(title="Quotex Chart")
        main_layout.addWidget(self.chart)

        # Initialize variables
        self.candles_data = []
        self.live_mode_active = False
        self.last_live_price = None

        # Create live candle timer
        self.live_timer = QtCore.QTimer()
        self.live_timer.timeout.connect(self.update_live_candle)
        self.live_timer.setInterval(500)  # Update every 500ms

        # Generate initial data
        self.generate_data()

    def generate_data(self):
        """Generate simulated candle data"""
        # Generate realistic simulated candles
        self.candles_data = self.generate_realistic_candles("EURUSD", count=100)
        
        # Update the chart
        self.chart.set_candle_data(self.candles_data)
        
        # Start live mode if checked
        if self.live_mode_check.isChecked():
            self.toggle_live_mode(QtCore.Qt.Checked)
        
        # Update status
        self.status_label.setText("Data generated")

    def generate_realistic_candles(self, asset_name, count=100):
        """Generate realistic simulated candles for testing"""
        print(f"Generating {count} realistic simulated candles for {asset_name}")
        
        # Set base price for EURUSD
        base_price = 1.12000
        
        # Generate candles
        candles = []
        current_time = int(time.time())
        
        # Start from the oldest candle
        start_time = current_time - (count * 60)  # 1-minute candles
        
        # Create a realistic price trend with some randomness
        # Use a sine wave with some noise to create a realistic price movement
        x = np.linspace(0, 4*np.pi, count)  # 2 full cycles
        trend = np.sin(x) * 0.005 * base_price  # Small amplitude
        
        for i in range(count):
            # Calculate candle time
            candle_time = start_time + (i * 60)
            
            # Generate price movement with trend and noise
            volatility = 0.0002 * base_price  # Base volatility
            noise = np.random.normal(0, volatility)
            
            # Calculate price movement
            if i == 0:
                # First candle
                open_price = base_price
                close_price = base_price + trend[i] + noise
            else:
                # Use previous close as this open
                open_price = candles[i-1]['close']
                close_price = open_price + trend[i] - trend[i-1] + noise
            
            # Add some randomness to high and low
            price_range = abs(close_price - open_price)
            extra_range = max(price_range * 0.5, volatility)
            
            if close_price > open_price:
                high_price = close_price + np.random.uniform(0, extra_range)
                low_price = open_price - np.random.uniform(0, extra_range)
            else:
                high_price = open_price + np.random.uniform(0, extra_range)
                low_price = close_price - np.random.uniform(0, extra_range)
            
            # Create candle
            candle = {
                'timestamp': datetime.fromtimestamp(candle_time).strftime("%Y-%m-%d %H:%M:%S"),
                'time': candle_time,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'color': 'green' if close_price >= open_price else 'red',
                'source': 'simulated'
            }
            
            candles.append(candle)
        
        # Mark the last candle as live generated
        if candles:
            candles[-1]['source'] = 'live_generated'
        
        return candles

    def toggle_live_mode(self, state):
        """Toggle live candle generation mode"""
        self.live_mode_active = (state == QtCore.Qt.Checked)
        
        if self.live_mode_active:
            print("Starting live candle generation mode")
            
            # Get the last candle's close price as starting point
            if self.candles_data:
                self.last_live_price = self.candles_data[-1]['close']
                
                # Mark the last candle as live generated
                self.candles_data[-1]['source'] = 'live_generated'
                
                # Update the chart
                self.chart.live_mode_active = True
                self.chart.set_candle_data(self.candles_data)
                
                # Start the timer
                self.live_timer.start()
                
                # Update status
                self.status_label.setText("● LIVE MODE: EURUSD")
                self.status_label.setStyleSheet("color: #4299E1; font-weight: bold;")
            else:
                print("Cannot start live mode without initial data")
                return
        else:
            print("Stopping live candle generation mode")
            
            # Stop live mode in the chart
            self.chart.live_mode_active = False
            
            # Stop the timer
            if self.live_timer.isActive():
                self.live_timer.stop()
            
            # Reset UI
            self.status_label.setText("Ready")
            self.status_label.setStyleSheet("color: white;")

    def update_live_candle(self):
        """Update the live candle in real-time"""
        if not self.candles_data or not self.live_mode_active:
            return
        
        # Get the last candle
        last_candle = self.candles_data[-1]
        
        # Generate a new price with realistic movement
        volatility = 0.0001  # Smaller volatility for more realistic 1-second movements
        trend = 0.00001 * (1 if np.random.random() > 0.5 else -1)  # Very small trend
        
        # Calculate price movement
        price_change = np.random.normal(trend, volatility) * self.last_live_price
        new_price = self.last_live_price + price_change
        
        # Store the new price for next update
        self.last_live_price = new_price
        
        # Update the last candle with the new price
        if new_price > last_candle['high']:
            last_candle['high'] = new_price
        if new_price < last_candle['low']:
            last_candle['low'] = new_price
        
        # Update close price
        last_candle['close'] = new_price
        
        # Update color
        last_candle['color'] = 'green' if last_candle['close'] > last_candle['open'] else 'red'
        
        # Mark as live generated
        last_candle['source'] = 'live_generated'
        
        # Update the chart
        self.chart.set_candle_data(self.candles_data)
        
        # Every 60 seconds, create a new candle
        current_time = time.time()
        last_candle_time = last_candle.get('time', 0)
        
        if current_time - last_candle_time >= 60:
            self.create_new_candle()
    
    def create_new_candle(self):
        """Create a new candle for the next time period"""
        if not self.candles_data:
            return
        
        # Get the last candle
        last_candle = self.candles_data[-1]
        
        # Create a new candle starting from the last close price
        current_time = time.time()
        
        # Round time to the nearest minute for better display
        rounded_time = int(current_time - (current_time % 60))
        
        # Create new candle with the last close price as the open
        new_candle = {
            'time': rounded_time,
            'timestamp': datetime.fromtimestamp(rounded_time).strftime("%Y-%m-%d %H:%M:%S"),
            'open': last_candle['close'],
            'high': last_candle['close'],
            'low': last_candle['close'],
            'close': last_candle['close'],
            'color': 'gray',
            'source': 'live_generated'
        }
        
        # Add the new candle to the data
        self.candles_data.append(new_candle)
        
        # If we have too many candles, remove the oldest one
        if len(self.candles_data) > 100:
            self.candles_data.pop(0)
        
        # Update the chart
        self.chart.set_candle_data(self.candles_data)
        
        # Log the new candle creation
        print(f"Created new candle at {new_candle['timestamp']} - Open: {new_candle['open']:.5f}")

def main():
    """Main function to start the application"""
    # Create application
    app = QtWidgets.QApplication(sys.argv)
    
    # Create and show UI
    ui = QuotexChartTest()
    ui.show()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
