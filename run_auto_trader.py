#!/usr/bin/env python
"""
Run Auto Trader with predefined inputs

This script runs the auto_trader.py script with predefined inputs to avoid
having to manually enter them each time. It also ensures that the models
are properly trained with historical data before trading.
"""

import os
import sys
import subprocess
import glob
import time
import platform

# Configuration
MODEL_DIR = 'models'
CACHE_DIR = 'data_cache'
FORCE_RETRAIN = False  # Set to True to force model retraining

def find_latest_model(model_dir=MODEL_DIR, prefix='xgboost_model_', extension='.pkl'):
    """Find the latest model file in the specified directory"""
    # Create directory if it doesn't exist
    os.makedirs(model_dir, exist_ok=True)

    pattern = os.path.join(model_dir, f"{prefix}*{extension}")
    model_files = glob.glob(pattern)

    if not model_files:
        # Try with any .pkl file
        pattern = os.path.join(model_dir, f"*{extension}")
        model_files = glob.glob(pattern)

        if not model_files:
            print(f"No model files found matching pattern: {pattern}")
            return None

    # Sort by modification time (newest first)
    latest_model = max(model_files, key=os.path.getmtime)
    return latest_model

def find_latest_features(model_dir=MODEL_DIR, prefix='xgboost_selected_features_', extension='.txt'):
    """Find the latest features file in the specified directory"""
    # Create directory if it doesn't exist
    os.makedirs(model_dir, exist_ok=True)

    pattern = os.path.join(model_dir, f"{prefix}*{extension}")
    feature_files = glob.glob(pattern)

    if not feature_files:
        # Try with any features file
        pattern = os.path.join(model_dir, f"*features*{extension}")
        feature_files = glob.glob(pattern)

        if not feature_files:
            print(f"No feature files found matching pattern: {pattern}")
            return None

    # Sort by modification time (newest first)
    latest_features = max(feature_files, key=os.path.getmtime)
    return latest_features

def check_cache_exists(cache_dir=CACHE_DIR, asset='usdars_otc', timeframe=60):
    """Check if cache files exist for the specified asset and timeframe"""
    # Create directory if it doesn't exist
    os.makedirs(cache_dir, exist_ok=True)

    # Check for history cache
    history_cache = os.path.join(cache_dir, f"{asset}_{timeframe}_history.pkl")

    # Check for engineered data cache
    engineered_cache = os.path.join(cache_dir, f"{asset}_{timeframe}_engineered.pkl")

    return os.path.exists(history_cache), os.path.exists(engineered_cache)

def run_auto_trader_with_inputs():
    """Run auto_trader.py with predefined inputs"""
    print("Starting Auto Trader with predefined inputs...")

    # Use subprocess with input piping
    inputs = "demo\n4\n10\n60\n5\n0.65\ny\ny\n\n\n\nn\n"

    try:
        # Run in a separate process to avoid event loop conflicts
        # Use creationflags to create a new process group (Windows)
        # This prevents event loop conflicts between parent and child processes
        if platform.system() == 'Windows':
            process = subprocess.Popen(
                ["python", "auto_trader.py"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
        else:
            # For non-Windows platforms
            process = subprocess.Popen(
                ["python", "auto_trader.py"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                preexec_fn=os.setpgrp  # Create a new process group on Unix
            )

        # Send inputs and get output
        stdout, stderr = process.communicate(inputs)

        # Print output
        print(stdout)
        if stderr:
            print("Errors:", stderr)

    except Exception as e:
        print(f"Error running auto_trader.py: {e}")
        import traceback
        traceback.print_exc()

    print("Auto Trader process completed")

def main():
    """Main function to run auto_trader.py with the trained models"""
    # Check if models exist
    model_file = find_latest_model()
    features_file = find_latest_features()

    # Check if cache exists
    history_cache_exists, engineered_cache_exists = check_cache_exists()

    # Print status
    print("Auto Trader Setup Status:")
    print(f"Model file: {'Found' if model_file else 'Not found'} - {model_file if model_file else ''}")
    print(f"Features file: {'Found' if features_file else 'Not found'} - {features_file if features_file else ''}")
    print(f"History cache: {'Found' if history_cache_exists else 'Not found'}")
    print(f"Engineered data cache: {'Found' if engineered_cache_exists else 'Not found'}")

    # Force retraining if needed
    if FORCE_RETRAIN:
        print("\nForce retraining is enabled. Models will be retrained regardless of existing files.")

    # Run auto_trader.py with predefined inputs
    print("\nStarting auto_trader.py...")
    run_auto_trader_with_inputs()

if __name__ == "__main__":
    main()
