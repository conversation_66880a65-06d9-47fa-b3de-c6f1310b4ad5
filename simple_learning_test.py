#!/usr/bin/env python
"""
Simple test to verify learning chart update functionality
"""

import sys
import numpy as np
from PyQt5 import QtWidgets, Qt<PERSON><PERSON>

def test_learning_chart():
    """Test learning chart functionality"""
    print("🔍 Testing Learning Chart...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import chart widget
        from chart_widgets import LearningProgressChart
        
        # Create chart
        chart = LearningProgressChart()
        print("✅ LearningProgressChart created successfully")
        
        # Test data update
        iterations = list(range(1, 21))  # 20 iterations
        accuracy_data = [0.5 + i * 0.02 for i in range(20)]  # Improving accuracy
        loss_data = [1.0 - acc for acc in accuracy_data]  # Corresponding loss
        
        # Update chart
        chart.set_learning_data(iterations, accuracy_data, loss_data)
        print(f"✅ Chart updated with {len(iterations)} data points")
        print(f"   Accuracy: {min(accuracy_data):.3f} to {max(accuracy_data):.3f}")
        print(f"   Loss: {min(loss_data):.3f} to {max(loss_data):.3f}")
        
        # Show chart briefly
        chart.show()
        
        # Process events briefly
        QtCore.QTimer.singleShot(1000, app.quit)  # Quit after 1 second
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_learning_chart()
    if success:
        print("🎉 Learning chart test PASSED!")
    else:
        print("❌ Learning chart test FAILED!")
