#!/usr/bin/env python
import os
import sys
import time
import csv
import asyncio
import argparse
from datetime import datetime, timedelta
from quotexapi.stable_api import Quotex
from quotexapi.utils.processor import process_candles, get_color
from quotexapi.expiration import get_timestamp_days_ago, timestamp_to_date

# Parse command line arguments
parser = argparse.ArgumentParser(description='Capture live candle data from Quotex API')
parser.add_argument('--asset', type=str, default="BRLUSD_otc", help='Asset to capture (default: BRLUSD_otc)')
parser.add_argument('--period', type=int, default=60, help='Candle period in seconds (default: 60)')
parser.add_argument('--duration', type=int, default=3600, help='Duration to capture in seconds (default: 3600 - 1 hour)')
parser.add_argument('--output', type=str, help='Output CSV file (default: asset_timestamp_candles.csv)')
parser.add_argument('--continuous', action='store_true', help='Run continuously until stopped')
parser.add_argument('--historical', action='store_true', help='Retrieve historical data before starting live capture')
parser.add_argument('--days', type=int, default=7, help='Number of days of historical data to retrieve (default: 7)')
args = parser.parse_args()

# Set CSV filename
if not args.output:
    args.output = f"{args.asset.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_candles.csv"

# Load credentials from config.ini
client = Quotex(
    lang="en",  # Use English language
)

def save_candles_to_csv(candles, filename):
    """Save candles data to CSV file"""
    # Check if file exists to determine if we need to write headers
    file_exists = os.path.isfile(filename)

    # Define fieldnames for CSV
    fieldnames = ['timestamp', 'time', 'open', 'high', 'low', 'close', 'color', 'ticks']

    # Open file in append mode
    with open(filename, 'a', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Write header if file doesn't exist
        if not file_exists:
            writer.writeheader()

        # Write candle data
        for candle in candles:
            # Add color and timestamp if not present
            if 'color' not in candle:
                candle['color'] = get_color(candle)
            if 'timestamp' not in candle:
                candle['timestamp'] = datetime.fromtimestamp(candle['time']).strftime('%Y-%m-%d %H:%M:%S')

            # Write row
            writer.writerow(candle)

    return len(candles)

async def select_account():
    """Select between demo and real accounts"""
    account_type = input("Select account type (demo or real): ").lower()

    if account_type == "real":
        print("Real account selected")
        client.set_account_mode("REAL")
    else:
        print("Demo account selected")
        client.set_account_mode("PRACTICE")  # Default is PRACTICE/DEMO

    return account_type

async def get_historical_candles(asset, period, days, output_file):
    """Retrieve historical candle data for the specified number of days"""
    print(f"\nRetrieving {days} days of historical candle data for {asset}...")

    # Calculate timestamps
    timestamp = get_timestamp_days_ago(days)
    end_time = time.time()

    # Calculate how many hours we need to fetch
    hours_to_fetch = days * 24
    candles_per_request = 60  # Typical number of candles per request

    # Store all candles
    all_candles = []

    # Start time for the first request
    start_from_time = timestamp

    print(f"Fetching data from {timestamp_to_date(timestamp)} to {timestamp_to_date(end_time)}")
    print(f"This will require approximately {hours_to_fetch} requests. Please be patient...")
    print("-" * 60)

    # Progress tracking
    total_requests = hours_to_fetch
    completed_requests = 0

    try:
        # Loop through each hour
        for i in range(hours_to_fetch):
            # Calculate offset to get one hour of data
            offset = 3600  # 1 hour in seconds

            # Get candles for this hour
            candles = await client.get_candles(asset, start_from_time, offset, period)

            if candles and len(candles) > 0:
                # Process candles if needed
                if not candles[0].get("open"):
                    candles = process_candles(candles, period)

                # Add color to candles
                for candle in candles:
                    candle['color'] = get_color(candle)

                # Add to our collection
                all_candles.extend(candles)

                # Save batch to CSV
                save_candles_to_csv(candles, output_file)

            # Move to next hour
            start_from_time += offset

            # Update progress
            completed_requests += 1
            progress = (completed_requests / total_requests) * 100
            print(f"\rProgress: {progress:.1f}% ({completed_requests}/{total_requests} hours) - {len(all_candles)} candles retrieved", end="")

            # Small delay to avoid overwhelming the API
            await asyncio.sleep(0.5)

    except Exception as e:
        print(f"\nError retrieving historical data: {e}")

    print(f"\n\nHistorical data retrieval complete. {len(all_candles)} candles retrieved.")
    print(f"Data saved to: {output_file}")

    return all_candles

async def capture_live_candles():
    """Capture live candle data and save to CSV"""
    print(f"\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully.")

        # Get asset information
        asset_name, asset_data = await client.get_available_asset(args.asset, force_open=True)
        print(f"Asset: {asset_name} ({asset_data})")

        if not asset_data[2]:  # Check if asset is open
            print("WARNING: Asset appears to be closed. Data may not be available.")

        # Retrieve historical data if requested
        candles_history = []
        if args.historical:
            print(f"\nRetrieving {args.days} days of historical data before starting live capture...")
            historical_candles = await get_historical_candles(args.asset, args.period, args.days, args.output)
            candles_history.extend(historical_candles)
            print(f"Historical data retrieval complete. {len(historical_candles)} candles retrieved.")

        print(f"\nStarting to capture live candles for {asset_name}...")
        print(f"Period: {args.period} seconds")
        if args.continuous:
            print("Running continuously until stopped (Ctrl+C to stop)")
        else:
            print(f"Duration: {args.duration} seconds")
        print(f"Saving to: {args.output}")
        print("-" * 60)

        # If we didn't load historical data, initialize an empty list
        if not args.historical:
            candles_history = []

        # Start time for monitoring duration
        start_time = time.time()
        last_candle_time = 0
        last_minute = -1  # Track the last minute we captured

        # Start monitoring
        try:
            # Run until duration expires or indefinitely if continuous mode is enabled
            while args.continuous or (time.time() - start_time) < args.duration:
                # Get current time
                current_time = time.time()
                current_datetime = datetime.now()
                current_minute = current_datetime.minute

                # Calculate time remaining if not in continuous mode
                if not args.continuous:
                    time_remaining = args.duration - (current_time - start_time)

                # Check if we're in a new minute
                if current_minute != last_minute:
                    # Get the latest candles
                    candles = await client.get_candles(args.asset, current_time, 300, args.period)

                    if len(candles) > 0:
                        # Process candles if needed
                        if not candles[0].get("open"):
                            candles = process_candles(candles, args.period)

                        # Add to history if it's a new candle
                        if not candles_history or candles[-1]['time'] != last_candle_time:
                            # Add color to candle
                            candles[-1]['color'] = get_color(candles[-1])

                            # Add to history
                            candles_history.append(candles[-1])
                            last_candle_time = candles[-1]['time']
                            last_minute = current_minute  # Update last minute captured

                            # Save to CSV
                            saved_count = save_candles_to_csv([candles[-1]], args.output)

                            # Print information with clear minute marker
                            candle_time = datetime.fromtimestamp(candles[-1]['time'])
                            print(f"\n[{current_datetime.strftime('%H:%M:%S')}] New 1-minute candle captured")
                            print(f"Candle time: {candle_time}")
                            print(f"Open: {candles[-1]['open']}, Close: {candles[-1]['close']}, High: {candles[-1]['high']}, Low: {candles[-1]['low']}")
                            print(f"Color: {candles[-1]['color']}")
                            print(f"Saved to {args.output} (Total candles: {len(candles_history)})")

                            if not args.continuous:
                                print(f"Time remaining: {time_remaining:.0f} seconds")
                            else:
                                print("Running continuously (Ctrl+C to stop)")

                # Wait before checking again (1 second)
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            print("\nCapture interrupted by user.")

        # Final statistics
        print("\n" + "=" * 60)
        print("CAPTURE SESSION SUMMARY")
        print("=" * 60)
        print(f"Total candles captured: {len(candles_history)}")
        print(f"Data saved to: {args.output}")
        print(f"Capture duration: {time.time() - start_time:.2f} seconds")

        # Print the first and last candle timestamps to show the range
        if len(candles_history) > 0:
            first_candle = datetime.fromtimestamp(candles_history[0]['time'])
            last_candle = datetime.fromtimestamp(candles_history[-1]['time'])
            print(f"First candle: {first_candle}")
            print(f"Last candle: {last_candle}")
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def main():
    try:
        # Print banner
        print("=" * 60)
        print("QUOTEX LIVE CANDLE CAPTURE (1-MINUTE INTERVALS)")
        print("=" * 60)
        if args.historical:
            print(f"This script will retrieve {args.days} days of historical data and then capture new candles every minute")
        else:
            print("This script will capture a new candle every minute")
        print("=" * 60)

        # Select account type
        account_type = await select_account()

        # Start capturing candles
        await capture_live_candles()
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
