#!/usr/bin/env python
"""
Run script for the trading application
This script first verifies the API and handles login through the terminal,
then launches the trading UI if login is successful
"""

import os
import sys
import time
from datetime import datetime

def main():
    """Main function"""
    print("=== Quotex Trading Application ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Check for command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == '--reset-config':
            # Reset config file
            try:
                import reset_config
                if reset_config.reset_config_file():
                    print("Config file reset successfully. Please run the application again.")
                else:
                    print("Failed to reset config file. Exiting.")
                sys.exit(0)
            except ImportError:
                print("Error: reset_config.py not found.")
                sys.exit(1)
            except Exception as e:
                print(f"Error resetting config file: {e}")
                sys.exit(1)

    # First fix config file if needed
    try:
        import fix_config
        fix_config.fix_config_file()
    except ImportError:
        print("Warning: fix_config.py not found. Skipping config file fix.")
    except Exception as e:
        print(f"Warning: Error fixing config file: {e}")
        print("If you're having issues with the config file, try running with --reset-config")

    # Verify API and login through terminal
    try:
        # Import terminal login function
        from terminal_login import terminal_login

        print("Verifying API and logging in through terminal...")
        login_success = terminal_login()

        if not login_success:
            print("\nAPI verification and login failed.")
            print("\nTroubleshooting tips:")
            print("1. If you're having issues with credentials, try running with --reset-config")
            print("2. If you're not receiving the PIN email, try running request_pin.py to request a new PIN")
            print("3. Make sure you have the latest version of the quotexapi package")
            print("4. Check your internet connection")
            print("5. If you're using a VPN, try disabling it")
            print("\nCommands:")
            print("- Reset config: python run_trading_app.py --reset-config")
            print("- Request PIN: python request_pin.py")
            sys.exit(1)

        print("API verification and login successful. Starting trading UI...")

        # Import and run the trading UI
        import trading_ui
        trading_ui.main()

    except ImportError as e:
        print(f"Error importing required modules: {e}")
        print("Please make sure all required modules are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
