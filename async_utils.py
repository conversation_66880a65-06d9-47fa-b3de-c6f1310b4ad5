#!/usr/bin/env python
"""
Async Utilities for PyQt Applications

This module provides utilities for integrating asyncio with PyQt applications,
allowing async operations to be performed without blocking the UI or creating
multiple event loops.
"""

import asyncio
import sys
import functools
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QEventLoop

class AsyncHelper(QObject):
    """
    Helper class to run asyncio tasks from PyQt

    This class provides methods to run asyncio coroutines from a PyQt application
    without creating multiple event loops or blocking the UI.

    This class is implemented as a singleton to ensure only one instance exists.
    """

    # Class variables for singleton pattern
    _instance = None
    _loop = None

    # Signal emitted when a task is complete
    task_complete = pyqtSignal(object)

    # Signal emitted when a task raises an exception
    task_error = pyqtSignal(Exception)

    def __new__(cls):
        """Create a singleton instance"""
        if cls._instance is None:
            print("Creating new AsyncHelper instance")
            cls._instance = super(AsyncHelper, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the singleton instance (only once)"""
        # Always call super().__init__() first
        super().__init__()

        # Check if already initialized
        if hasattr(self, '_initialized') and self._initialized:
            return

        print("Initializing AsyncHelper")
        self._initialized = True

        # Store the main event loop
        if AsyncHelper._loop is None:
            try:
                AsyncHelper._loop = asyncio.get_event_loop()
                print("Using existing asyncio event loop")
            except RuntimeError:
                AsyncHelper._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(AsyncHelper._loop)
                print("Created new asyncio event loop")

        self._loop = AsyncHelper._loop
        print(f"AsyncHelper using event loop: {id(self._loop)}")

        # Create a timer to process asyncio events
        self._timer = QTimer(self)
        self._timer.timeout.connect(self._process_asyncio_events)

        # Start the timer with a longer interval to reduce CPU usage
        self._timer.start(100)  # 100ms interval to prevent UI freezing

    def _process_asyncio_events(self):
        """Process asyncio events in the event loop - non-blocking version"""
        # Process all events that are ready without blocking
        try:
            # Only process events if the loop is not already running
            if not self._loop.is_running():
                # Process only ready tasks without blocking
                # Use a very short timeout to avoid blocking the UI
                try:
                    # Run for a very short time to process ready tasks
                    self._loop.call_soon(self._loop.stop)
                    # Use run_until_complete with a timeout instead of run_forever
                    import asyncio
                    try:
                        # Create a short timeout task
                        timeout_task = asyncio.sleep(0.001)  # 1ms timeout
                        self._loop.run_until_complete(timeout_task)
                    except:
                        # If that fails, just stop the loop
                        if self._loop.is_running():
                            self._loop.stop()
                except:
                    # If anything fails, just skip this cycle
                    pass
        except RuntimeError as e:
            # Don't print every runtime error to avoid spam
            if "loop is already running" not in str(e).lower():
                print(f"RuntimeError in _process_asyncio_events: {e}")
            # If we get a runtime error, the loop might be closed or in an invalid state
            # Create a new loop and use it
            try:
                if self._loop.is_closed():
                    self._loop = asyncio.new_event_loop()
                    AsyncHelper._loop = self._loop
                    asyncio.set_event_loop(self._loop)
                    print(f"Created new event loop: {id(self._loop)}")
            except Exception as e2:
                print(f"Error creating new event loop: {e2}")
        except Exception as e:
            print(f"Error processing asyncio events: {e}")

    @classmethod
    def get_event_loop(cls):
        """Get the global event loop"""
        if cls._loop is None:
            try:
                cls._loop = asyncio.get_event_loop()
                print(f"Got existing event loop: {id(cls._loop)}")
            except RuntimeError:
                cls._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(cls._loop)
                print(f"Created new event loop: {id(cls._loop)}")
        return cls._loop

    def run_coroutine(self, coro, callback=None, error_callback=None):
        """
        Run a coroutine in the asyncio event loop

        Args:
            coro: The coroutine to run
            callback: Optional callback function to call with the result
            error_callback: Optional callback function to call if an exception occurs

        Returns:
            The task object
        """
        # Create a callback that will be called when the task is complete
        def _task_done(task):
            try:
                result = task.result()
                if callback:
                    callback(result)
                self.task_complete.emit(result)
            except Exception as e:
                if error_callback:
                    error_callback(e)
                self.task_error.emit(e)

        # Create a task and add the callback
        task = asyncio.ensure_future(coro, loop=self._loop)
        task.add_done_callback(_task_done)

        return task

    def run_coroutine_sync(self, coro):
        """
        Run a coroutine synchronously (blocking)

        This should only be used during initialization or when blocking is acceptable.
        For normal operations, use run_coroutine instead.

        Args:
            coro: The coroutine to run

        Returns:
            The result of the coroutine
        """
        # Create a QEventLoop to wait for the result
        loop = QEventLoop()
        result = None
        error = None

        # Define callbacks
        def on_complete(res):
            nonlocal result
            result = res
            if loop.isRunning():
                loop.quit()

        def on_error(err):
            nonlocal error
            error = err
            if loop.isRunning():
                loop.quit()

        # Run the coroutine
        self.run_coroutine(coro, on_complete, on_error)

        # Wait for the result, but only if we don't already have a result
        if result is None and error is None:
            try:
                loop.exec_()
            except RuntimeError as e:
                print(f"Warning: QEventLoop error: {e}")

        # Raise the error if one occurred
        if error:
            raise error

        return result

# Global instance for convenience
async_helper = AsyncHelper()

def run_async(callback=None, error_callback=None):
    """
    Decorator to run a coroutine function using the AsyncHelper

    Args:
        callback: Optional callback function to call with the result
        error_callback: Optional callback function to call if an exception occurs

    Returns:
        A decorator function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            coro = func(*args, **kwargs)
            return async_helper.run_coroutine(coro, callback, error_callback)
        return wrapper
    return decorator
