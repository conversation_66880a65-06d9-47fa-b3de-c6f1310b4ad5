#!/usr/bin/env python
"""
Make predictions using the XGBoost models trained on real trading data.
This script loads the trained models and makes predictions on new data.
"""

import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
import matplotlib.pyplot as plt

# Import custom modules
from Models.Feature_Engineering import engineer_features
from Models.XGBoost_Model import predict_with_xgboost

def load_models(model_dir='models'):
    """
    Load the trained XGBoost models
    
    Parameters:
    - model_dir: Directory containing model files
    
    Returns:
    - models: Dictionary of loaded models
    """
    # Find the latest model files
    model_files = {}
    feature_files = {}
    
    # List all files in the model directory
    for filename in os.listdir(model_dir):
        if filename.startswith('xgboost_model_20250506'):
            # Extract horizon from filename
            if '1min' in filename:
                horizon = 1
            elif '3min' in filename:
                horizon = 3
            elif '5min' in filename:
                horizon = 5
            else:
                # Try to determine horizon from the timestamp
                timestamp = filename.split('_')[-1].split('.')[0]
                
                # Check if there's a corresponding feature file with horizon info
                for feature_file in os.listdir(model_dir):
                    if feature_file.startswith(f'xgboost_selected_features_{timestamp}'):
                        if '1min' in feature_file:
                            horizon = 1
                        elif '3min' in feature_file:
                            horizon = 3
                        elif '5min' in feature_file:
                            horizon = 5
                        else:
                            # Default to 1 minute horizon
                            horizon = 1
                        break
                else:
                    # If no feature file found, use the timestamp to determine order
                    if timestamp == '20250506-165646':
                        horizon = 1
                    elif timestamp == '20250506-165917':
                        horizon = 3
                    elif timestamp == '20250506-170149':
                        horizon = 5
                    else:
                        # Default to 1 minute horizon
                        horizon = 1
            
            model_files[horizon] = os.path.join(model_dir, filename)
        
        elif filename.startswith('xgboost_selected_features_20250506'):
            # Extract horizon from filename
            if '1min' in filename:
                horizon = 1
            elif '3min' in filename:
                horizon = 3
            elif '5min' in filename:
                horizon = 5
            else:
                # Try to determine horizon from the timestamp
                timestamp = filename.split('_')[-1].split('.')[0]
                
                # Check if there's a corresponding model file with horizon info
                for model_file in os.listdir(model_dir):
                    if model_file.startswith(f'xgboost_model_{timestamp}'):
                        if '1min' in model_file:
                            horizon = 1
                        elif '3min' in model_file:
                            horizon = 3
                        elif '5min' in model_file:
                            horizon = 5
                        else:
                            # Default to 1 minute horizon
                            horizon = 1
                        break
                else:
                    # If no model file found, use the timestamp to determine order
                    if timestamp == '20250506-165646':
                        horizon = 1
                    elif timestamp == '20250506-165917':
                        horizon = 3
                    elif timestamp == '20250506-170149':
                        horizon = 5
                    else:
                        # Default to 1 minute horizon
                        horizon = 1
            
            feature_files[horizon] = os.path.join(model_dir, filename)
    
    # Load models and features
    models = {}
    for horizon in [1, 3, 5]:
        if horizon in model_files and horizon in feature_files:
            print(f"Loading model for {horizon} minute horizon...")
            model = joblib.load(model_files[horizon])
            
            # Load selected features
            with open(feature_files[horizon], 'r') as f:
                selected_features = [line.strip() for line in f.readlines()]
            
            models[horizon] = {
                'model': model,
                'features': selected_features
            }
    
    return models

def make_predictions(models, data):
    """
    Make predictions using the loaded models
    
    Parameters:
    - models: Dictionary of loaded models
    - data: DataFrame with features
    
    Returns:
    - predictions: DataFrame with predictions
    """
    # Apply feature engineering
    featured_data = engineer_features(data)
    
    # Make predictions for each horizon
    predictions = pd.DataFrame()
    predictions['timestamp'] = featured_data['timestamp'] if 'timestamp' in featured_data.columns else featured_data.index
    
    for horizon, model_info in models.items():
        print(f"Making predictions for {horizon} minute horizon...")
        model = model_info['model']
        features = model_info['features']
        
        # Make prediction
        prediction_result = predict_with_xgboost(model, featured_data, features)
        
        # Add prediction to results
        predictions[f'prediction_{horizon}min'] = prediction_result['prediction']
        predictions[f'probability_{horizon}min'] = prediction_result['probability']
        predictions[f'confidence_{horizon}min'] = prediction_result['confidence']
    
    return predictions

def main():
    """Main function to make predictions using trained models"""
    # Configuration
    model_dir = 'models'
    test_data_path = 'usdars_otc_20250506_105606_candles.csv'  # Using the same file for demonstration
    
    # Load models
    models = load_models(model_dir)
    
    # Load test data
    print(f"Loading test data from {test_data_path}")
    test_data = pd.read_csv(test_data_path)
    
    # Use a subset of data for testing (last 100 candles)
    test_subset = test_data.tail(100).copy()
    
    # Make predictions
    predictions = make_predictions(models, test_subset)
    
    # Print predictions
    print("\nPredictions:")
    print(predictions.head())
    
    # Save predictions
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    predictions_path = f"predictions_{timestamp}.csv"
    predictions.to_csv(predictions_path, index=False)
    print(f"Predictions saved to {predictions_path}")

if __name__ == "__main__":
    main()
