from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import optuna
import os
import time
import numpy as np
import pandas as pd
from datetime import datetime
import json
from Models.XGBoost_Model import train_xgboost_model
from Models.Ensemble_Model_Integration import create_ensemble

# Check if TensorFlow is available
try:
    import tensorflow as tf
    from tensorflow.keras.models import Model
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Input
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow is available in Self-Learning module.")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow is not available in Self-Learning module. Some features will be limited.")

class ModelTrainer:
    def __init__(self, data_collector, feature_engineer,
                 adaptation_rate=0.1, confidence_threshold=0.65, memory_factor=0.8,
                 retraining_threshold=0.55, optimization_interval=10):
        """
        Initialize the model trainer with self-learning capabilities

        Parameters:
        - data_collector: Data collector object for fetching market data
        - feature_engineer: Function for engineering features
        - adaptation_rate: Rate at which weights adapt to new performance (0-1)
        - confidence_threshold: Threshold for high-confidence predictions
        - memory_factor: Factor for weighting recent vs historical performance (0-1)
        - retraining_threshold: Accuracy threshold below which models are retrained
        - optimization_interval: Number of iterations between hyperparameter optimizations
        """
        self.data_collector = data_collector
        self.feature_engineer = feature_engineer
        self.models = {}
        self.ensemble = None
        self.performance_history = []

        # Self-learning parameters
        self.adaptation_rate = adaptation_rate
        self.confidence_threshold = confidence_threshold
        self.memory_factor = memory_factor
        self.retraining_threshold = retraining_threshold
        self.optimization_interval = optimization_interval

        # Tracking variables
        self.prediction_feedback = []
        self.market_conditions = []
        self.model_versions = {'lstm': 1, 'xgboost': 1, 'dqn': 1, 'transformer': 1, 'cnn_lstm': 1}

    def initial_training(self, data):
        """
        Perform initial training of all models with enhanced ensemble integration
        """
        print("Starting initial training of models...")

        # Feature engineering
        print("Performing feature engineering...")
        featured_data = self.feature_engineer(data)

        # Define features for models
        feature_columns = [col for col in featured_data.columns
                          if col not in ['target_1min', 'target_5min', 'timestamp']]

        print(f"Using {len(feature_columns)} features for model training")

        # Initialize model parameters
        lstm_model = None
        dqn_agent = None
        transformer_model = None
        cnn_lstm_model = None

        # Train LSTM-GRU model if TensorFlow is available
        if TENSORFLOW_AVAILABLE:
            try:
                from Models.LSTM_GRU_Deep_Learning_Model import train_lstm_gru_model, prepare_time_series

                print("Training LSTM-GRU model...")
                X_train, X_val, X_test, y_train, y_val, y_test, scaler, feature_importance = prepare_time_series(
                    featured_data, feature_columns, 'target_1min'
                )
                lstm_model, history, _ = train_lstm_gru_model(featured_data, feature_columns, model_type='advanced')

                # Store model and metadata
                self.models['lstm'] = {
                    'model': lstm_model,
                    'scaler': scaler,
                    'feature_columns': feature_columns,
                    'feature_importance': feature_importance,
                    'training_history': history.history if history else None,
                    'version': 1,
                    'last_trained': datetime.now().isoformat()
                }
            except Exception as e:
                print(f"Error training LSTM-GRU model: {e}")
                print("Continuing without LSTM-GRU model")

            # Train CNN-LSTM with Attention model
            try:
                from Models.LSTM_GRU_Deep_Learning_Model import train_lstm_gru_model, prepare_time_series

                print("Training CNN-LSTM with Attention model...")
                cnn_lstm_model, history, _ = train_lstm_gru_model(
                    featured_data,
                    feature_columns,
                    model_type='cnn_lstm_attention',
                    seq_length=30,
                    batch_size=32,
                    epochs=100
                )

                # Store model and metadata
                self.models['cnn_lstm'] = {
                    'model': cnn_lstm_model,
                    'scaler': scaler,  # Reuse scaler from LSTM model
                    'feature_columns': feature_columns,
                    'feature_importance': feature_importance,  # Reuse feature importance from LSTM model
                    'training_history': history.history if history else None,
                    'version': 1,
                    'last_trained': datetime.now().isoformat()
                }
            except Exception as e:
                print(f"Error training CNN-LSTM with Attention model: {e}")
                print("Continuing without CNN-LSTM with Attention model")

            # Train Transformer model
            try:
                from Models.LSTM_GRU_Deep_Learning_Model import train_lstm_gru_model

                print("Training Transformer model...")
                transformer_model, transformer_scaler, transformer_history, transformer_feature_importance, transformer_evaluation = train_lstm_gru_model(
                    featured_data,
                    feature_columns,
                    model_type='transformer',
                    seq_length=30,
                    batch_size=32,
                    epochs=100
                )

                # Store model and metadata
                self.models['transformer'] = {
                    'model': transformer_model,
                    'scaler': transformer_scaler,
                    'feature_columns': feature_columns,
                    'feature_importance': transformer_feature_importance,
                    'training_history': transformer_history.history if transformer_history else None,
                    'evaluation': transformer_evaluation,
                    'version': 1,
                    'last_trained': datetime.now().isoformat()
                }
            except Exception as e:
                print(f"Error training Transformer model: {e}")
                print("Continuing without Transformer model")

        # Train XGBoost model
        print("Training XGBoost model...")
        xgb_model, xgb_feature_importance, xgb_evaluation, xgb_selected_features = train_xgboost_model(
            featured_data,
            feature_columns,
            optimize_hyperparams=True,
            feature_selection=True
        )

        # Store model and metadata
        self.models['xgboost'] = {
            'model': xgb_model,
            'feature_columns': xgb_selected_features,
            'feature_importance': xgb_feature_importance,
            'evaluation': xgb_evaluation,
            'version': 1,
            'last_trained': datetime.now().isoformat()
        }

        # Train DQN agent if TensorFlow is available
        if TENSORFLOW_AVAILABLE:
            try:
                from Models.Reinforcement_Learning_Agent_with_DQN import train_dqn_agent

                print("Training DQN agent...")
                dqn_agent, dqn_env, dqn_results = train_dqn_agent(
                    featured_data,
                    feature_columns=feature_columns,
                    episodes=100,
                    model_type='advanced'
                )

                # Store model and metadata
                self.models['dqn'] = {
                    'model': dqn_agent,
                    'env': dqn_env,
                    'results': dqn_results,
                    'feature_columns': feature_columns,
                    'version': 1,
                    'last_trained': datetime.now().isoformat()
                }
            except Exception as e:
                print(f"Error training DQN agent: {e}")
                print("Continuing without DQN agent")

        # Create enhanced ensemble with self-learning capabilities
        print("Creating ensemble with self-learning capabilities...")

        # Collect available models
        models_dict = {}
        if 'lstm' in self.models:
            models_dict['lstm'] = self.models['lstm']['model']
        if 'xgboost' in self.models:
            models_dict['xgboost'] = self.models['xgboost']['model']
        if 'dqn' in self.models:
            models_dict['dqn'] = self.models['dqn']['model']
        if 'transformer' in self.models:
            models_dict['transformer'] = self.models['transformer']['model']
        if 'cnn_lstm' in self.models:
            models_dict['cnn_lstm'] = self.models['cnn_lstm']['model']

        # Calculate initial weights based on available models
        num_models = len(models_dict)
        if num_models == 0:
            print("No models available for ensemble. This should not happen.")
            return None

        # Base weight for each model
        base_weight = 1.0 / num_models

        # Adjust weights based on model types (give slightly more weight to advanced models)
        weights_dict = {model_type: base_weight for model_type in models_dict.keys()}

        # If we have advanced models, give them slightly more weight
        if 'transformer' in weights_dict or 'cnn_lstm' in weights_dict:
            # Boost advanced models
            advanced_boost = 0.1
            advanced_models = ['transformer', 'cnn_lstm']

            # Count advanced models
            num_advanced = sum(1 for model in advanced_models if model in weights_dict)

            if num_advanced > 0:
                # Reduce weight from basic models
                basic_models = [m for m in weights_dict.keys() if m not in advanced_models]
                weight_reduction = advanced_boost * num_advanced / len(basic_models) if basic_models else 0

                # Apply adjustments
                for model in advanced_models:
                    if model in weights_dict:
                        weights_dict[model] += advanced_boost

                for model in basic_models:
                    weights_dict[model] -= weight_reduction

        # Create the ensemble with the calculated weights
        print(f"Creating ensemble with models: {list(models_dict.keys())}")
        print(f"Initial weights: {weights_dict}")

        # Create ensemble with custom models and weights
        self.ensemble = create_ensemble(
            models=list(models_dict.values()),
            model_types=list(models_dict.keys()),
            weights=list(weights_dict.values()),
            window_size=20,
            adaptation_rate=self.adaptation_rate,
            confidence_threshold=self.confidence_threshold,
            memory_factor=self.memory_factor
        )

        # Track initial market conditions
        market_condition = self.ensemble.track_market_condition(featured_data)
        if market_condition:
            self.market_conditions.append({
                'timestamp': datetime.now().isoformat(),
                'condition': market_condition
            })

        print("Initial training complete")
        return self.ensemble

    def optimize_hyperparameters(self, data, model_name):
        """
        Optimize hyperparameters for a specific model
        """
        if model_name == 'xgboost':
            # Define search space for XGBoost
            param_space = {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 5, 7],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.6, 0.8, 1.0],
                'colsample_bytree': [0.6, 0.8, 1.0]
            }

            # Prepare data
            feature_columns = self.models['xgboost']['feature_columns']
            X = data[feature_columns].fillna(0)
            y = data['target_1min']

            # Train test split
            train_size = int(len(X) * 0.8)
            X_train, X_test = X.iloc[:train_size], X.iloc[train_size:]
            y_train, y_test = y.iloc[:train_size], y.iloc[train_size:]

            # Create and run search
            model = XGBClassifier(objective='binary:logistic', use_label_encoder=False)
            random_search = RandomizedSearchCV(
                model, param_space,
                n_iter=10,
                cv=3,
                scoring='accuracy',
                verbose=1
            )
            random_search.fit(X_train, y_train)

            # Update model with best parameters
            best_model = XGBClassifier(
                **random_search.best_params_,
                objective='binary:logistic',
                use_label_encoder=False
            )
            best_model.fit(X_train, y_train)

            self.models['xgboost']['model'] = best_model

            # Evaluate new model
            y_pred = best_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            print(f"Optimized XGBoost - Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

        elif model_name == 'lstm':
            # Define optimization function for Optuna
            def objective(trial):
                # Hyperparameters to optimize
                lstm_units = trial.suggest_int('lstm_units', 32, 256)
                gru_units = trial.suggest_int('gru_units', 16, 128)
                dense_units = trial.suggest_int('dense_units', 16, 64)
                dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5)
                learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)

                # Create model with trial parameters
                inputs = Input(shape=(30, len(self.models['lstm']['feature_columns'])))
                x = LSTM(lstm_units, return_sequences=True)(inputs)
                x = BatchNormalization()(x)
                x = Dropout(dropout_rate)(x)
                x = GRU(gru_units, return_sequences=False)(x)
                x = BatchNormalization()(x)
                x = Dropout(dropout_rate)(x)
                x = Dense(dense_units, activation='relu')(x)
                x = Dropout(dropout_rate)(x)
                outputs = Dense(1, activation='sigmoid')(x)

                model = Model(inputs=inputs, outputs=outputs)
                model.compile(
                    loss='binary_crossentropy',
                    optimizer=Adam(learning_rate=learning_rate),
                    metrics=['accuracy']
                )

                # Prepare data
                feature_columns = self.models['lstm']['feature_columns']
                X_train, X_val, X_test, y_train, y_val, y_test, _ = prepare_time_series(
                    data, feature_columns, 'target_1min'
                )

                # Train with early stopping
                callbacks = [EarlyStopping(monitor='val_loss', patience=5)]
                model.fit(
                    X_train, y_train,
                    epochs=30,
                    batch_size=32,
                    validation_data=(X_val, y_val),
                    callbacks=callbacks,
                    verbose=0
                )

                # Evaluate
                _, accuracy = model.evaluate(X_test, y_test, verbose=0)
                return accuracy

            # Create and run study
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=20)

            # Get best parameters
            best_params = study.best_params
            print(f"Best parameters: {best_params}")

            # Create and train final model with best parameters
            feature_columns = self.models['lstm']['feature_columns']
            X_train, X_val, X_test, y_train, y_val, y_test, scaler = prepare_time_series(
                data, feature_columns, 'target_1min'
            )

            inputs = Input(shape=(30, len(feature_columns)))
            x = LSTM(best_params['lstm_units'], return_sequences=True)(inputs)
            x = BatchNormalization()(x)
            x = Dropout(best_params['dropout_rate'])(x)
            x = GRU(best_params['gru_units'], return_sequences=False)(x)
            x = BatchNormalization()(x)
            x = Dropout(best_params['dropout_rate'])(x)
            x = Dense(best_params['dense_units'], activation='relu')(x)
            x = Dropout(best_params['dropout_rate'])(x)
            outputs = Dense(1, activation='sigmoid')(x)

            best_model = Model(inputs=inputs, outputs=outputs)
            best_model.compile(
                loss='binary_crossentropy',
                optimizer=Adam(learning_rate=best_params['learning_rate']),
                metrics=['accuracy']
            )

            callbacks = [EarlyStopping(monitor='val_loss', patience=10)]
            best_model.fit(
                X_train, y_train,
                epochs=100,
                batch_size=32,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1
            )

            # Update model
            self.models['lstm']['model'] = best_model

            # Evaluate
            _, accuracy = best_model.evaluate(X_test, y_test, verbose=0)
            y_pred_prob = best_model.predict(X_test)
            y_pred = (y_pred_prob > 0.5).astype(int)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            print(f"Optimized LSTM - Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

    def continuous_learning_loop(self, interval_hours=6):
        """
        Advanced continuous learning loop with self-adaptation capabilities

        This loop periodically:
        1. Fetches new market data
        2. Evaluates model performance
        3. Updates ensemble weights based on performance and market conditions
        4. Retrains underperforming models
        5. Optimizes hyperparameters
        6. Incorporates feedback from prediction outcomes

        Parameters:
        - interval_hours: Hours between learning iterations
        """
        print(f"Starting continuous learning loop with {interval_hours} hour interval")

        # Wait a bit before starting to let the UI initialize
        print("Waiting 10 seconds before starting continuous learning to let UI initialize...")
        time.sleep(10)

        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Running continuous learning iteration")

                # Fetch new data
                print("Fetching new market data...")
                new_data = self.data_collector.fetch_historical_data(lookback_days=30)  # Get more data for better learning
                preprocessed_data = self.data_collector.preprocess_data(new_data)

                # Engineer features
                print("Engineering features...")
                featured_data = self.feature_engineer(preprocessed_data)

                # Track market conditions
                print("Analyzing market conditions...")
                market_condition = self.ensemble.track_market_condition(featured_data)
                if market_condition:
                    self.market_conditions.append({
                        'timestamp': datetime.now().isoformat(),
                        'condition': market_condition
                    })
                    print(f"Current market volatility: {market_condition.get('volatility', 'unknown')}")
                    if 'trend_strength' in market_condition:
                        trend_direction = "uptrend" if market_condition.get('uptrend', False) else "downtrend"
                        print(f"Trend strength: {market_condition['trend_strength']:.4f} ({trend_direction})")

                # Evaluate current models on new data
                print("Evaluating model performance...")
                performance_metrics = self.evaluate_models(featured_data)

                # Store performance history
                self.performance_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'metrics': performance_metrics,
                    'market_condition': market_condition
                })

                # Keep history limited
                if len(self.performance_history) > 100:
                    self.performance_history = self.performance_history[-100:]

                # Print performance summary
                print("\nPerformance Summary:")
                for model_name, metrics in performance_metrics.items():
                    if model_name != 'ensemble':  # Individual models
                        print(f"  {model_name.upper()}: Accuracy={metrics['accuracy']:.4f}, F1={metrics['f1']:.4f}")

                if 'ensemble' in performance_metrics:
                    ensemble_metrics = performance_metrics['ensemble']
                    print(f"  ENSEMBLE: Accuracy={ensemble_metrics['accuracy']:.4f}, " +
                          f"Precision={ensemble_metrics['precision']:.4f}, " +
                          f"Recall={ensemble_metrics['recall']:.4f}, " +
                          f"F1={ensemble_metrics['f1']:.4f}")

                # Update ensemble weights based on recent performance and market conditions
                print("\nUpdating ensemble weights based on performance and market conditions...")
                model_metrics = [performance_metrics.get(model_type, {}) for model_type in self.ensemble.model_types]
                self.ensemble.update_weights(model_metrics, market_condition)

                # Process prediction feedback for self-learning
                if self.prediction_feedback:
                    print("\nIncorporating prediction feedback for self-learning...")
                    recent_feedback = self.prediction_feedback[-min(20, len(self.prediction_feedback)):]

                    for feedback in recent_feedback:
                        if not feedback.get('processed', False):
                            adjustment_made = self.ensemble.feedback_learning(
                                feedback['prediction'],
                                feedback['actual'],
                                feedback['confidence']
                            )
                            feedback['processed'] = True

                            if adjustment_made:
                                print("Model weights adjusted based on prediction feedback")

                # Periodically retrain models that are underperforming
                for model_name, metrics in performance_metrics.items():
                    if model_name in self.models and metrics['accuracy'] < self.retraining_threshold:
                        print(f"\nRetraining {model_name.upper()} due to low accuracy: {metrics['accuracy']:.4f}")

                        if model_name == 'lstm':
                            # Retrain LSTM model
                            new_model, history, _ = train_lstm_gru_model(
                                featured_data,
                                self.models['lstm']['feature_columns'],
                                model_type='advanced'
                            )

                            # Update model
                            self.models['lstm']['model'] = new_model
                            self.models['lstm']['training_history'] = history.history if history else None
                            self.models['lstm']['last_trained'] = datetime.now().isoformat()
                            self.models['lstm']['version'] += 1

                            # Update ensemble
                            self.ensemble.models[self.ensemble.model_types.index('lstm')] = new_model

                            print(f"LSTM model retrained (version {self.models['lstm']['version']})")

                        elif model_name == 'xgboost':
                            # Retrain XGBoost model
                            new_model, feature_importance, evaluation, selected_features = train_xgboost_model(
                                featured_data,
                                self.models['xgboost']['feature_columns'],
                                optimize_hyperparams=True
                            )

                            # Update model
                            self.models['xgboost']['model'] = new_model
                            self.models['xgboost']['feature_importance'] = feature_importance
                            self.models['xgboost']['evaluation'] = evaluation
                            self.models['xgboost']['last_trained'] = datetime.now().isoformat()
                            self.models['xgboost']['version'] += 1

                            # Update ensemble
                            self.ensemble.models[self.ensemble.model_types.index('xgboost')] = new_model

                            print(f"XGBoost model retrained (version {self.models['xgboost']['version']})")

                        elif model_name == 'dqn':
                            # Retrain DQN agent
                            new_agent, env, results = train_dqn_agent(
                                featured_data,
                                feature_columns=self.models['dqn']['feature_columns'],
                                episodes=50  # Shorter training for updates
                            )

                            # Update model
                            self.models['dqn']['model'] = new_agent
                            self.models['dqn']['results'] = results
                            self.models['dqn']['last_trained'] = datetime.now().isoformat()
                            self.models['dqn']['version'] += 1

                            # Update ensemble
                            self.ensemble.models[self.ensemble.model_types.index('dqn')] = new_agent

                            print(f"DQN agent retrained (version {self.models['dqn']['version']})")

                # Periodically optimize hyperparameters
                if len(self.performance_history) % self.optimization_interval == 0:
                    print("\nOptimizing hyperparameters...")

                    # Find the worst performing model (excluding ensemble)
                    model_performances = {k: v['accuracy'] for k, v in performance_metrics.items()
                                         if k in self.models}

                    if model_performances:
                        model_to_optimize = min(model_performances, key=model_performances.get)
                        print(f"Optimizing {model_to_optimize.upper()} hyperparameters")
                        self.optimize_hyperparameters(featured_data, model_to_optimize)

                # Save ensemble configuration
                print("\nSaving ensemble configuration...")
                os.makedirs('models', exist_ok=True)
                self.ensemble.save(os.path.join('models', f'ensemble_config_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'))

                # Sleep for the specified interval in smaller chunks to be more responsive
                print(f"\nContinuous learning iteration complete. Next iteration in {interval_hours} hours.")

                # Sleep in 5-minute chunks to be more responsive to UI
                for _ in range(interval_hours * 12):  # 5-minute chunks for the specified hours
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit (this would be set by a signal handler or UI)
                    if hasattr(self, '_exit_learning_loop') and self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return

            except Exception as e:
                print(f"\nError in continuous learning loop: {e}")
                import traceback
                traceback.print_exc()
                print("Waiting 1 hour before trying again...")

                # Sleep in 5-minute chunks
                for _ in range(12):  # 5-minute chunks for 1 hour
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit
                    if hasattr(self, '_exit_learning_loop') and self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return

    def evaluate_models(self, data):
        """
        Evaluate all models on new data
        """
        results = {}

        # Prepare targets
        y_true = data['target_1min'].values[-100:]  # Evaluate on last 100 points

        # Evaluate LSTM model
        if 'lstm' in self.models:
            # Prepare sequence data
            X_test, y_test = prepare_lstm_test_data(
                data,
                self.models['lstm']['feature_columns'],
                self.models['lstm']['scaler'],
                'target_1min'
            )

            y_pred_prob = self.models['lstm']['model'].predict(X_test)
            y_pred = (y_pred_prob > 0.5).astype(int).flatten()

            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            results['lstm'] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }

        # Evaluate CNN-LSTM model
        if 'cnn_lstm' in self.models:
            # Prepare sequence data (reuse the same data preparation as LSTM)
            X_test, y_test = prepare_lstm_test_data(
                data,
                self.models['cnn_lstm']['feature_columns'],
                self.models['cnn_lstm']['scaler'],
                'target_1min'
            )

            y_pred_prob = self.models['cnn_lstm']['model'].predict(X_test)
            y_pred = (y_pred_prob > 0.5).astype(int).flatten()

            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            results['cnn_lstm'] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }

        # Evaluate Transformer model
        if 'transformer' in self.models:
            try:
                from Transformer_Model import predict_with_transformer

                # Prepare data for transformer prediction
                # Get the last 100 samples for evaluation
                eval_data = data.iloc[-100:].copy()

                # Make predictions one by one
                y_pred = []
                y_test = eval_data['target_1min'].values

                for i in range(len(eval_data)):
                    # Get data up to this point
                    current_data = eval_data.iloc[:i+1]

                    # Make prediction
                    prediction = predict_with_transformer(self.models['transformer'], current_data)

                    # Add prediction to list
                    if prediction and 'prediction' in prediction and prediction['prediction'] is not None:
                        y_pred.append(prediction['prediction'])

                # Ensure we have at least some predictions
                if len(y_pred) > 0:
                    # Adjust y_test to match y_pred length
                    y_test = y_test[-len(y_pred):]

                    # Calculate metrics
                    accuracy = accuracy_score(y_test, y_pred)
                    precision = precision_score(y_test, y_pred)
                    recall = recall_score(y_test, y_pred)
                    f1 = f1_score(y_test, y_pred)

                    results['transformer'] = {
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'f1': f1
                    }
                else:
                    # No predictions available
                    results['transformer'] = {
                        'accuracy': 0.5,  # Default
                        'precision': 0.5,
                        'recall': 0.5,
                        'f1': 0.5
                    }
            except Exception as e:
                print(f"Error evaluating Transformer model: {e}")
                # Provide default metrics
                results['transformer'] = {
                    'accuracy': 0.5,
                    'precision': 0.5,
                    'recall': 0.5,
                    'f1': 0.5
                }

        # Evaluate XGBoost
        if 'xgboost' in self.models:
            # Prepare tabular data
            X_test = data[self.models['xgboost']['feature_columns']].iloc[-100:].fillna(0)
            y_test = data['target_1min'].iloc[-100:].values

            # Predict and evaluate
            y_pred = self.models['xgboost']['model'].predict(X_test)

            # Calculate metrics
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            results['xgboost'] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }

        # Evaluate ensemble
        if self.ensemble is not None:
            # Prepare data for ensemble prediction
            X = {
                'sequence_data': X_test if 'lstm' in self.models else None,
                'tabular_data': data[self.models['xgboost']['feature_columns']].iloc[-100:].fillna(0) if 'xgboost' in self.models else None
            }

            # Predict
            ensemble_prediction = self.ensemble.predict(X)

            # Ensure y_pred is an array, not a scalar
            if isinstance(ensemble_prediction['prediction'], (int, float)):
                y_pred = np.array([ensemble_prediction['prediction']])
            else:
                y_pred = ensemble_prediction['prediction']

            # Ensure y_test and y_pred have the same length
            if len(y_pred) == 1 and len(y_test) > 1:
                # If we only have one prediction but multiple test values,
                # we'll use the last test value for comparison
                y_test_single = np.array([y_test[-1]])

                # Calculate metrics
                accuracy = accuracy_score(y_test_single, y_pred)
                precision = precision_score(y_test_single, y_pred)
                recall = recall_score(y_test_single, y_pred)
                f1 = f1_score(y_test_single, y_pred)
            else:
                # Make sure lengths match
                min_len = min(len(y_test), len(y_pred))

                # Calculate metrics
                accuracy = accuracy_score(y_test[:min_len], y_pred[:min_len])
                precision = precision_score(y_test[:min_len], y_pred[:min_len])
                recall = recall_score(y_test[:min_len], y_pred[:min_len])
                f1 = f1_score(y_test[:min_len], y_pred[:min_len])

            results['ensemble'] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }

        return results

    def record_prediction_feedback(self, prediction, actual_outcome, confidence, timestamp=None, prediction_details=None):
        """
        Record feedback from a prediction for self-learning with enhanced learning capabilities

        Parameters:
        - prediction: The prediction made (1 for UP, 0 for DOWN)
        - actual_outcome: The actual outcome (1 for UP, 0 for DOWN)
        - confidence: The confidence level of the prediction (0-1)
        - timestamp: Optional timestamp (defaults to current time)
        - prediction_details: Optional dictionary with additional prediction details
        """
        if timestamp is None:
            timestamp = datetime.now().isoformat()

        # Create enhanced feedback entry
        feedback = {
            'timestamp': timestamp,
            'prediction': prediction,
            'actual': actual_outcome,
            'confidence': confidence,
            'correct': prediction == actual_outcome,
            'processed': False
        }

        # Add additional details if provided
        if prediction_details:
            feedback.update({
                'direction': prediction_details.get('direction', 'UP' if prediction == 1 else 'DOWN'),
                'model_agreement': prediction_details.get('model_agreement', 0.0),
                'bias_adjustment': prediction_details.get('bias_adjustment', 0.0),
                'original_probability': prediction_details.get('original_probability', 0.5)
            })
        else:
            feedback['direction'] = 'UP' if prediction == 1 else 'DOWN'

        # Add to feedback history
        self.prediction_feedback.append(feedback)

        # Keep history limited
        if len(self.prediction_feedback) > 1000:
            self.prediction_feedback = self.prediction_feedback[-1000:]

        # Check for consecutive incorrect predictions of the same type
        if len(self.prediction_feedback) >= 3:
            recent_feedback = self.prediction_feedback[-3:]

            # Check if all recent predictions were incorrect and in the same direction
            all_incorrect = all(not f['correct'] for f in recent_feedback)
            same_direction = len(set(f['direction'] for f in recent_feedback)) == 1

            if all_incorrect and same_direction:
                direction = recent_feedback[0]['direction']
                print(f"WARNING: Detected {len(recent_feedback)} consecutive incorrect {direction} predictions!")

                # Apply stronger adjustment to the ensemble weights
                if self.ensemble:
                    # Create adjustment to reverse the bias
                    model_adjustments = {}

                    # If we have individual model predictions, use them
                    if prediction_details and 'individual_predictions' in prediction_details:
                        for model_pred in prediction_details['individual_predictions']:
                            model_type = model_pred.get('model_type')
                            model_prediction = model_pred.get('prediction')

                            # If model prediction was wrong, reduce its weight
                            if model_prediction != actual_outcome:
                                model_adjustments[model_type] = -0.3  # Stronger penalty
                            else:
                                model_adjustments[model_type] = 0.3   # Stronger reward
                    else:
                        # Apply general adjustment to all models
                        for model_type in self.ensemble.model_types:
                            model_adjustments[model_type] = 0.1  # Small adjustment to all models

                    # Apply adjustments
                    print("Applying stronger model weight adjustments due to consecutive errors...")
                    self.ensemble.apply_feedback_adjustments(model_adjustments)

                    # Force retraining of the model on next iteration
                    self.force_retraining = True
                    print("Scheduled model retraining due to consecutive prediction errors")

        # If it's a high confidence prediction, process it immediately
        if confidence >= self.confidence_threshold and self.ensemble:
            # For high confidence predictions, apply immediate feedback
            adjustment_made = self.ensemble.feedback_learning(prediction, actual_outcome, confidence)
            feedback['processed'] = True

            # If high confidence prediction was wrong, take additional action
            if not feedback['correct'] and confidence > 0.8:
                print(f"High confidence wrong prediction! Direction: {feedback['direction']}, Confidence: {confidence:.2f}")

                # Apply immediate bias correction
                direction_bias = 0.2 if actual_outcome == 1 else -0.2  # Push toward correct direction
                self.ensemble.bias_correction = direction_bias
                print(f"Applied bias correction of {direction_bias} toward {'UP' if actual_outcome == 1 else 'DOWN'}")

        return feedback['correct']

    def get_performance_summary(self):
        """
        Get a summary of model performance

        Returns:
        - Dictionary containing performance summary
        """
        summary = {}

        # Overall accuracy
        if self.prediction_feedback:
            recent_feedback = self.prediction_feedback[-min(100, len(self.prediction_feedback)):]
            overall_accuracy = sum(f['correct'] for f in recent_feedback) / len(recent_feedback)
            summary['overall_accuracy'] = overall_accuracy

            # High confidence accuracy
            high_conf_feedback = [f for f in recent_feedback if f['confidence'] >= self.confidence_threshold]
            if high_conf_feedback:
                high_conf_accuracy = sum(f['correct'] for f in high_conf_feedback) / len(high_conf_feedback)
                summary['high_confidence_accuracy'] = high_conf_accuracy

        # Model versions
        summary['model_versions'] = self.model_versions

        # Ensemble weights
        if self.ensemble:
            summary['ensemble_weights'] = {
                model_type: weight
                for model_type, weight in zip(self.ensemble.model_types, self.ensemble.weights)
            }

        return summary


def prepare_lstm_test_data(data, feature_columns, scaler, target_column, seq_length=30):
    """
    Prepare LSTM test data for evaluation
    """
    # Extract features and target
    test_data = data[feature_columns].values[-130:]  # Get last 130 points (to form 100 sequences)
    test_target = data[target_column].values[-100:]  # The corresponding 100 targets

    # Scale features
    test_data = scaler.transform(test_data)

    # Create sequences
    X_test, y_test = [], []
    for i in range(len(test_data) - seq_length):
        X_test.append(test_data[i:i+seq_length])
        y_test.append(test_target[i])

    X_test = np.array(X_test)
    y_test = np.array(y_test)

    return X_test, y_test