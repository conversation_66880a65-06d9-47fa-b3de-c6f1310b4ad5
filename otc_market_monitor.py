#!/usr/bin/env python
"""
OTC Market Monitor with Live Charts

This application provides a real-time view of OTC markets from Quotex,
specifically focusing on USD/ARS (OTC) and USD/BRL (OTC).
It features live candlestick charts with real-time updates and market information.
"""

import os
import sys
import asyncio
import configparser
import threading
import time
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QComboBox, QPushButton,
                            QGridLayout, QGroupBox, QSplitter, QFrame,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QTabWidget, QProgressBar, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import mplfinance as mpf
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

from quotexapi.stable_api import Quotex

# Default OTC markets to monitor
DEFAULT_MARKETS = ["USD/ARS (OTC)", "USD/BRL (OTC)"]

# Load credentials from config.ini
config = configparser.RawConfigParser()
config_path = os.path.join('settings', 'config.ini')
if os.path.exists(config_path):
    config.read(config_path)
    email = config.get('settings', 'email', fallback=None)
    password = config.get('settings', 'password', fallback=None)
else:
    email = None
    password = None

# Initialize Quotex client
client = Quotex(
    email=email,
    password=password,
    lang="en",  # Use English language
)

# Signal class for thread communication
class WorkerSignals(QObject):
    candles_updated = pyqtSignal(str, object)
    markets_updated = pyqtSignal(object)
    connection_status = pyqtSignal(bool, str)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)

# Worker thread for background data fetching
class DataWorker(QThread):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.signals = WorkerSignals()
        self.running = True
        self.markets_to_monitor = DEFAULT_MARKETS.copy()
        self.timeframe = 60  # 1-minute candles by default
        self.candle_count = 100
        self.connected = False
        self.client = client
        self.markets_data = {}
        self.otc_markets = {}
        self.update_interval = 1  # seconds

    async def connect_to_api(self):
        """Connect to Quotex API"""
        try:
            self.signals.progress.emit(10)
            check_connect, message = await self.client.connect()
            self.signals.connection_status.emit(check_connect, message)
            self.connected = check_connect

            if check_connect:
                self.signals.progress.emit(30)
                # Get all assets data
                all_data = self.client.get_payment()

                self.signals.progress.emit(50)
                # Get all asset codes
                codes_asset = await self.client.get_all_assets()

                self.signals.progress.emit(70)
                # Create a mapping from display name to code
                asset_codes = {}
                for code, name in codes_asset.items():
                    asset_codes[name] = code

                # Filter for OTC markets only
                self.otc_markets = {name: data for name, data in all_data.items() if "(OTC)" in name}
                self.markets_data = {"all_data": all_data, "asset_codes": asset_codes, "otc_markets": self.otc_markets}

                self.signals.progress.emit(90)
                # Emit markets data
                self.signals.markets_updated.emit(self.markets_data)
                self.signals.progress.emit(100)

            return check_connect
        except Exception as e:
            self.signals.error.emit(f"Connection error: {str(e)}")
            return False

    async def fetch_candles_for_market(self, market):
        """Fetch candles for a specific market"""
        try:
            if not self.connected:
                return

            # Convert display name to API name (e.g., "USD/ARS (OTC)" to "USDARS_otc")
            # First, handle the special format
            if "(" in market and ")" in market:
                # Extract the base part and the OTC part
                base_part = market.split("(")[0].strip()
                # Remove slashes and spaces from base part
                base_part = base_part.replace("/", "")
                # Add _otc suffix
                api_market_name = f"{base_part}_otc"
            else:
                # Regular conversion for non-OTC markets
                api_market_name = market.replace("/", "")

            # Debug output
            print(f"Fetching candles for market: {market} (API name: {api_market_name})")

            # Check if asset is open first
            asset_name, asset_data = await self.client.get_available_asset(api_market_name, force_open=True)
            print(f"Asset check result: {asset_name}, {asset_data}")

            # Get candles - use the original API market name if the asset_name doesn't contain "otc"
            end_from_time = time.time()
            candle_market_name = api_market_name if "otc" not in asset_name.lower() else asset_name
            print(f"Using market name for candles: {candle_market_name}")
            candles = await self.client.get_candles(candle_market_name, end_from_time, self.candle_count, self.timeframe)

            if candles and len(candles) > 0:
                print(f"Retrieved {len(candles)} candles for {market}")

                # Convert to DataFrame
                df = pd.DataFrame(candles)

                # Add human-readable timestamp
                df['timestamp'] = df['time'].apply(lambda x: datetime.fromtimestamp(x))

                # Set timestamp as index
                df.set_index('timestamp', inplace=True)

                # Rename columns to match mplfinance requirements
                df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volume': 'Volume'}, inplace=True)

                # Ensure all required columns exist
                for col in ['Open', 'High', 'Low', 'Close']:
                    if col not in df.columns:
                        print(f"Warning: {col} column missing, adding zeros")
                        df[col] = 0.0
                    else:
                        # Ensure numeric type
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                # Emit updated candles
                self.signals.candles_updated.emit(market, df)
            else:
                print(f"No candles returned for {market} (API name: {api_market_name})")
                self.signals.error.emit(f"No candle data retrieved for {market}")
        except Exception as e:
            print(f"Error fetching candles for {market}: {str(e)}")
            self.signals.error.emit(f"Error fetching candles for {market}: {str(e)}")

    async def run_async(self):
        """Main async loop"""
        # Connect to API
        connected = await self.connect_to_api()

        if not connected:
            self.signals.error.emit("Failed to connect to API. Check credentials.")
            return

        # Main data fetch loop
        while self.running:
            for market in self.markets_to_monitor:
                await self.fetch_candles_for_market(market)

            # Wait before next update
            await asyncio.sleep(self.update_interval)

    def run(self):
        """Thread entry point"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.run_async())
        finally:
            loop.close()

    def stop(self):
        """Stop the worker thread"""
        self.running = False
        self.wait()

# Candlestick chart widget with real-time updates
class LiveCandlestickChart(FigureCanvas):
    def __init__(self, parent=None, width=10, height=8, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super().__init__(self.fig)
        self.setParent(parent)
        self.df = None
        self.market_name = ""

        # Set up the plot
        self.setup_plot()

    def setup_plot(self):
        """Set up the initial plot"""
        self.axes.set_title("Loading data...", fontsize=12)
        self.axes.set_xlabel("Time")
        self.axes.set_ylabel("Price")
        self.fig.tight_layout()
        self.draw()

    def update_chart(self, df, market_name):
        """Update the chart with new data"""
        self.df = df
        self.market_name = market_name

        # Clear the axes
        self.axes.clear()

        if self.df is not None and not self.df.empty:
            try:
                # Make sure we have all required columns with proper types
                for col in ['Open', 'High', 'Low', 'Close']:
                    if col not in self.df.columns:
                        self.df[col] = 0.0
                    else:
                        # Ensure numeric type
                        self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

                # Make sure we have enough valid data
                if len(self.df) >= 2:
                    # Plot candlestick chart - don't use title parameter
                    mpf.plot(self.df, type='candle', style='yahoo',
                            ax=self.axes, volume=False,
                            ylabel="Price")
                    # Set title directly on the axes
                    self.axes.set_title(f"{market_name} - Live Chart")
                else:
                    # Not enough data for candlestick chart, use line chart instead
                    self.axes.plot(self.df.index, self.df['Close'], label='Close Price')
                    self.axes.set_title(f"{market_name} - Live Chart (Limited Data)")
                    self.axes.set_ylabel("Price")
                    self.axes.legend()

                # Add grid
                self.axes.grid(True, alpha=0.3)

                # Format x-axis
                self.axes.tick_params(axis='x', rotation=45)

                # Adjust layout
                self.fig.tight_layout()
            except Exception as e:
                # If plotting fails, show error message on chart
                self.axes.text(0.5, 0.5, f"Error plotting chart: {str(e)}",
                              horizontalalignment='center', verticalalignment='center',
                              transform=self.axes.transAxes, fontsize=12)
                self.axes.set_title(f"{market_name} - Chart Error")
                print(f"Error updating chart: {str(e)}")

            # Redraw
            self.draw()
        else:
            # No data, show message
            self.axes.text(0.5, 0.5, "No data available",
                          horizontalalignment='center', verticalalignment='center',
                          transform=self.axes.transAxes, fontsize=14)
            self.axes.set_title(f"{market_name} - Waiting for Data")
            self.draw()

# Market tab widget
class MarketTab(QWidget):
    def __init__(self, market_name, parent=None):
        super().__init__(parent)
        self.market_name = market_name
        self.candles_df = None

        # Create layout
        layout = QVBoxLayout()

        # Chart
        self.chart = LiveCandlestickChart(self, width=8, height=5)

        # Market info
        info_group = QGroupBox("Market Information")
        info_layout = QGridLayout()

        self.market_status_label = QLabel("Status: -")
        self.market_payout_1m_label = QLabel("Payout (1M): -")
        self.market_payout_5m_label = QLabel("Payout (5M): -")
        self.last_update_label = QLabel("Last Update: -")

        info_layout.addWidget(self.market_status_label, 0, 0)
        info_layout.addWidget(self.market_payout_1m_label, 1, 0)
        info_layout.addWidget(self.market_payout_5m_label, 2, 0)
        info_layout.addWidget(self.last_update_label, 3, 0)

        info_group.setLayout(info_layout)

        # Latest candle info
        latest_group = QGroupBox("Latest Candle")
        latest_layout = QGridLayout()

        self.candle_time_label = QLabel("Time: -")
        self.candle_open_label = QLabel("Open: -")
        self.candle_high_label = QLabel("High: -")
        self.candle_low_label = QLabel("Low: -")
        self.candle_close_label = QLabel("Close: -")

        latest_layout.addWidget(self.candle_time_label, 0, 0)
        latest_layout.addWidget(self.candle_open_label, 1, 0)
        latest_layout.addWidget(self.candle_high_label, 2, 0)
        latest_layout.addWidget(self.candle_low_label, 3, 0)
        latest_layout.addWidget(self.candle_close_label, 4, 0)

        latest_group.setLayout(latest_layout)

        # Add widgets to layout
        layout.addWidget(self.chart)

        # Info panel in horizontal layout
        info_panel = QHBoxLayout()
        info_panel.addWidget(info_group)
        info_panel.addWidget(latest_group)

        layout.addLayout(info_panel)

        self.setLayout(layout)

    def update_chart(self, df):
        """Update the chart with new data"""
        self.candles_df = df
        self.chart.update_chart(df, self.market_name)
        self.update_latest_candle()
        self.last_update_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

    def update_market_info(self, market_data):
        """Update market information"""
        if market_data:
            status = "🟢 Open" if market_data["open"] else "🔴 Closed"
            self.market_status_label.setText(f"Status: {status}")

            profit_1m = f"{market_data['profit']['1M']}%" if '1M' in market_data['profit'] else "N/A"
            self.market_payout_1m_label.setText(f"Payout (1M): {profit_1m}")

            profit_5m = f"{market_data['profit']['5M']}%" if '5M' in market_data['profit'] else "N/A"
            self.market_payout_5m_label.setText(f"Payout (5M): {profit_5m}")

    def update_latest_candle(self):
        """Update latest candle information"""
        if self.candles_df is not None and not self.candles_df.empty:
            latest = self.candles_df.iloc[-1]

            self.candle_time_label.setText(f"Time: {self.candles_df.index[-1].strftime('%H:%M:%S')}")
            self.candle_open_label.setText(f"Open: {latest['Open']:.5f}")
            self.candle_high_label.setText(f"High: {latest['High']:.5f}")
            self.candle_low_label.setText(f"Low: {latest['Low']:.5f}")

            # Set close with color
            close_text = f"Close: {latest['Close']:.5f}"
            if latest['Close'] >= latest['Open']:
                self.candle_close_label.setText(close_text)
                self.candle_close_label.setStyleSheet("color: green; font-weight: bold")
            else:
                self.candle_close_label.setText(close_text)
                self.candle_close_label.setStyleSheet("color: red; font-weight: bold")

# Main application window
class OTCMarketMonitor(QMainWindow):
    def __init__(self):
        super().__init__()

        # Set up the UI
        self.setWindowTitle("OTC Market Monitor")
        self.setGeometry(100, 100, 1200, 800)

        # Initialize variables
        self.markets_data = {}
        self.market_tabs = {}

        # Set up the worker thread
        self.worker = DataWorker()
        self.worker.signals.candles_updated.connect(self.update_candles)
        self.worker.signals.markets_updated.connect(self.update_markets)
        self.worker.signals.connection_status.connect(self.update_connection_status)
        self.worker.signals.error.connect(self.show_error)
        self.worker.signals.progress.connect(self.update_progress)

        # Create UI components
        self.create_ui()

        # Start the worker thread
        self.worker.start()

    def create_ui(self):
        """Create the user interface"""
        # Main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # Top controls
        controls_layout = QHBoxLayout()

        # Timeframe selection
        timeframe_label = QLabel("Timeframe:")
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1 Minute", "5 Minutes", "15 Minutes", "30 Minutes", "1 Hour"])
        self.timeframe_combo.setCurrentIndex(0)
        self.timeframe_combo.currentTextChanged.connect(self.change_timeframe)

        # Update interval
        interval_label = QLabel("Update Interval:")
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["1 second", "2 seconds", "5 seconds", "10 seconds"])
        self.interval_combo.setCurrentIndex(0)
        self.interval_combo.currentTextChanged.connect(self.change_update_interval)

        # Auto-scroll checkbox
        self.auto_scroll_check = QCheckBox("Auto-scroll to latest candle")
        self.auto_scroll_check.setChecked(True)

        # Refresh button
        self.refresh_button = QPushButton("Refresh Data")
        self.refresh_button.clicked.connect(self.refresh_data)

        # Status label
        self.status_label = QLabel("Connecting...")

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setMaximumWidth(200)

        # Add controls to layout
        controls_layout.addWidget(timeframe_label)
        controls_layout.addWidget(self.timeframe_combo)
        controls_layout.addWidget(interval_label)
        controls_layout.addWidget(self.interval_combo)
        controls_layout.addWidget(self.auto_scroll_check)
        controls_layout.addWidget(self.refresh_button)
        controls_layout.addStretch()
        controls_layout.addWidget(self.status_label)
        controls_layout.addWidget(self.progress_bar)

        # Tab widget for markets
        self.tabs = QTabWidget()

        # Create tabs for default markets
        for market in DEFAULT_MARKETS:
            tab = MarketTab(market)
            self.tabs.addTab(tab, market)
            self.market_tabs[market] = tab

        # Add components to main layout
        main_layout.addLayout(controls_layout)
        main_layout.addWidget(self.tabs)

        # Set the main layout
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def update_markets(self, markets_data):
        """Update markets data and UI"""
        self.markets_data = markets_data

        # Update market info in tabs
        for market_name, tab in self.market_tabs.items():
            if market_name in markets_data["all_data"]:
                tab.update_market_info(markets_data["all_data"][market_name])

    def update_candles(self, market_name, df):
        """Update candles data for a specific market"""
        if market_name in self.market_tabs:
            self.market_tabs[market_name].update_chart(df)

    def update_connection_status(self, connected, message):
        """Update connection status display"""
        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet("color: green; font-weight: bold")
        else:
            self.status_label.setText(f"Disconnected: {message}")
            self.status_label.setStyleSheet("color: red; font-weight: bold")

    def update_progress(self, value):
        """Update progress bar"""
        self.progress_bar.setValue(value)

    def show_error(self, error_message):
        """Display error message"""
        self.status_label.setText(f"Error: {error_message}")
        self.status_label.setStyleSheet("color: red; font-weight: bold")

    def change_timeframe(self, timeframe_text):
        """Change the chart timeframe"""
        # Map timeframe text to seconds
        timeframe_map = {
            "1 Minute": 60,
            "5 Minutes": 300,
            "15 Minutes": 900,
            "30 Minutes": 1800,
            "1 Hour": 3600
        }

        if timeframe_text in timeframe_map:
            self.worker.timeframe = timeframe_map[timeframe_text]
            self.refresh_data()

    def change_update_interval(self, interval_text):
        """Change the update interval"""
        # Map interval text to seconds
        interval_map = {
            "1 second": 1,
            "2 seconds": 2,
            "5 seconds": 5,
            "10 seconds": 10
        }

        if interval_text in interval_map:
            self.worker.update_interval = interval_map[interval_text]

    def refresh_data(self):
        """Manually refresh data"""
        self.status_label.setText("Refreshing data...")
        self.status_label.setStyleSheet("color: blue; font-weight: bold")
        self.progress_bar.setValue(0)

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop the worker thread
        self.worker.stop()
        event.accept()

# Main entry point
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application style
    app.setStyle("Fusion")

    # Create dark palette
    dark_palette = QPalette()
    dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.WindowText, Qt.white)
    dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
    dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ToolTipBase, Qt.white)
    dark_palette.setColor(QPalette.ToolTipText, Qt.white)
    dark_palette.setColor(QPalette.Text, Qt.white)
    dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
    dark_palette.setColor(QPalette.ButtonText, Qt.white)
    dark_palette.setColor(QPalette.BrightText, Qt.red)
    dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
    dark_palette.setColor(QPalette.HighlightedText, Qt.black)

    # Apply dark palette
    app.setPalette(dark_palette)

    # Create and show window
    window = OTCMarketMonitor()
    window.show()
    sys.exit(app.exec_())
