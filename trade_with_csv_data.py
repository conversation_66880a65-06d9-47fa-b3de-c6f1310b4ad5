#!/usr/bin/env python
"""
Trade with CSV data using the trained XGBoost model.
This script loads the CSV data, processes it, and uses it for trading.
"""

import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
import time
import sys

# Import custom modules
from Models.Feature_Engineering import engineer_features
from Models.model_manager import <PERSON><PERSON>ana<PERSON>, TENSORFLOW_AVAILABLE

def load_and_process_data(csv_path, cache_dir='data_cache'):
    """
    Load data from CSV, process it, and cache the processed data

    Parameters:
    - csv_path: Path to CSV file
    - cache_dir: Directory to cache processed data

    Returns:
    - df: Processed DataFrame
    """
    # Create cache directory if it doesn't exist
    os.makedirs(cache_dir, exist_ok=True)

    # Generate cache filename based on input file
    filename = os.path.basename(csv_path)
    cache_path = os.path.join(cache_dir, f"processed_{filename}.pkl")

    # Check if cached data exists
    if os.path.exists(cache_path):
        print(f"Loading cached processed data from {cache_path}")
        return joblib.load(cache_path)

    # Load data from CSV
    print(f"Loading data from {csv_path}")
    df = pd.read_csv(csv_path)

    # Convert timestamp to datetime if it exists
    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'])

    # Process data
    print("Engineering features...")
    df = engineer_features(df)

    # Cache processed data
    print(f"Caching processed data to {cache_path}")
    joblib.dump(df, cache_path)

    return df

def simulate_trading(df, model_manager, num_trades=5, start_index=None):
    """
    Simulate trading using the processed data and model

    Parameters:
    - df: Processed DataFrame
    - model_manager: ModelManager instance
    - num_trades: Number of trades to simulate
    - start_index: Index to start simulation from (default: random)

    Returns:
    - results: Dictionary with simulation results
    """
    if start_index is None:
        # Start from a random point in the data, but leave enough data for simulation
        start_index = np.random.randint(100, len(df) - num_trades - 10)

    print(f"Starting simulation from index {start_index}")

    results = {
        'trades': [],
        'wins': 0,
        'losses': 0,
        'win_rate': 0.0,
        'profit_loss': 0.0
    }

    for i in range(num_trades):
        current_index = start_index + i

        # Get current candle data
        current_data = df.iloc[:current_index+1].copy()

        # Make prediction
        prediction = model_manager.predict(current_data, model_name='xgboost')

        # Check if prediction is valid
        if prediction.get('prediction') is None:
            print(f"Trade {i+1}: Invalid prediction, skipping...")
            continue

        # Get actual outcome (next candle)
        next_candle = df.iloc[current_index+1]
        actual_outcome = 1 if next_candle['close'] > current_data.iloc[-1]['close'] else 0

        # Determine if prediction was correct
        is_correct = prediction['prediction'] == actual_outcome

        # Calculate profit/loss (assume 80% payout for correct predictions)
        pnl = 0.8 if is_correct else -1.0

        # Update results
        trade_result = {
            'index': current_index,
            'timestamp': df.iloc[current_index]['timestamp'] if 'timestamp' in df.columns else None,
            'prediction': prediction['prediction'],
            'direction': prediction['direction'],
            'confidence': prediction['confidence'],
            'actual': actual_outcome,
            'actual_direction': "UP" if actual_outcome == 1 else "DOWN",
            'correct': is_correct,
            'pnl': pnl
        }

        results['trades'].append(trade_result)

        if is_correct:
            results['wins'] += 1
        else:
            results['losses'] += 1

        results['profit_loss'] += pnl

        # Print trade result
        print(f"Trade {i+1}: Predicted {prediction['direction']} with {prediction['confidence']:.2f} confidence, "
              f"Actual: {trade_result['actual_direction']}, "
              f"Result: {'WIN' if is_correct else 'LOSS'}, "
              f"P/L: {pnl}")

    # Calculate win rate
    total_completed_trades = len(results['trades'])
    results['win_rate'] = results['wins'] / total_completed_trades if total_completed_trades > 0 else 0.0

    # Print summary
    print(f"\nSimulation Summary:")
    print(f"Trades Attempted: {num_trades}")
    print(f"Trades Completed: {total_completed_trades}")
    print(f"Wins: {results['wins']}")
    print(f"Losses: {results['losses']}")
    print(f"Win Rate: {results['win_rate']:.2f}")
    print(f"Profit/Loss: {results['profit_loss']:.2f}")

    return results

def main():
    """Main function to trade with CSV data"""
    # Configuration
    csv_path = 'Models/usdars_otc_20250426_234310_candles.csv'
    model_dir = 'models'
    cache_dir = 'data_cache'
    num_trades = 10

    # Create directories
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)

    # Load and process data
    df = load_and_process_data(csv_path, cache_dir)

    # Print data info
    print(f"\nData shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")

    # Initialize model manager
    model_manager = ModelManager(model_dir=model_dir)

    # Load models
    models = model_manager.load_models()

    if 'xgboost' not in models:
        print("Error: XGBoost model not found. Please train the model first.")
        return

    # Simulate trading
    results = simulate_trading(df, model_manager, num_trades=num_trades)

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    results_path = os.path.join(model_dir, f"trading_simulation_{timestamp}.txt")

    with open(results_path, 'w') as f:
        f.write(f"Trading Simulation Results ({timestamp})\n")
        f.write(f"{'='*50}\n\n")
        f.write(f"Data: {csv_path}\n")
        f.write(f"Model: XGBoost\n\n")
        f.write(f"Summary:\n")
        f.write(f"Trades Attempted: {num_trades}\n")
        f.write(f"Trades Completed: {len(results['trades'])}\n")
        f.write(f"Wins: {results['wins']}\n")
        f.write(f"Losses: {results['losses']}\n")
        f.write(f"Win Rate: {results['win_rate']:.2f}\n")
        f.write(f"Profit/Loss: {results['profit_loss']:.2f}\n\n")
        f.write(f"Trade Details:\n")
        f.write(f"{'-'*50}\n")

        for i, trade in enumerate(results['trades']):
            f.write(f"Trade {i+1}:\n")
            f.write(f"  Timestamp: {trade['timestamp']}\n")
            f.write(f"  Prediction: {trade['direction']} (confidence: {trade['confidence']:.2f})\n")
            f.write(f"  Actual: {trade['actual_direction']}\n")
            f.write(f"  Result: {'WIN' if trade['correct'] else 'LOSS'}\n")
            f.write(f"  P/L: {trade['pnl']}\n\n")

    print(f"\nResults saved to {results_path}")

if __name__ == "__main__":
    main()
