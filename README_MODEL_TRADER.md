# Self-Learning Model-Based Auto Trader for Quotex

This auto trader uses machine learning models with self-learning capabilities to make trading decisions on the Quotex platform, specifically optimized for USDARS OTC trading.

## Features

- **Multiple ML Models**: Uses LSTM-GRU, Transformer, XGBoost, and DQN models for prediction
- **Ensemble Integration**: Combines predictions from all models for better accuracy
- **Confidence-Based Trading**: Only executes trades when confidence exceeds threshold
- **Performance Tracking**: Tracks and reports model performance
- **Customizable Parameters**: Allows setting trade amount, expiration time, and more
- **Self-Learning Capabilities**: Models learn from trading mistakes and improve over time
- **Adaptive Weighting**: Automatically adjusts model weights based on performance
- **Market Adaptation**: System adapts to changing market conditions

## Models

1. **LSTM-GRU Deep Learning Model**: Captures temporal patterns in price data
2. **Transformer Model**: Uses attention mechanisms for better feature extraction
3. **XGBoost Model**: Gradient boosting for robust prediction
4. **DQN Reinforcement Learning Agent**: Learns optimal trading strategies through experience
5. **Ensemble Model**: Combines all models with dynamic weighting

## Self-Learning System

The auto trader includes an advanced self-learning system that:

1. **Learns from Mistakes**: Records trade outcomes and adjusts models to avoid repeating errors
2. **Adapts to Market Changes**: Detects market condition changes and adjusts strategies
3. **Improves Accuracy**: Continuously improves prediction accuracy through experience
4. **Optimizes Model Weights**: Dynamically adjusts the influence of each model based on performance
5. **Provides Feedback**: Tracks and reports learning progress and improvements

## Setup

1. Ensure you have all required dependencies installed
2. Place trained model files in the `models` directory
3. Configure your Quotex credentials in `settings/config.ini`

## Usage

1. Run `python auto_trader.py`
2. Select account type (demo or real)
3. Choose which model to use
4. Set trading parameters
5. Configure self-learning capabilities
6. The auto trader will monitor candles and execute trades based on model predictions

## Self-Learning Configuration

The auto trader offers two self-learning modes:

1. **Active Learning Mode**:
   - Updates models immediately after each trade
   - Provides faster adaptation to market conditions
   - Applies stronger corrections after consecutive mistakes
   - Best for rapidly changing markets

2. **Batch Learning Mode**:
   - Updates models periodically in the background
   - More stable and less resource-intensive
   - Performs comprehensive model retraining
   - Best for longer-term trading

### Learning Parameters

You can customize the self-learning behavior with these parameters:

- **Learning Rate (0.1-0.5)**: How quickly models adapt to new information
- **Memory Retention (0.5-0.9)**: Balance between historical and recent data
- **Mistake Threshold (2-5)**: Number of consecutive mistakes before stronger corrections
- **Retraining Interval (hours)**: How often models are retrained in batch mode

## Model Files

The auto trader expects the following model files in the `models` directory:

- LSTM-GRU: `lstm_gru_*.h5` (model), `lstm_gru_*_scaler.pkl` (scaler), `lstm_gru_*_features.txt` (features)
- Transformer: `transformer_*.h5` (model), `transformer_*_scaler.pkl` (scaler), `transformer_*_features.txt` (features)
- XGBoost: `xgboost_model_*.pkl` (model), `xgboost_selected_features_*.txt` (features)
- DQN: `dqn_model_*.h5` (model), `dqn_model_*_features.txt` (features)

## Configuration

You can modify the following settings in `auto_trader.py`:

- `ASSET`: Trading asset (default: "USDARS_otc")
- `AMOUNT`: Default trade amount
- `EXPIRATION`: Default expiration time in seconds
- `MAX_TRADES`: Maximum number of trades to execute
- `MIN_CONFIDENCE`: Minimum confidence threshold for trading
- `MODEL_CONFIG`: Configuration for models (sequence lengths, window sizes, weights)

## Performance Tracking

The auto trader tracks and reports:

- Win/loss statistics for each model
- Overall trading performance
- Balance changes
- Confidence levels for each prediction

## Notes

- The auto trader requires at least 30 candles before making predictions
- Higher confidence thresholds result in fewer but potentially more accurate trades
- The ensemble model generally provides the most balanced performance

## How Self-Learning Works

The self-learning system works through several mechanisms:

### 1. Feedback Loop

After each trade, the system:
- Records the prediction, confidence, and actual outcome
- Analyzes market conditions at the time of the trade
- Compares model predictions with actual results
- Adjusts model weights based on performance

### 2. Mistake Pattern Recognition

The system identifies patterns in trading mistakes:
- Tracks consecutive incorrect predictions
- Identifies market conditions that lead to errors
- Applies stronger corrections when patterns emerge
- Reduces bias in problematic directions

### 3. Dynamic Model Weighting

Models that perform better receive more influence:
- Successful models get weight boosts
- Underperforming models get weight reductions
- Weights are normalized to maintain balance
- Performance is tracked across different market conditions

### 4. Continuous Improvement

The system gets smarter over time:
- Tracks overall accuracy and high-confidence accuracy
- Periodically retrains models with new data
- Optimizes hyperparameters for better performance
- Adapts to changing market conditions

### 5. Performance Reporting

The system provides detailed feedback:
- Reports accuracy improvements over time
- Shows current model weights and adjustments
- Tracks win rates for each model
- Provides insights into market adaptation
