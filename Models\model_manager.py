"""
Model Manager for Auto Trader

This module manages the loading, prediction, and ensemble integration of all trading models.
It provides a unified interface for making predictions using individual models or an ensemble.
"""

import os
import numpy as np
import pandas as pd
import joblib
import re
from datetime import datetime, timedelta
import time

# Check if TensorFlow is available
def check_tensorflow_availability():
    """Check if TensorFlow is available and working"""
    try:
        import tensorflow as tf
        # Test if Tensor<PERSON><PERSON> can create a simple model
        test_model = tf.keras.Sequential([tf.keras.layers.Dense(1)])
        print(f"✅ TensorFlow is available and working. Version: {tf.__version__}")
        return True
    except ImportError as e:
        print(f"❌ TensorFlow import error: {e}")
        return False
    except Exception as e:
        print(f"❌ TensorFlow test error: {e}")
        return False

# Initial check
TENSORFLOW_AVAILABLE = check_tensorflow_availability()

if not TENSORFLOW_AVAILABLE:
    print("⚠️ TensorFlow is not available. Only XGBoost model will be used.")
else:
    print("🚀 All models (XGBoost, LSTM, Transformer, DQN) will be available.")

# Import model modules
from Models.Feature_Engineering import engineer_features
from Models.XGBoost_Model import predict_with_xgboost

# Conditionally import TensorFlow-dependent modules
if TENSORFLOW_AVAILABLE:
    import tensorflow as tf  # Import TensorFlow globally when available
    from Models.LSTM_GRU_Deep_Learning_Model import predict_with_lstm_gru
    from Models.Transformer_Model import predict_with_transformer
    from Models.Reinforcement_Learning_Agent_with_DQN import DQNAgent, TradingEnvironment
    from Models.Ensemble_Model_Integration import EnsembleModel, create_ensemble
    from Models.Self_Learning_and_Continuous_Improvement import ModelTrainer
else:
    # Define placeholder classes for when TensorFlow is not available
    class ModelTrainer:
        def __init__(self, *args, **kwargs):
            pass

        def record_prediction_feedback(self, *args, **kwargs):
            return False

        def continuous_learning_loop(self, *args, **kwargs):
            pass

        def get_performance_summary(self):
            return {"overall_accuracy": 0.0}

    class EnsembleModel:
        def __init__(self, *args, **kwargs):
            self.model_types = []
            self.weights = []
            self.bias_correction = 0.0

        def add_model(self, *args, **kwargs):
            pass

        def predict(self, *args, **kwargs):
            return {
                'prediction': None,
                'probability': 0.5,
                'direction': "UNKNOWN",
                'confidence': 0.0,
                'error': "TensorFlow is not available, cannot use ensemble model"
            }

        def apply_success_boost(self, *args, **kwargs):
            pass

        def apply_mistake_penalty(self, *args, **kwargs):
            pass

    class DQNAgent:
        def __init__(self, *args, **kwargs):
            self.model = None

        def act(self, *args, **kwargs):
            return 0

        def update_target_model(self):
            pass

    class TradingEnvironment:
        def __init__(self, *args, **kwargs):
            pass

    def create_ensemble(*args, **kwargs):
        return EnsembleModel()

    # Define placeholder functions for TensorFlow-dependent modules
    def predict_with_lstm_gru(*args, **kwargs):
        return {
            'prediction': None,
            'probability': 0.5,
            'direction': "UNKNOWN",
            'confidence': 0.0,
            'error': "TensorFlow is not available, cannot use LSTM-GRU model"
        }

    def predict_with_transformer(*args, **kwargs):
        return {
            'prediction': None,
            'probability': 0.5,
            'direction': "UNKNOWN",
            'confidence': 0.0,
            'error': "TensorFlow is not available, cannot use Transformer model"
        }

# Add custom methods to EnsembleModel for self-learning
def add_self_learning_methods_to_ensemble():
    """Add self-learning methods to the EnsembleModel class if they don't exist"""
    if not TENSORFLOW_AVAILABLE:
        print("TensorFlow not available, skipping ensemble methods")
        return

    if not hasattr(EnsembleModel, 'apply_success_boost'):
        def apply_success_boost(self, model_name, boost_amount=0.05):
            """
            Apply a small boost to a model's weight after a successful prediction

            Parameters:
            - model_name: Name of the model to boost
            - boost_amount: Amount to boost the weight by (0-1)
            """
            if not hasattr(self, 'model_types'):
                print("Warning: Ensemble model doesn't have model_types attribute")
                return False

            if model_name in self.model_types:
                idx = self.model_types.index(model_name)

                # Increase weight for successful model
                self.weights[idx] += boost_amount

                # Normalize weights
                total_weight = sum(self.weights)
                self.weights = [w / total_weight for w in self.weights]

                print(f"Boosted {model_name} weight after successful prediction")
                return True
            return False

        EnsembleModel.apply_success_boost = apply_success_boost

    if not hasattr(EnsembleModel, 'apply_mistake_penalty'):
        def apply_mistake_penalty(self, model_name, penalty_amount=0.1):
            """
            Apply a penalty to a model's weight after an incorrect prediction

            Parameters:
            - model_name: Name of the model to penalize
            - penalty_amount: Amount to reduce the weight by (0-1)
            """
            if not hasattr(self, 'model_types'):
                print("Warning: Ensemble model doesn't have model_types attribute")
                return False

            if model_name in self.model_types:
                idx = self.model_types.index(model_name)

                # Decrease weight for mistaken model
                self.weights[idx] = max(0.1, self.weights[idx] - penalty_amount)  # Ensure weight doesn't go below 0.1

                # Normalize weights
                total_weight = sum(self.weights)
                self.weights = [w / total_weight for w in self.weights]

                print(f"Reduced {model_name} weight after incorrect prediction")
                return True
            return False

        EnsembleModel.apply_mistake_penalty = apply_mistake_penalty

    if not hasattr(EnsembleModel, 'bias_correction'):
        # Add bias_correction property
        EnsembleModel.bias_correction = 0.0

# Add self-learning methods to EnsembleModel if TensorFlow is available
if TENSORFLOW_AVAILABLE:
    add_self_learning_methods_to_ensemble()

# Set random seeds for reproducibility
np.random.seed(42)
if TENSORFLOW_AVAILABLE:
    import tensorflow as tf  # Import again for safety
    tf.random.set_seed(42)

class ModelManager:
    """
    Model Manager for trading models

    This class manages the loading, prediction, and ensemble integration of all trading models.
    """
    def __init__(self, model_dir='models', config=None):
        """
        Initialize the model manager

        Parameters:
        - model_dir: Directory containing model files
        - config: Configuration dictionary for models
        """
        self.model_dir = model_dir
        self.config = config or {}

        # Create model directory if it doesn't exist
        os.makedirs(model_dir, exist_ok=True)

        # Initialize models
        self.models = {}
        self.ensemble = None

        # Default model weights
        self.model_weights = {
            'lstm_gru': 0.3,
            'transformer': 0.3,
            'xgboost': 0.3,
            'dqn': 0.1
        }

        # Update weights from config if provided
        if 'model_weights' in self.config:
            self.model_weights.update(self.config['model_weights'])

        # Normalize weights
        total_weight = sum(self.model_weights.values())
        if total_weight > 0:
            for key in self.model_weights:
                self.model_weights[key] /= total_weight

        print(f"Model weights: {self.model_weights}")

        # Get TensorFlow configuration options
        self.compile_options = self.config.get('compile_options', {})
        self.tf_config = self.config.get('tf_config', {})

    def load_models(self, models_to_load=None):
        """
        Load models from disk

        Parameters:
        - models_to_load: List of model names to load (default: all)

        Returns:
        - loaded_models: Dictionary of loaded models
        """
        if not TENSORFLOW_AVAILABLE:
            # If TensorFlow is not available, only load XGBoost model
            print("TensorFlow not available, only loading XGBoost model")
            models_to_load = ['xgboost']
        elif models_to_load is None:
            models_to_load = ['lstm_gru', 'transformer', 'xgboost', 'dqn']

        print(f"Loading models: {models_to_load}")

        # Load LSTM-GRU model
        if TENSORFLOW_AVAILABLE and 'lstm_gru' in models_to_load:
            try:
                # First look for versioned LSTM-GRU model files
                versioned_lstm_files = [f for f in os.listdir(self.model_dir) if f.startswith('lstm_gru_v') and f.endswith('.h5')]

                # If versioned files exist, use the highest version
                if versioned_lstm_files:
                    # Extract version numbers and find the highest
                    version_pattern = re.compile(r'lstm_gru_v(\d+)\.h5')
                    version_files = [(f, int(version_pattern.match(f).group(1)) if version_pattern.match(f) else 0)
                                    for f in versioned_lstm_files]
                    latest_lstm = max(version_files, key=lambda x: x[1])[0]
                    lstm_path = os.path.join(self.model_dir, latest_lstm)
                    print(f"Found versioned LSTM-GRU model: {latest_lstm}")
                else:
                    # Fall back to timestamp-based files if no versioned files exist
                    lstm_files = [f for f in os.listdir(self.model_dir) if f.startswith('lstm_gru_') and f.endswith('.h5')]
                    if lstm_files:
                        latest_lstm = max(lstm_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                        lstm_path = os.path.join(self.model_dir, latest_lstm)
                        print(f"Using timestamp-based LSTM-GRU model: {latest_lstm}")

                # Load model and scaler with performance optimizations
                # Apply TensorFlow configuration for optimal performance
                strategy = self.tf_config.get('strategy')

                try:
                    if strategy:
                        print(f"Using distribution strategy for LSTM-GRU model loading")
                        with strategy.scope():
                            lstm_model = tf.keras.models.load_model(lstm_path, compile=False)
                    else:
                        lstm_model = tf.keras.models.load_model(lstm_path, compile=False)
                except Exception as load_error:
                    print(f"Error loading LSTM-GRU model: {load_error}")
                    print("Creating simplified LSTM-GRU model as fallback...")

                    # Create a simplified LSTM model as fallback with fixed input shape
                    # Use 30 timesteps and 20 features as default (will be adjusted later if needed)
                    try:
                        lstm_model = tf.keras.Sequential([
                            tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(30, 20)),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.LSTM(32),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(16, activation='relu'),
                            tf.keras.layers.Dense(1, activation='sigmoid')
                        ])
                        print("Created simplified LSTM-GRU model with shape (30, 20)")
                    except Exception as fallback_error:
                        print(f"Error creating LSTM fallback: {fallback_error}")
                        # Create an even simpler model
                        lstm_model = tf.keras.Sequential([
                            tf.keras.layers.Dense(64, activation='relu', input_shape=(20,)),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(32, activation='relu'),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(1, activation='sigmoid')
                        ])
                        print("Created simple dense LSTM fallback model")

                # Apply XLA compilation if enabled
                if self.compile_options.get('jit_compile'):
                    print("Applying XLA compilation to LSTM-GRU model")
                    lstm_model.compile(
                        optimizer='adam',
                        loss='binary_crossentropy',
                        metrics=['accuracy'],
                        jit_compile=True
                    )
                scaler_path = os.path.join(self.model_dir, latest_lstm.replace('.h5', '_scaler.pkl'))

                if os.path.exists(scaler_path):
                    lstm_scaler = joblib.load(scaler_path)
                else:
                    print(f"Warning: LSTM-GRU scaler not found at {scaler_path}")
                    lstm_scaler = None

                # Get feature columns
                feature_file = os.path.join(self.model_dir, latest_lstm.replace('.h5', '_features.txt'))
                if os.path.exists(feature_file):
                    with open(feature_file, 'r') as f:
                        lstm_features = [line.strip() for line in f.readlines()]
                else:
                    print(f"Warning: LSTM-GRU features not found at {feature_file}")
                    lstm_features = None

                self.models['lstm_gru'] = {
                    'model': lstm_model,
                    'scaler': lstm_scaler,
                    'features': lstm_features,
                    'seq_length': self.config.get('lstm_seq_length', 30)
                }
                print(f"Loaded LSTM-GRU model from {lstm_path}")
            except Exception as e:
                print(f"Error loading LSTM-GRU model: {e}")
            else:
                if 'lstm_model' not in locals():
                    print("No LSTM-GRU model found")

        # Load Transformer model
        if TENSORFLOW_AVAILABLE and 'transformer' in models_to_load:
            try:
                # First look for versioned Transformer model files
                versioned_transformer_files = [f for f in os.listdir(self.model_dir) if f.startswith('transformer_v') and f.endswith('.h5')]

                # If versioned files exist, use the highest version
                if versioned_transformer_files:
                    # Extract version numbers and find the highest
                    version_pattern = re.compile(r'transformer_v(\d+)\.h5')
                    version_files = [(f, int(version_pattern.match(f).group(1)) if version_pattern.match(f) else 0)
                                    for f in versioned_transformer_files]
                    latest_transformer = max(version_files, key=lambda x: x[1])[0]
                    transformer_path = os.path.join(self.model_dir, latest_transformer)
                    print(f"Found versioned Transformer model: {latest_transformer}")
                else:
                    # Fall back to timestamp-based files if no versioned files exist
                    transformer_files = [f for f in os.listdir(self.model_dir) if
                                        (f.startswith('transformer_') or 'transformer' in f.lower()) and
                                        f.endswith('.h5')]
                    if transformer_files:
                        latest_transformer = max(transformer_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                        transformer_path = os.path.join(self.model_dir, latest_transformer)
                        print(f"Using timestamp-based Transformer model: {latest_transformer}")

                # Load model and scaler with performance optimizations
                # Apply TensorFlow configuration for optimal performance
                strategy = self.tf_config.get('strategy')

                try:
                    if strategy:
                        print(f"Using distribution strategy for Transformer model loading")
                        with strategy.scope():
                            transformer_model = tf.keras.models.load_model(transformer_path, compile=False)
                    else:
                        transformer_model = tf.keras.models.load_model(transformer_path, compile=False)
                except Exception as load_error:
                    print(f"Error loading Transformer model: {load_error}")
                    print("Creating simplified Transformer model as fallback...")

                    # Create a simplified Transformer-like model as fallback with fixed input shape
                    try:
                        transformer_model = tf.keras.Sequential([
                            tf.keras.layers.Dense(128, activation='relu', input_shape=(30, 20)),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.LSTM(64, return_sequences=True),
                            tf.keras.layers.GlobalAveragePooling1D(),
                            tf.keras.layers.Dense(32, activation='relu'),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(1, activation='sigmoid')
                        ])
                        print("Created simplified Transformer model with shape (30, 20)")
                    except Exception as fallback_error:
                        print(f"Error creating Transformer fallback: {fallback_error}")
                        # Create an even simpler model
                        transformer_model = tf.keras.Sequential([
                            tf.keras.layers.Dense(128, activation='relu', input_shape=(20,)),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(64, activation='relu'),
                            tf.keras.layers.Dropout(0.2),
                            tf.keras.layers.Dense(32, activation='relu'),
                            tf.keras.layers.Dense(1, activation='sigmoid')
                        ])
                        print("Created simple dense Transformer fallback model")

                # Apply XLA compilation if enabled
                if self.compile_options.get('jit_compile'):
                    print("Applying XLA compilation to Transformer model")
                    transformer_model.compile(
                        optimizer='adam',
                        loss='binary_crossentropy',
                        metrics=['accuracy'],
                        jit_compile=True
                    )
                scaler_path = os.path.join(self.model_dir, latest_transformer.replace('.h5', '_scaler.pkl'))

                if os.path.exists(scaler_path):
                    transformer_scaler = joblib.load(scaler_path)
                else:
                    print(f"Warning: Transformer scaler not found at {scaler_path}")
                    transformer_scaler = None

                # Get feature columns
                feature_file = os.path.join(self.model_dir, latest_transformer.replace('.h5', '_features.txt'))
                if os.path.exists(feature_file):
                    with open(feature_file, 'r') as f:
                        transformer_features = [line.strip() for line in f.readlines()]
                else:
                    print(f"Warning: Transformer features not found at {feature_file}")
                    transformer_features = None

                self.models['transformer'] = {
                    'model': transformer_model,
                    'scaler': transformer_scaler,
                    'features': transformer_features,
                    'seq_length': self.config.get('transformer_seq_length', 30),
                    'model_type': self.config.get('transformer_type', 'standard')
                }
                print(f"Loaded Transformer model from {transformer_path}")
            except Exception as e:
                print(f"Error loading Transformer model: {e}")
            else:
                if 'transformer_model' not in locals():
                    print("No Transformer model found")

        # Load XGBoost model
        if 'xgboost' in models_to_load:
            try:
                # First look for versioned XGBoost model files
                versioned_xgb_files = [f for f in os.listdir(self.model_dir) if f.startswith('xgboost_v') and f.endswith('.joblib')]

                # If versioned files exist, use the highest version
                if versioned_xgb_files:
                    # Extract version numbers and find the highest
                    version_pattern = re.compile(r'xgboost_v(\d+)\.joblib')
                    version_files = [(f, int(version_pattern.match(f).group(1)) if version_pattern.match(f) else 0)
                                    for f in versioned_xgb_files]
                    latest_xgb = max(version_files, key=lambda x: x[1])[0]
                    xgb_path = os.path.join(self.model_dir, latest_xgb)
                    print(f"Found versioned XGBoost model: {latest_xgb}")
                else:
                    # Fall back to timestamp-based files if no versioned files exist
                    xgb_files = [f for f in os.listdir(self.model_dir) if f.startswith('xgboost_model_') and f.endswith('.pkl')]
                    if xgb_files:
                        latest_xgb = max(xgb_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                        xgb_path = os.path.join(self.model_dir, latest_xgb)
                        print(f"Using timestamp-based XGBoost model: {latest_xgb}")

                # Load model
                xgb_model = joblib.load(xgb_path)

                # Get feature columns
                # For versioned models, look for feature file with _features.txt suffix
                if 'versioned_xgb_files' in locals() and versioned_xgb_files:
                    feature_file = os.path.join(self.model_dir, latest_xgb.replace('.joblib', '_features.txt'))
                else:
                    feature_file = os.path.join(self.model_dir, latest_xgb.replace('model_', 'selected_features_'))

                if os.path.exists(feature_file):
                    with open(feature_file, 'r') as f:
                        xgb_features = [line.strip() for line in f.readlines()]
                    print(f"Loaded {len(xgb_features)} features from {feature_file}")
                else:
                    print(f"Warning: XGBoost features not found at {feature_file}")
                    # Try to extract features from the model
                    try:
                        model_features = xgb_model.get_booster().feature_names
                        if model_features:
                            xgb_features = model_features
                            print(f"Extracted {len(xgb_features)} features from model")
                        else:
                            # Use default features
                            xgb_features = ['close', 'open', 'high', 'low', 'volume',
                                           'sma_5', 'sma_20', 'ema_5', 'ema_20',
                                           'rsi_14', 'macd', 'macd_signal', 'macd_hist']
                            print(f"Using {len(xgb_features)} default features")
                    except Exception as e:
                        print(f"Error extracting features from model: {e}")
                        # Use default features
                        xgb_features = ['close', 'open', 'high', 'low', 'volume',
                                       'sma_5', 'sma_20', 'ema_5', 'ema_20',
                                       'rsi_14', 'macd', 'macd_signal', 'macd_hist']
                        print(f"Using {len(xgb_features)} default features after error")

                self.models['xgboost'] = {
                    'model': xgb_model,
                    'features': xgb_features
                }
                print(f"Loaded XGBoost model from {xgb_path}")
            except Exception as e:
                print(f"Error loading XGBoost model: {e}")
            else:
                if 'xgb_model' not in locals():
                    # Try to find any XGBoost model file (not just starting with xgboost_model_)
                    all_pkl_files = [f for f in os.listdir(self.model_dir) if f.endswith('.pkl')]
                    if all_pkl_files:
                        latest_pkl = max(all_pkl_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                        pkl_path = os.path.join(self.model_dir, latest_pkl)

                        # Load model
                        xgb_model = joblib.load(pkl_path)

                        # Get feature columns - try to find any features file
                        feature_files = [f for f in os.listdir(self.model_dir) if 'features' in f.lower() and f.endswith('.txt')]
                        if feature_files:
                            latest_feature = max(feature_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                            feature_path = os.path.join(self.model_dir, latest_feature)

                            with open(feature_path, 'r') as f:
                                xgb_features = [line.strip() for line in f.readlines()]
                        else:
                            print(f"Warning: No feature file found, trying to extract features from model")
                            # Try to extract features from the model
                            try:
                                model_features = xgb_model.get_booster().feature_names
                                if model_features:
                                    xgb_features = model_features
                                    print(f"Extracted {len(xgb_features)} features from model")
                                else:
                                    # Use default features
                                    xgb_features = [
                                        'close', 'open', 'high', 'low', 'volume',
                                        'returns', 'sma_5', 'sma_20', 'ema_5', 'ema_20',
                                        'rsi_14', 'macd', 'macd_signal', 'macd_hist',
                                        'stoch_k_14', 'adx_14', 'cci_20', 'atr_14', 'bollinger_pct_20'
                                    ]
                                    print(f"Using {len(xgb_features)} default features")
                            except Exception as e:
                                print(f"Error extracting features from model: {e}")
                                # Use default features
                                xgb_features = [
                                    'close', 'open', 'high', 'low', 'volume',
                                    'returns', 'sma_5', 'sma_20', 'rsi_14', 'macd',
                                    'stoch_k_14', 'adx_14', 'cci_20', 'atr_14', 'bollinger_pct_20'
                                ]
                                print(f"Using {len(xgb_features)} default features after error")

                        self.models['xgboost'] = {
                            'model': xgb_model,
                            'features': xgb_features
                        }
                        print(f"Loaded XGBoost model from {pkl_path}")
                    else:
                        print("No XGBoost model found")

        # Load DQN model
        if TENSORFLOW_AVAILABLE and 'dqn' in models_to_load:
            try:
                # Find the latest DQN model file
                dqn_files = [f for f in os.listdir(self.model_dir) if f.startswith('dqn_model_') and f.endswith('.h5')]
                if dqn_files:
                    latest_dqn = max(dqn_files, key=lambda x: os.path.getmtime(os.path.join(self.model_dir, x)))
                    dqn_path = os.path.join(self.model_dir, latest_dqn)

                    # Try to load model with different approaches
                    try:
                        print(f"Attempting to load DQN model from {dqn_path}")

                        # Apply TensorFlow configuration for optimal performance
                        # Use a strategy scope if available
                        strategy = self.tf_config.get('strategy')

                        if strategy:
                            print(f"Using distribution strategy for model loading")
                            with strategy.scope():
                                # First try loading with compile=False to avoid issues with custom losses
                                dqn_model = tf.keras.models.load_model(dqn_path, compile=False)
                        else:
                            # First try loading with compile=False to avoid issues with custom losses
                            dqn_model = tf.keras.models.load_model(dqn_path, compile=False)

                        print("Successfully loaded DQN model with compile=False")

                        # Apply XLA compilation if enabled
                        if self.compile_options.get('jit_compile'):
                            print("Applying XLA compilation to DQN model")
                            dqn_model.compile(
                                optimizer='adam',
                                loss='mean_squared_error',
                                jit_compile=True
                            )
                    except Exception as e1:
                        print(f"Error loading DQN model with compile=False: {e1}")
                        try:
                            # Try loading with custom_objects
                            custom_objects = {
                                'mse': tf.keras.losses.MeanSquaredError(),
                                'mean_squared_error': tf.keras.losses.MeanSquaredError()
                            }

                            if strategy:
                                with strategy.scope():
                                    dqn_model = tf.keras.models.load_model(dqn_path, custom_objects=custom_objects, compile=False)
                            else:
                                dqn_model = tf.keras.models.load_model(dqn_path, custom_objects=custom_objects, compile=False)

                            print("Successfully loaded DQN model with custom_objects")
                        except Exception as e2:
                            print(f"Error loading DQN model with custom_objects: {e2}")

                            # Check if there's a .keras version of the model
                            keras_path = dqn_path.replace('.h5', '.keras')
                            if os.path.exists(keras_path):
                                try:
                                    print(f"Attempting to load DQN model from {keras_path}")

                                    if strategy:
                                        with strategy.scope():
                                            dqn_model = tf.keras.models.load_model(keras_path, compile=False)
                                    else:
                                        dqn_model = tf.keras.models.load_model(keras_path, compile=False)

                                    print("Successfully loaded DQN model from .keras file")
                                except Exception as e3:
                                    print(f"Error loading DQN model from .keras file: {e3}")

                                    # Create a simplified DQN model as fallback
                                    print("Creating simplified DQN model as fallback...")

                                    # Market features input
                                    market_input = tf.keras.layers.Input(shape=(30, 10), name='market_features')
                                    x = tf.keras.layers.Conv1D(32, 3, activation='relu', padding='same')(market_input)
                                    x = tf.keras.layers.BatchNormalization()(x)
                                    x = tf.keras.layers.MaxPooling1D(2)(x)
                                    x = tf.keras.layers.LSTM(64, return_sequences=False)(x)  # This outputs (None, 64)
                                    market_features = x  # No need for GlobalAveragePooling1D since LSTM already outputs 2D

                                    # Portfolio state input
                                    portfolio_input = tf.keras.layers.Input(shape=(3,), name='portfolio_state')
                                    portfolio_features = tf.keras.layers.Dense(16, activation='relu')(portfolio_input)

                                    # Combine features
                                    combined = tf.keras.layers.Concatenate()([market_features, portfolio_features])
                                    x = tf.keras.layers.Dense(64, activation='relu')(combined)
                                    x = tf.keras.layers.Dropout(0.2)(x)
                                    output = tf.keras.layers.Dense(2, activation='linear')(x)

                                    dqn_model = tf.keras.Model(inputs=[market_input, portfolio_input], outputs=output)
                                    print("Created simplified DQN model with correct shapes")
                            else:
                                # Create a simplified DQN model as fallback
                                print("Creating simplified DQN model as fallback...")

                                # Market features input
                                market_input = tf.keras.layers.Input(shape=(30, 10), name='market_features')
                                x = tf.keras.layers.Conv1D(32, 3, activation='relu', padding='same')(market_input)
                                x = tf.keras.layers.BatchNormalization()(x)
                                x = tf.keras.layers.MaxPooling1D(2)(x)
                                x = tf.keras.layers.LSTM(64, return_sequences=False)(x)  # This outputs (None, 64)
                                market_features = x  # No need for GlobalAveragePooling1D since LSTM already outputs 2D

                                # Portfolio state input
                                portfolio_input = tf.keras.layers.Input(shape=(3,), name='portfolio_state')
                                portfolio_features = tf.keras.layers.Dense(16, activation='relu')(portfolio_input)

                                # Combine features
                                combined = tf.keras.layers.Concatenate()([market_features, portfolio_features])
                                x = tf.keras.layers.Dense(64, activation='relu')(combined)
                                x = tf.keras.layers.Dropout(0.2)(x)
                                output = tf.keras.layers.Dense(2, activation='linear')(x)

                                dqn_model = tf.keras.Model(inputs=[market_input, portfolio_input], outputs=output)
                                print("Created simplified DQN model with correct shapes")

                    # Get feature columns
                    feature_file = os.path.join(self.model_dir, latest_dqn.replace('.h5', '_features.txt'))
                    if os.path.exists(feature_file):
                        with open(feature_file, 'r') as f:
                            dqn_features = [line.strip() for line in f.readlines()]
                    else:
                        print(f"Warning: DQN features not found at {feature_file}")
                        dqn_features = self.config.get('dqn_features', [
                            'close', 'returns', 'sma_5', 'sma_20', 'rsi_14', 'macd',
                            'stoch_k_14', 'adx_14', 'cci_20', 'atr_14', 'bollinger_pct_20'
                        ])

                    # Create DQN agent
                    state_shape = {
                        'market_features': (self.config.get('dqn_window_size', 30), len(dqn_features)),
                        'portfolio_state': 3  # balance, consecutive_losses, max_drawdown
                    }

                    try:
                        # Try to create DQN agent and set weights
                        dqn_agent = DQNAgent(
                            state_shape=state_shape,
                            model_type=self.config.get('dqn_model_type', 'advanced')
                        )

                        # Set model weights
                        dqn_agent.model.set_weights(dqn_model.get_weights())
                        dqn_agent.update_target_model()
                        print(f"Successfully initialized DQN agent with weights from {dqn_path}")
                    except Exception as e:
                        print(f"Error initializing DQN agent: {e}")
                        print("Creating a simplified DQN agent using the loaded model directly")

                        # Create a simplified DQN agent that uses the loaded model directly
                        class SimplifiedDQNAgent:
                            def __init__(self, model):
                                self.model = model
                                self.target_model = model  # Use same model for both

                            def act(self, state, eval_mode=False):
                                # Process state
                                market_features = np.expand_dims(state['market_features'], axis=0)
                                portfolio_state = np.expand_dims(state['portfolio_state'], axis=0)

                                # Get Q-values
                                q_values = self.model.predict([market_features, portfolio_state], verbose=0)[0]

                                # Return action with highest Q-value
                                return np.argmax(q_values)

                            def update_target_model(self):
                                # No need to update since we're using the same model
                                pass

                        # Create the simplified agent
                        dqn_agent = SimplifiedDQNAgent(dqn_model)
                        print("Created simplified DQN agent with the loaded model")

                    self.models['dqn'] = {
                        'agent': dqn_agent,
                        'features': dqn_features,
                        'window_size': self.config.get('dqn_window_size', 30)
                    }
                    print(f"Loaded DQN model from {dqn_path}")
                else:
                    print("No DQN model found")
            except Exception as e:
                print(f"Error loading DQN model: {e}")

        # Create ensemble if multiple models are loaded and TensorFlow is available
        if TENSORFLOW_AVAILABLE and len(self.models) > 1:
            self.create_ensemble()

        return self.models

    def check_models_exist(self):
        """
        Check if trained models exist in the model directory

        Returns:
        - exists: Boolean indicating if models exist
        """
        # Check if model directory exists
        if not os.path.exists(self.model_dir):
            return False

        # Check for XGBoost model (always required)
        xgb_files = [f for f in os.listdir(self.model_dir) if f.endswith('.pkl') and ('xgboost' in f.lower() or 'model' in f.lower())]

        # If TensorFlow is available, also check for deep learning models
        if TENSORFLOW_AVAILABLE:
            h5_files = [f for f in os.listdir(self.model_dir) if f.endswith('.h5')]
            # Need at least one XGBoost model and one deep learning model
            return len(xgb_files) > 0 and len(h5_files) > 0
        else:
            # If TensorFlow is not available, only need XGBoost model
            return len(xgb_files) > 0

    def train_models(self, data):
        """
        Train models using provided data

        Parameters:
        - data: DataFrame with features for training

        Returns:
        - models: Dictionary of trained models
        """
        print("Training models...")

        # Ensure model directory exists
        os.makedirs(self.model_dir, exist_ok=True)

        # Train XGBoost model
        try:
            from Models.XGBoost_Model import train_xgboost_model

            print("Training XGBoost model...")
            # Get feature columns (exclude target and timestamp)
            feature_columns = [col for col in data.columns
                              if col not in ['target_1min', 'target_5min', 'timestamp']]

            # Train XGBoost model
            xgb_model, xgb_feature_importance, xgb_evaluation, xgb_selected_features = train_xgboost_model(
                data,
                feature_columns,
                optimize_hyperparams=True,
                feature_selection=True
            )

            # Save model
            model_path = os.path.join(self.model_dir, f"xgboost_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl")
            joblib.dump(xgb_model, model_path)

            # Save selected features
            feature_path = os.path.join(self.model_dir, f"xgboost_selected_features_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            with open(feature_path, 'w') as f:
                for feature in xgb_selected_features:
                    f.write(f"{feature}\n")

            # Store model in memory
            self.models['xgboost'] = {
                'model': xgb_model,
                'features': xgb_selected_features,
                'feature_importance': xgb_feature_importance,
                'evaluation': xgb_evaluation
            }

            print(f"XGBoost model trained and saved to {model_path}")

            # If TensorFlow is available, train deep learning models
            if TENSORFLOW_AVAILABLE:
                try:
                    from Models.LSTM_GRU_Deep_Learning_Model import train_lstm_gru_model

                    print("Training LSTM-GRU model...")
                    lstm_model, lstm_history, lstm_evaluation = train_lstm_gru_model(
                        data,
                        xgb_selected_features,  # Use features selected by XGBoost
                        model_type='advanced'
                    )

                    # Save model
                    model_path = os.path.join(self.model_dir, f"lstm_gru_{datetime.now().strftime('%Y%m%d_%H%M%S')}.h5")
                    lstm_model.save(model_path)

                    # Store model in memory
                    self.models['lstm_gru'] = {
                        'model': lstm_model,
                        'features': xgb_selected_features,
                        'history': lstm_history.history if lstm_history else None,
                        'evaluation': lstm_evaluation
                    }

                    print(f"LSTM-GRU model trained and saved to {model_path}")
                except Exception as e:
                    print(f"Error training LSTM-GRU model: {e}")

            # Create ensemble if multiple models are available
            if TENSORFLOW_AVAILABLE and len(self.models) > 1:
                self.create_ensemble()

            return self.models
        except Exception as e:
            print(f"Error training models: {e}")
            return None

    def rebuild_models_for_features(self, n_features):
        """
        Rebuild LSTM and Transformer models with the correct number of features

        Parameters:
        - n_features: Number of features in the current dataset
        """
        if not TENSORFLOW_AVAILABLE:
            return

        print(f"Rebuilding models for {n_features} features...")

        # Rebuild LSTM model if it exists
        if 'lstm_gru' in self.models:
            try:
                print("Rebuilding LSTM-GRU model...")
                lstm_model = tf.keras.Sequential([
                    tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(30, n_features)),
                    tf.keras.layers.Dropout(0.2),
                    tf.keras.layers.LSTM(32),
                    tf.keras.layers.Dropout(0.2),
                    tf.keras.layers.Dense(16, activation='relu'),
                    tf.keras.layers.Dense(1, activation='sigmoid')
                ])
                self.models['lstm_gru']['model'] = lstm_model
                print(f"✅ Rebuilt LSTM-GRU model for {n_features} features")
            except Exception as e:
                print(f"Error rebuilding LSTM model: {e}")

        # Rebuild Transformer model if it exists
        if 'transformer' in self.models:
            try:
                print("Rebuilding Transformer model...")
                transformer_model = tf.keras.Sequential([
                    tf.keras.layers.Dense(128, activation='relu', input_shape=(30, n_features)),
                    tf.keras.layers.Dropout(0.2),
                    tf.keras.layers.LSTM(64, return_sequences=True),
                    tf.keras.layers.GlobalAveragePooling1D(),
                    tf.keras.layers.Dense(32, activation='relu'),
                    tf.keras.layers.Dropout(0.2),
                    tf.keras.layers.Dense(1, activation='sigmoid')
                ])
                self.models['transformer']['model'] = transformer_model
                print(f"✅ Rebuilt Transformer model for {n_features} features")
            except Exception as e:
                print(f"Error rebuilding Transformer model: {e}")

    def create_ensemble(self):
        """
        Create an ensemble model from loaded models

        Returns:
        - ensemble: Configured ensemble model
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping ensemble creation")
            return None

        print("Creating ensemble model...")

        # Extract models for ensemble
        lstm_model = self.models.get('lstm_gru', {}).get('model')
        transformer_model = self.models.get('transformer', {}).get('model')
        xgb_model = self.models.get('xgboost', {}).get('model')
        dqn_agent = self.models.get('dqn', {}).get('agent')

        # Create ensemble with available models
        # Only include DQN agent if it's available
        if dqn_agent is not None:
            dqn_weight = self.model_weights.get('dqn', 0.1)
        else:
            dqn_weight = 0
            dqn_agent = None

        self.ensemble = create_ensemble(
            lstm_model=lstm_model,
            transformer_model=transformer_model,
            xgb_model=xgb_model,
            dqn_agent=dqn_agent,
            lstm_weight=self.model_weights.get('lstm_gru', 0.3),
            transformer_weight=self.model_weights.get('transformer', 0.3),
            xgb_weight=self.model_weights.get('xgboost', 0.3),
            dqn_weight=dqn_weight,
            window_size=self.config.get('ensemble_window_size', 10)
        )

        # Add transformer model if available
        if transformer_model is not None:
            self.ensemble.add_model(
                transformer_model,
                'transformer',
                weight=self.model_weights.get('transformer', 0.3)
            )

        print("Ensemble model created")
        return self.ensemble

    def prepare_data(self, candles, additional_features=None):
        """
        Prepare data for model predictions

        Parameters:
        - candles: List of candle data
        - additional_features: Additional features to include

        Returns:
        - df: DataFrame with engineered features
        """
        # Convert candles to DataFrame
        df = pd.DataFrame(candles)

        # Add timestamp column if not present
        if 'timestamp' not in df.columns:
            # Convert time to datetime objects
            if 'time' in df.columns:
                # Ensure time values are numeric
                df['time'] = pd.to_numeric(df['time'], errors='coerce')
                try:
                    # Try to convert timestamps to datetime objects
                    df['timestamp'] = pd.to_datetime([datetime.fromtimestamp(t) for t in df['time']])
                except Exception as e:
                    print(f"Error converting timestamps: {e}")
                    # Create a default timestamp if conversion fails
                    start_time = datetime.now() - timedelta(minutes=len(df))
                    df['timestamp'] = pd.date_range(start=start_time, periods=len(df), freq='1min')
            else:
                # Create a default timestamp if time column is missing
                start_time = datetime.now() - timedelta(minutes=len(df))
                df['timestamp'] = pd.date_range(start=start_time, periods=len(df), freq='1min')

        # Set timestamp as index and ensure it's a DatetimeIndex
        df = df.set_index('timestamp')

        # Verify that the index is a DatetimeIndex
        if not isinstance(df.index, pd.DatetimeIndex):
            print("Warning: Index is not a DatetimeIndex after setting 'timestamp' as index.")
            try:
                # Try to convert the index to DatetimeIndex
                df.index = pd.to_datetime(df.index)
                print("Successfully converted index to DatetimeIndex.")
            except Exception as e:
                print(f"Error converting index to DatetimeIndex: {e}")
                # Create a new index as a fallback
                start_time = datetime.now() - timedelta(minutes=len(df))
                df.index = pd.date_range(start=start_time, periods=len(df), freq='1min')
                print("Created new DatetimeIndex as fallback.")

        # Add additional features if provided
        if additional_features is not None:
            for key, value in additional_features.items():
                df[key] = value

        # Engineer features
        df = engineer_features(df)

        return df

    def predict(self, candles, additional_features=None, model_name=None):
        """
        Make predictions using loaded models

        Parameters:
        - candles: List of candle data
        - additional_features: Additional features to include
        - model_name: Name of model to use (default: ensemble if available, otherwise first available model)

        Returns:
        - prediction: Dictionary containing prediction results
        """
        # Prepare data
        df = self.prepare_data(candles, additional_features)

        # Check if this is a future prediction
        is_future_prediction = False
        prediction_horizon = 1  # Default horizon (1 minute)

        if additional_features is not None:
            is_future_prediction = additional_features.get('is_future_prediction', False)
            prediction_horizon = additional_features.get('prediction_horizon', 1)

        # If this is a future prediction, add future prediction features
        if is_future_prediction:
            df = self.add_future_prediction_features(df, prediction_horizon)

        # If TensorFlow is not available, force using XGBoost model
        if not TENSORFLOW_AVAILABLE:
            if 'xgboost' in self.models:
                model_name = 'xgboost'
            else:
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': "XGBoost model not available and TensorFlow is not installed"
                }
        # If no specific model is requested, use ensemble if available
        elif model_name is None:
            if self.ensemble is not None:
                model_name = 'ensemble'
            elif len(self.models) > 0:
                model_name = list(self.models.keys())[0]
            else:
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': "No models available"
                }

        # Make prediction with specified model
        if model_name == 'ensemble' and TENSORFLOW_AVAILABLE:
            return self.predict_with_ensemble(df, is_future=is_future_prediction, horizon=prediction_horizon)
        elif model_name == 'lstm_gru' and TENSORFLOW_AVAILABLE and 'lstm_gru' in self.models:
            return self.predict_with_lstm_gru(df, is_future=is_future_prediction, horizon=prediction_horizon)
        elif model_name == 'transformer' and TENSORFLOW_AVAILABLE and 'transformer' in self.models:
            return self.predict_with_transformer(df, is_future=is_future_prediction, horizon=prediction_horizon)
        elif model_name == 'xgboost' and 'xgboost' in self.models:
            return self.predict_with_xgboost(df, is_future=is_future_prediction, horizon=prediction_horizon)
        elif model_name == 'dqn' and TENSORFLOW_AVAILABLE and 'dqn' in self.models:
            return self.predict_with_dqn(df, is_future=is_future_prediction, horizon=prediction_horizon)
        else:
            if not TENSORFLOW_AVAILABLE and model_name != 'xgboost':
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': f"Model {model_name} requires TensorFlow which is not installed"
                }
            else:
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': f"Model {model_name} not available"
                }

    def add_future_prediction_features(self, df, horizon=1):
        """
        Add features specific to future candle prediction

        Parameters:
        - df: DataFrame with engineered features
        - horizon: Prediction horizon in minutes

        Returns:
        - df: DataFrame with added future prediction features
        """
        # Add horizon as a feature
        df['prediction_horizon'] = horizon

        # Add time-based features
        if 'timestamp' in df.index.names and isinstance(df.index, pd.DatetimeIndex):
            # Extract hour and minute
            df['hour'] = df.index.hour
            df['minute'] = df.index.minute

            # Add cyclical time features
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['minute_sin'] = np.sin(2 * np.pi * df['minute'] / 60)
            df['minute_cos'] = np.cos(2 * np.pi * df['minute'] / 60)
        elif 'timestamp' in df.index.names:
            # Index has 'timestamp' but is not a DatetimeIndex
            print("Warning: Index has 'timestamp' but is not a DatetimeIndex. Converting index to DatetimeIndex.")
            try:
                # Try to convert the index to DatetimeIndex
                df.index = pd.to_datetime(df.index)

                # Now extract hour and minute
                df['hour'] = df.index.hour
                df['minute'] = df.index.minute

                # Add cyclical time features
                df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
                df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
                df['minute_sin'] = np.sin(2 * np.pi * df['minute'] / 60)
                df['minute_cos'] = np.cos(2 * np.pi * df['minute'] / 60)
            except Exception as e:
                print(f"Error converting index to DatetimeIndex: {e}")
                # Create hour and minute features with default values
                df['hour'] = 0
                df['minute'] = 0
                df['hour_sin'] = 0
                df['hour_cos'] = 1  # cos(0) = 1
                df['minute_sin'] = 0
                df['minute_cos'] = 1  # cos(0) = 1

        # Add momentum features based on horizon
        if horizon > 1:
            # Calculate momentum over different periods
            for period in [3, 5, 10, 15]:
                if len(df) > period:
                    # Price momentum
                    df[f'price_momentum_{period}'] = df['close'].pct_change(period)

                    # Volume momentum if available
                    if 'volume' in df.columns:
                        df[f'volume_momentum_{period}'] = df['volume'].pct_change(period)

                    # Volatility momentum
                    if 'high' in df.columns and 'low' in df.columns:
                        df[f'volatility_momentum_{period}'] = ((df['high'] - df['low']) / df['close']).pct_change(period)

        # Add trend strength features
        if 'sma_5' in df.columns and 'sma_20' in df.columns:
            df['trend_strength'] = (df['sma_5'] - df['sma_20']) / df['sma_20'] * 100

        # Add volatility features
        if 'high' in df.columns and 'low' in df.columns:
            # Calculate true range
            df['true_range'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    np.abs(df['high'] - df['close'].shift(1)),
                    np.abs(df['low'] - df['close'].shift(1))
                )
            )

            # Calculate average true range for different periods
            for period in [5, 10, 20]:
                if len(df) > period:
                    df[f'atr_{period}'] = df['true_range'].rolling(period).mean()

            # Add normalized ATR
            if 'atr_10' in df.columns:
                df['norm_atr'] = df['atr_10'] / df['close'] * 100

        # Add price action features
        if len(df) > 3:
            # Calculate candle body size
            df['body_size'] = np.abs(df['close'] - df['open']) / df['open'] * 100

            # Calculate upper and lower shadows
            if 'high' in df.columns and 'low' in df.columns:
                df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['open'] * 100
                df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['open'] * 100

            # Calculate candle patterns
            df['bullish_candle'] = (df['close'] > df['open']).astype(int)
            df['consecutive_bullish'] = df['bullish_candle'].rolling(3).sum()
            df['consecutive_bearish'] = 3 - df['consecutive_bullish']

        # Fill NaN values
        df = df.fillna(0)

        return df

    def predict_with_lstm_gru(self, df, is_future=False, horizon=1):
        """
        Make prediction with LSTM-GRU model

        Parameters:
        - df: DataFrame with engineered features
        - is_future: Whether this is a future prediction
        - horizon: Prediction horizon in minutes

        Returns:
        - prediction: Dictionary containing prediction results
        """
        if not TENSORFLOW_AVAILABLE:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "TensorFlow is not available, cannot use LSTM-GRU model"
            }

        model_info = self.models['lstm_gru']

        # Check if we have enough data
        if len(df) < model_info['seq_length']:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Not enough data for LSTM-GRU model (need {model_info['seq_length']} candles)"
            }

        # Make prediction
        try:
            # Extract features
            if model_info['features']:
                # Check if all required features are available
                missing_features = [f for f in model_info['features'] if f not in df.columns]
                if missing_features:
                    print(f"Missing {len(missing_features)} features required by the model")
                    # Add missing features with default values
                    for feature in missing_features:
                        df[feature] = 0
                    print(f"Added missing features with default values")

                # Check if there are extra features that need to be removed
                extra_features = [f for f in df.columns if f not in model_info['features']]
                if extra_features:
                    print(f"Removing {len(extra_features)} extra features not used by the model")

                # Extract only the features needed by the model
                features = df[model_info['features']].copy()
                print(f"Aligned features with model's expected features")
            else:
                features = df.copy()

            # Ensure all data is numeric
            for col in features.columns:
                if not pd.api.types.is_numeric_dtype(features[col]):
                    # Try to convert to numeric, replace non-convertible values with NaN
                    features[col] = pd.to_numeric(features[col], errors='coerce')

            # Fill NaN values
            features = features.fillna(0)

            # Create sequence
            X = np.array([features.values[-model_info['seq_length']:]])

            # Scale features if scaler is available
            if model_info['scaler'] is not None:
                # Check if the number of features matches what the scaler expects
                expected_features = model_info['scaler'].n_features_in_
                if X.shape[-1] != expected_features:
                    print(f"Feature mismatch: Model expects {expected_features} features, but got {X.shape[-1]}")

                    # If we have too many features, select only the ones the model was trained on
                    if X.shape[-1] > expected_features:
                        print(f"Reducing feature count from {X.shape[-1]} to {expected_features}")
                        # Since we don't know which features the model was trained on, we'll just take the first expected_features
                        X = X[:, :, :expected_features]
                    else:
                        # If we have too few features, pad with zeros
                        print(f"Padding feature count from {X.shape[-1]} to {expected_features}")
                        padding = np.zeros((X.shape[0], X.shape[1], expected_features - X.shape[-1]))
                        X = np.concatenate([X, padding], axis=2)

                X = model_info['scaler'].transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)

            # Make prediction with performance optimizations
            # Use TensorFlow's distribution strategy if available
            strategy = self.tf_config.get('strategy')

            if strategy and hasattr(strategy, 'run'):
                # Use strategy.run for distributed prediction
                def predict_fn(x):
                    return model_info['model'](x, training=False)

                result = strategy.run(predict_fn, args=(X,))
                # Extract the result from the distribution strategy
                if hasattr(result, 'values'):
                    prediction_prob = result.values[0][0][0]
                else:
                    prediction_prob = result[0][0]
            else:
                # Standard prediction
                prediction_prob = model_info['model'].predict(X, verbose=0)[0][0]
            prediction = 1 if prediction_prob > 0.5 else 0
            confidence = max(prediction_prob, 1 - prediction_prob)

            # Adjust confidence for future predictions
            if is_future:
                # Decrease confidence as horizon increases
                confidence_decay = 0.95 ** (horizon - 1)  # 5% decay per minute into the future
                adjusted_confidence = confidence * confidence_decay

                # Adjust probability toward 0.5 (uncertainty) as horizon increases
                adjusted_prob = 0.5 + (prediction_prob - 0.5) * confidence_decay

                return {
                    'prediction': prediction,
                    'probability': float(adjusted_prob),
                    'direction': "UP" if prediction == 1 else "DOWN",
                    'confidence': float(adjusted_confidence),
                    'model': 'lstm_gru',
                    'horizon': horizon,
                    'original_confidence': float(confidence)
                }
            else:
                return {
                    'prediction': prediction,
                    'probability': float(prediction_prob),
                    'direction': "UP" if prediction == 1 else "DOWN",
                    'confidence': float(confidence),
                    'model': 'lstm_gru'
                }
        except Exception as e:
            print(f"Error making LSTM-GRU prediction: {e}")
            import traceback
            traceback.print_exc()
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Error making LSTM-GRU prediction: {e}",
                'model': 'lstm_gru'
            }

    def predict_with_transformer(self, df, is_future=False, horizon=1):
        """
        Make prediction with Transformer model

        Parameters:
        - df: DataFrame with engineered features
        - is_future: Whether this is a future prediction
        - horizon: Prediction horizon in minutes

        Returns:
        - prediction: Dictionary containing prediction results
        """
        if not TENSORFLOW_AVAILABLE:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "TensorFlow is not available, cannot use Transformer model"
            }

        model_info = self.models['transformer']

        # Check if we have enough data
        if len(df) < model_info['seq_length']:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Not enough data for Transformer model (need {model_info['seq_length']} candles)"
            }

        # Make prediction
        try:
            # Get base prediction
            try:
                # Check if we have the necessary components
                if 'feature_columns' not in model_info or not model_info['feature_columns']:
                    # Use default feature columns if not available
                    model_info['feature_columns'] = df.select_dtypes(include=['number']).columns.tolist()
                    print(f"Using {len(model_info['feature_columns'])} numeric columns as features for Transformer")

                # Now call the transformer prediction function
                from Models.Transformer_Model import predict_with_transformer
                prediction = predict_with_transformer(model_info, df)
            except Exception as e:
                print(f"Error in transformer prediction function: {e}")
                # Provide a fallback prediction
                import random
                random_pred = random.choice([0, 1])
                prediction = {
                    'prediction': random_pred,
                    'probability': 0.51 if random_pred == 1 else 0.49,
                    'confidence': 0.51,
                    'direction': 'UP' if random_pred == 1 else 'DOWN',
                    'model': 'transformer',
                    'note': 'Fallback prediction due to transformer error'
                }

            # Adjust for future prediction if needed
            if is_future and prediction.get('prediction') is not None:
                # Transformers are generally better at longer-term predictions
                # Use a gentler decay factor
                confidence_decay = 0.97 ** (horizon - 1)  # 3% decay per minute into the future

                # Store original values
                original_confidence = prediction['confidence']
                original_prob = prediction['probability']

                # Adjust confidence and probability
                adjusted_confidence = original_confidence * confidence_decay
                adjusted_prob = 0.5 + (original_prob - 0.5) * confidence_decay

                # Update prediction
                prediction['confidence'] = float(adjusted_confidence)
                prediction['probability'] = float(adjusted_prob)
                prediction['horizon'] = horizon
                prediction['original_confidence'] = float(original_confidence)

            return prediction
        except Exception as e:
            print(f"Error making Transformer prediction: {e}")
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Error making Transformer prediction: {e}",
                'model': 'transformer'
            }

    def predict_with_xgboost(self, df, is_future=False, horizon=1):
        """
        Make prediction with XGBoost model with enhanced error handling

        Parameters:
        - df: DataFrame with engineered features
        - is_future: Whether this is a future prediction
        - horizon: Prediction horizon in minutes

        Returns:
        - prediction: Dictionary containing prediction results
        """
        try:
            # Check if xgboost model exists
            if 'xgboost' not in self.models:
                print("XGBoost model not found in loaded models")
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': "XGBoost model not found in loaded models",
                    'model': 'xgboost'
                }

            model_info = self.models['xgboost']

            # Check if model exists
            if 'model' not in model_info or model_info['model'] is None:
                print("XGBoost model object is missing")
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': "XGBoost model object is missing",
                    'model': 'xgboost'
                }

            # Make a copy of the dataframe to avoid modifying the original
            df_copy = df.copy()

            # Check if we need to apply feature engineering
            try:
                # First, check if features is a valid list
                if not isinstance(model_info.get('features'), list):
                    print("Model features is not a valid list, extracting features from model...")
                    try:
                        # Try to extract features from the model
                        model_features = model_info['model'].get_booster().feature_names
                        if model_features:
                            model_info['features'] = model_features
                            print(f"Extracted {len(model_features)} features from model")
                        else:
                            # If no features found, use a default set of common features
                            default_features = ['close', 'open', 'high', 'low', 'volume',
                                              'sma_5', 'sma_20', 'ema_5', 'ema_20',
                                              'rsi_14', 'macd', 'macd_signal', 'macd_hist']
                            model_info['features'] = [f for f in default_features if f in df_copy.columns]
                            print(f"Using {len(model_info['features'])} default features")
                    except Exception as e:
                        print(f"Error extracting features from model: {e}")
                        # Use all available numeric columns as features
                        model_info['features'] = df_copy.select_dtypes(include=['number']).columns.tolist()
                        print(f"Using all {len(model_info['features'])} numeric columns as features")

                # Now check if we need to apply feature engineering
                if model_info.get('features') and any(f not in df_copy.columns for f in model_info['features']):
                    print("Applying feature engineering to generate missing features...")

                    # Import feature engineering module
                    from Models.Feature_Engineering import engineer_features

                    # Apply feature engineering to generate missing features
                    df_copy = engineer_features(df_copy)
                    print(f"Feature engineering applied. Available columns: {len(df_copy.columns)}")
            except Exception as e:
                print(f"Error checking features: {e}")
                # Continue with available features

            # Ensure all data is numeric for XGBoost
            print(f"Converting non-numeric columns...")
            non_numeric_cols = df_copy.select_dtypes(exclude=['number']).columns.tolist()
            if non_numeric_cols:
                print(f"Converting {len(non_numeric_cols)} non-numeric columns: {non_numeric_cols}")
                for col in non_numeric_cols:
                    # Try to convert to numeric, replace non-convertible values with NaN
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

                # Fill NaN values
                df_copy = df_copy.fillna(0)

            # Get base prediction
            try:
                # Check if the feature names are corrupted (contain binary data)
                if model_info.get('features') and any('\x00' in str(f) for f in model_info.get('features')):
                    print("Detected corrupted feature names, cleaning up feature names")

                    # Clean up feature names by replacing them with generic names
                    clean_features = []
                    for i, feature in enumerate(model_info.get('features')):
                        clean_features.append(f'feature_{i}')

                    print(f"Replaced {len(model_info.get('features'))} corrupted feature names with clean names")
                    model_info['features'] = clean_features

                from Models.XGBoost_Model import predict_with_xgboost
                prediction = predict_with_xgboost(
                    model=model_info['model'],
                    data=df_copy,
                    feature_columns=model_info.get('features')
                )
            except Exception as e:
                print(f"Error in XGBoost prediction: {e}")
                # Provide a fallback prediction

                # Try to use a simple trend-following strategy
                if 'close' in df_copy.columns and len(df_copy) >= 3:
                    # Simple trend-following strategy as fallback
                    last_3_closes = df_copy['close'].tail(3).values
                    if last_3_closes[-1] > last_3_closes[-2] and last_3_closes[-2] > last_3_closes[-3]:
                        # Strong uptrend
                        prediction = {
                            'probability': 0.65,
                            'prediction': 1,
                            'direction': 'UP',
                            'confidence': 0.65,
                            'note': 'Fallback prediction based on recent uptrend',
                            'error': str(e)
                        }
                    elif last_3_closes[-1] < last_3_closes[-2] and last_3_closes[-2] < last_3_closes[-3]:
                        # Strong downtrend
                        prediction = {
                            'probability': 0.35,
                            'prediction': 0,
                            'direction': 'DOWN',
                            'confidence': 0.65,
                            'note': 'Fallback prediction based on recent downtrend',
                            'error': str(e)
                        }
                    else:
                        # No clear trend, use random prediction
                        import random
                        random_pred = random.choice([0, 1])
                        prediction = {
                            'probability': 0.51 if random_pred == 1 else 0.49,
                            'prediction': random_pred,
                            'direction': 'UP' if random_pred == 1 else 'DOWN',
                            'confidence': 0.51,
                            'note': 'Random fallback prediction due to model error',
                            'error': str(e)
                        }
                else:
                    # If we can't use trend-following, use random prediction
                    import random
                    random_pred = random.choice([0, 1])
                    prediction = {
                        'probability': 0.51 if random_pred == 1 else 0.49,
                        'prediction': random_pred,
                        'direction': 'UP' if random_pred == 1 else 'DOWN',
                        'confidence': 0.51,
                        'note': 'Random fallback prediction due to model error',
                        'error': str(e)
                    }

            # Add model name to prediction
            prediction['model'] = 'xgboost'

            # Adjust for future prediction if needed
            if is_future and prediction.get('prediction') is not None:
                # XGBoost is generally less reliable for future predictions
                # Use a steeper decay factor
                confidence_decay = 0.90 ** (horizon - 1)  # 10% decay per minute into the future

                # Store original values
                original_confidence = prediction['confidence']
                original_prob = prediction['probability']

                # Adjust confidence and probability
                adjusted_confidence = original_confidence * confidence_decay
                adjusted_prob = 0.5 + (original_prob - 0.5) * confidence_decay

                # Update prediction
                prediction['confidence'] = float(adjusted_confidence)
                prediction['probability'] = float(adjusted_prob)
                prediction['horizon'] = horizon
                prediction['original_confidence'] = float(original_confidence)

            # Ensure all values are properly formatted
            if 'prediction' in prediction and prediction['prediction'] is not None:
                try:
                    prediction['prediction'] = int(prediction['prediction'])
                except (TypeError, ValueError) as e:
                    print(f"Error converting prediction to int: {e}")
                    # Provide a fallback prediction
                    prediction['prediction'] = 1  # Default to UP
                    prediction['note'] = "Using fallback prediction due to conversion error"

            if 'probability' in prediction and prediction['probability'] is not None:
                try:
                    prediction['probability'] = float(prediction['probability'])
                except (TypeError, ValueError) as e:
                    print(f"Error converting probability to float: {e}")
                    prediction['probability'] = 0.5  # Default to 50% probability

            if 'confidence' in prediction and prediction['confidence'] is not None:
                try:
                    prediction['confidence'] = float(prediction['confidence'])
                except (TypeError, ValueError) as e:
                    print(f"Error converting confidence to float: {e}")
                    prediction['confidence'] = 0.5  # Default confidence

            return prediction
        except Exception as e:
            print(f"Error making XGBoost prediction: {e}")
            # Print detailed error information
            import traceback
            traceback.print_exc()

            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Error making XGBoost prediction: {e}",
                'model': 'xgboost'
            }

    def predict_with_dqn(self, df, is_future=False, horizon=1):
        """
        Make prediction with DQN model

        Parameters:
        - df: DataFrame with engineered features
        - is_future: Whether this is a future prediction
        - horizon: Prediction horizon in minutes

        Returns:
        - prediction: Dictionary containing prediction results
        """
        if not TENSORFLOW_AVAILABLE:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "TensorFlow is not available, cannot use DQN model"
            }

        model_info = self.models['dqn']

        # Check if DQN agent is available
        if model_info.get('agent') is None:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "DQN agent could not be initialized properly"
            }

        # Check if we have enough data
        if len(df) < model_info['window_size']:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Not enough data for DQN model (need {model_info['window_size']} candles)"
            }

        # Make prediction
        try:
            # Extract features
            features = df[model_info['features']].copy() if model_info['features'] else df

            # Create state
            window_start = max(0, len(features) - model_info['window_size'])
            window_data = features.iloc[window_start:].values

            # Create portfolio state (dummy values for prediction)
            # For future predictions, we can adjust the portfolio state to reflect potential future state
            if is_future:
                # Simulate a portfolio state that reflects the horizon
                # Higher horizon = more uncertainty in portfolio state
                uncertainty_factor = min(0.5, 0.1 * horizon)  # Cap at 0.5
                portfolio_state = np.array([
                    1.0,  # normalized balance
                    uncertainty_factor,  # consecutive losses (higher for longer horizons)
                    uncertainty_factor   # max drawdown (higher for longer horizons)
                ])
            else:
                portfolio_state = np.array([1.0, 0.0, 0.0])  # normalized balance, consecutive losses, max drawdown

            # Create state dictionary
            state = {
                'market_features': window_data,
                'portfolio_state': portfolio_state
            }

            # Get action from agent
            action = model_info['agent'].act(state, eval_mode=True)

            # Convert action to prediction
            prediction = action  # 0 = DOWN, 1 = UP

            # Get Q-values for confidence
            market_features = np.expand_dims(state['market_features'], axis=0)
            portfolio_state = np.expand_dims(state['portfolio_state'], axis=0)
            q_values = model_info['agent'].model.predict([market_features, portfolio_state], verbose=0)[0]

            # Calculate probability and confidence
            q_sum = np.sum(np.exp(q_values))
            probabilities = np.exp(q_values) / q_sum
            prediction_prob = probabilities[prediction]
            confidence = max(probabilities)

            # Adjust for future prediction if needed
            if is_future:
                # DQN is designed for sequential decision making, so it's actually
                # quite good at predicting future states, but confidence should still decay
                confidence_decay = 0.93 ** (horizon - 1)  # 7% decay per minute into the future

                # Store original values
                original_confidence = confidence
                original_prob = prediction_prob

                # Adjust confidence and probability
                adjusted_confidence = original_confidence * confidence_decay
                adjusted_prob = 0.5 + (original_prob - 0.5) * confidence_decay

                return {
                    'prediction': prediction,
                    'probability': float(adjusted_prob),
                    'direction': "UP" if prediction == 1 else "DOWN",
                    'confidence': float(adjusted_confidence),
                    'model': 'dqn',
                    'horizon': horizon,
                    'original_confidence': float(original_confidence)
                }
            else:
                return {
                    'prediction': prediction,
                    'probability': float(prediction_prob),
                    'direction': "UP" if prediction == 1 else "DOWN",
                    'confidence': float(confidence),
                    'model': 'dqn'
                }
        except Exception as e:
            print(f"Error making DQN prediction: {e}")
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': f"Error making DQN prediction: {e}",
                'model': 'dqn'
            }

    def predict_with_ensemble(self, df, is_future=False, horizon=1):
        """
        Make prediction with ensemble model

        Parameters:
        - df: DataFrame with engineered features
        - is_future: Whether this is a future prediction
        - horizon: Prediction horizon in minutes

        Returns:
        - prediction: Dictionary containing prediction results
        """
        if not TENSORFLOW_AVAILABLE:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "TensorFlow is not available, cannot use ensemble model"
            }

        if self.ensemble is None:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "Ensemble model not available"
            }

        # Make predictions with individual models
        predictions = {}

        if 'lstm_gru' in self.models:
            predictions['lstm_gru'] = self.predict_with_lstm_gru(df, is_future, horizon)

        if 'transformer' in self.models:
            predictions['transformer'] = self.predict_with_transformer(df, is_future, horizon)

        if 'xgboost' in self.models:
            predictions['xgboost'] = self.predict_with_xgboost(df, is_future, horizon)

        if 'dqn' in self.models:
            predictions['dqn'] = self.predict_with_dqn(df, is_future, horizon)

        # Check if we have any valid predictions
        valid_predictions = {k: v for k, v in predictions.items() if v.get('prediction') is not None}

        if not valid_predictions:
            return {
                'prediction': None,
                'probability': None,
                'direction': "UNKNOWN",
                'confidence': None,
                'error': "No valid predictions from individual models"
            }

        # Prepare input data for ensemble prediction
        ensemble_input = {}

        # Add individual model predictions
        for model_name, pred in valid_predictions.items():
            ensemble_input[model_name] = {
                'prediction': pred['prediction'],
                'probability': pred['probability'],
                'confidence': pred['confidence']
            }

        # Prepare sequence data for LSTM model if needed
        if 'lstm_gru' in self.models:
            model_info = self.models['lstm_gru']
            if len(df) >= model_info['seq_length']:
                try:
                    # Extract features with safety check
                    if model_info['features']:
                        # Check if all required features are available
                        missing_features = [f for f in model_info['features'] if f not in df.columns]
                        if missing_features:
                            print(f"Warning: Missing {len(missing_features)} features required by LSTM model")
                            # Add missing features with default values
                            for feature in missing_features:
                                df[feature] = 0
                            print(f"Added missing features with default values")
                        features = df[model_info['features']].copy()
                    else:
                        features = df.copy()

                    # Create sequence with safety check
                    seq_length = min(model_info['seq_length'], len(features))
                    if seq_length < model_info['seq_length']:
                        print(f"Warning: Using reduced sequence length ({seq_length}) for LSTM model")

                    X = np.array([features.values[-seq_length:]])

                    # Scale features if scaler is available
                    if model_info['scaler'] is not None:
                        X = model_info['scaler'].transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)

                    # Add sequence data to ensemble input
                    ensemble_input['sequence_data'] = X
                except Exception as e:
                    print(f"Error preparing LSTM sequence data: {e}")
                    # Continue without LSTM data

        # Prepare tabular data for XGBoost model if needed
        if 'xgboost' in self.models:
            model_info = self.models['xgboost']
            # Extract features for the latest data point
            features = df[model_info['features']].iloc[-1:].copy() if model_info['features'] else df.iloc[-1:].copy()
            # Add tabular data to ensemble input
            ensemble_input['tabular_data'] = features

        # Prepare state data for DQN model if needed
        if 'dqn' in self.models:
            model_info = self.models['dqn']
            if len(df) >= model_info['window_size']:
                try:
                    # Extract features with safety check
                    if model_info['features']:
                        # Check if all required features are available
                        missing_features = [f for f in model_info['features'] if f not in df.columns]
                        if missing_features:
                            print(f"Warning: Missing {len(missing_features)} features required by DQN model")
                            # Add missing features with default values
                            for feature in missing_features:
                                df[feature] = 0
                            print(f"Added missing features with default values")
                        features = df[model_info['features']].copy()
                    else:
                        features = df.copy()

                    # Create state with safety check
                    window_size = min(model_info['window_size'], len(features))
                    if window_size < model_info['window_size']:
                        print(f"Warning: Using reduced window size ({window_size}) for DQN model")

                    window_start = max(0, len(features) - window_size)
                    window_data = features.iloc[window_start:].values

                    # Create portfolio state (dummy values for prediction)
                    portfolio_state = np.array([1.0, 0.0, 0.0])  # normalized balance, consecutive losses, max drawdown

                    # Create state dictionary
                    state = {
                        'market_features': window_data,
                        'portfolio_state': portfolio_state
                    }

                    # Add state data to ensemble input
                    ensemble_input['state'] = state
                except Exception as e:
                    print(f"Error preparing DQN state data: {e}")
                    # Continue without DQN data

        # Make ensemble prediction
        try:
            ensemble_pred = self.ensemble.predict(ensemble_input)
        except Exception as e:
            print(f"Error making ensemble prediction: {e}")
            # If ensemble prediction fails, use the best individual prediction
            best_pred = None
            best_confidence = 0

            for model_name, pred in valid_predictions.items():
                if pred.get('confidence', 0) > best_confidence:
                    best_confidence = pred.get('confidence', 0)
                    best_pred = pred

            if best_pred:
                print(f"Using best individual prediction from {best_pred.get('model', 'unknown')} model")
                return best_pred
            else:
                return {
                    'prediction': None,
                    'probability': None,
                    'direction': "UNKNOWN",
                    'confidence': None,
                    'error': f"Ensemble prediction failed: {e}"
                }

        # Add individual model predictions for reference
        ensemble_pred['model_predictions'] = predictions
        ensemble_pred['model'] = 'ensemble'

        # Add future prediction information if applicable
        if is_future:
            ensemble_pred['horizon'] = horizon

            # Calculate model agreement
            up_votes = sum(1 for p in valid_predictions.values() if p['direction'] == 'UP')
            down_votes = sum(1 for p in valid_predictions.values() if p['direction'] == 'DOWN')
            total_votes = up_votes + down_votes

            # Calculate agreement percentage
            if total_votes > 0:
                if ensemble_pred['direction'] == 'UP':
                    agreement = up_votes / total_votes
                else:
                    agreement = down_votes / total_votes

                ensemble_pred['model_agreement'] = float(agreement)
                ensemble_pred['up_votes'] = up_votes
                ensemble_pred['down_votes'] = down_votes

                # Adjust confidence based on model agreement
                # Higher agreement = higher confidence
                agreement_boost = (agreement - 0.5) * 2  # Scale from 0 to 1
                confidence_decay = 0.95 ** (horizon - 1)  # 5% decay per minute into the future

                # Store original confidence
                ensemble_pred['original_confidence'] = ensemble_pred['confidence']

                # Apply agreement boost and horizon decay
                adjusted_confidence = ensemble_pred['confidence'] * (1 + agreement_boost * 0.2) * confidence_decay
                ensemble_pred['confidence'] = min(0.95, float(adjusted_confidence))  # Cap at 0.95

                # Adjust probability toward 0.5 (uncertainty) as horizon increases
                original_prob = ensemble_pred['probability']
                adjusted_prob = 0.5 + (original_prob - 0.5) * confidence_decay
                ensemble_pred['probability'] = float(adjusted_prob)

        # Ensure all values are properly formatted
        if 'prediction' in ensemble_pred:
            ensemble_pred['prediction'] = int(ensemble_pred['prediction'])
        if 'probability' in ensemble_pred:
            ensemble_pred['probability'] = float(ensemble_pred['probability'])
        if 'confidence' in ensemble_pred:
            ensemble_pred['confidence'] = float(ensemble_pred['confidence'])
        if 'direction' not in ensemble_pred and 'prediction' in ensemble_pred:
            ensemble_pred['direction'] = 'UP' if ensemble_pred['prediction'] == 1 else 'DOWN'

        return ensemble_pred

    def initialize_self_learning(self, data_collector=None):
        """
        Initialize self-learning capabilities using the ModelTrainer

        Parameters:
        - data_collector: Optional data collector object for fetching market data

        Returns:
        - model_trainer: Initialized ModelTrainer object
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot initialize self-learning capabilities")
            # Create a dummy model trainer that does nothing
            self.model_trainer = ModelTrainer()
            return self.model_trainer

        print("Initializing self-learning capabilities...")

        # Create a simple data collector if none is provided
        if data_collector is None:
            class SimpleDataCollector:
                def fetch_historical_data(self, lookback_days=30):
                    # This is a placeholder - in a real implementation, this would fetch data
                    print(f"Fetching {lookback_days} days of historical data...")
                    return None

                def preprocess_data(self, data):
                    # This is a placeholder - in a real implementation, this would preprocess data
                    print("Preprocessing data...")
                    return data

            data_collector = SimpleDataCollector()

        # Initialize the model trainer
        model_trainer = ModelTrainer(
            data_collector=data_collector,
            feature_engineer=engineer_features,
            adaptation_rate=self.config.get('adaptation_rate', 0.1),
            confidence_threshold=self.config.get('confidence_threshold', 0.65),
            memory_factor=self.config.get('memory_factor', 0.8),
            retraining_threshold=self.config.get('retraining_threshold', 0.55),
            optimization_interval=self.config.get('optimization_interval', 10)
        )

        # Store the model trainer
        self.model_trainer = model_trainer

        print("Self-learning capabilities initialized")
        return model_trainer

    def record_prediction_feedback(self, prediction, actual_outcome, confidence, timestamp=None, prediction_details=None):
        """
        Record feedback from a prediction for self-learning

        Parameters:
        - prediction: The prediction made (1 for UP, 0 for DOWN)
        - actual_outcome: The actual outcome (1 for UP, 0 for DOWN)
        - confidence: The confidence level of the prediction (0-1)
        - timestamp: Optional timestamp (defaults to current time)
        - prediction_details: Optional dictionary with additional prediction details

        Returns:
        - correct: Whether the prediction was correct
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot record prediction feedback")
            return prediction == actual_outcome

        if hasattr(self, 'model_trainer'):
            return self.model_trainer.record_prediction_feedback(
                prediction, actual_outcome, confidence, timestamp, prediction_details
            )
        else:
            print("Warning: Self-learning not initialized. Call initialize_self_learning() first.")
            return prediction == actual_outcome

    def learn_from_trade(self, trade_data, was_successful):
        """
        Learn from a completed trade

        Parameters:
        - trade_data: Dictionary with trade details
        - was_successful: Whether the trade was successful

        Returns:
        - bool: Whether learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot learn from trade")
            return False

        if hasattr(self, 'enhanced_trainer') and self.enhanced_trainer:
            print("Using enhanced trainer for learning from trade")
            return self.enhanced_trainer.learn_from_trade(trade_data, was_successful)
        elif hasattr(self, 'model_trainer'):
            if hasattr(self.model_trainer, 'learn_from_trade'):
                print("Using model trainer for learning from trade")
                return self.model_trainer.learn_from_trade(trade_data, was_successful)
            else:
                print("Warning: ModelTrainer does not have learn_from_trade method")
                # Extract prediction details from trade data
                prediction = trade_data.get('prediction', 0)
                actual = 1 if was_successful else 0
                confidence = trade_data.get('confidence', 0.5)
                timestamp = trade_data.get('timestamp', None)

                # Use record_prediction_feedback as fallback
                return self.record_prediction_feedback(prediction, actual, confidence, timestamp, trade_data)
        else:
            print("Warning: Self-learning not initialized. Call initialize_self_learning() first.")
            return False

    def special_learning_from_mistakes(self, consecutive_losses):
        """
        Apply special learning when consecutive losses are detected

        Parameters:
        - consecutive_losses: Number of consecutive losses

        Returns:
        - bool: Whether special learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot apply special learning")
            return False

        if hasattr(self, 'enhanced_trainer') and self.enhanced_trainer:
            print("Using enhanced trainer for special learning")
            return self.enhanced_trainer.special_learning_from_mistakes(consecutive_losses)
        elif hasattr(self, 'model_trainer'):
            if hasattr(self.model_trainer, 'special_learning_from_mistakes'):
                print("Using model trainer for special learning")
                return self.model_trainer.special_learning_from_mistakes(consecutive_losses)
            else:
                print("Warning: ModelTrainer does not have special_learning_from_mistakes method")
                return False
        else:
            print("Warning: Self-learning not initialized. Call initialize_self_learning() first.")
            return False

    def batch_learn_from_trades(self, trades):
        """
        Apply batch learning from multiple trades

        Parameters:
        - trades: List of trade data dictionaries

        Returns:
        - bool: Whether batch learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot apply batch learning")
            return False

        if hasattr(self, 'enhanced_trainer') and self.enhanced_trainer:
            print("Using enhanced trainer for batch learning")
            return self.enhanced_trainer.batch_learn_from_trades(trades)
        elif hasattr(self, 'model_trainer'):
            if hasattr(self.model_trainer, 'batch_learn_from_trades'):
                print("Using model trainer for batch learning")
                return self.model_trainer.batch_learn_from_trades(trades)
            else:
                print("Warning: ModelTrainer does not have batch_learn_from_trades method")
                return False
        else:
            print("Warning: Self-learning not initialized. Call initialize_self_learning() first.")
            return False

    def start_continuous_learning(self, interval_hours=6):
        """
        Start the continuous learning loop in a background thread

        Parameters:
        - interval_hours: Hours between learning iterations

        Returns:
        - thread: The background thread running the continuous learning loop
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot start continuous learning")
            return None

        if hasattr(self, 'model_trainer'):
            import threading

            # Create a thread for continuous learning
            learning_thread = threading.Thread(
                target=self.model_trainer.continuous_learning_loop,
                args=(interval_hours,),
                daemon=True  # Make it a daemon thread so it exits when the main program exits
            )

            # Start the thread
            learning_thread.start()
            print(f"Continuous learning started with {interval_hours} hour interval")

            # Store the thread
            self.learning_thread = learning_thread

            return learning_thread
        else:
            print("Warning: Self-learning not initialized. Call initialize_self_learning() first.")
            return None

    def stop_continuous_learning(self):
        """
        Stop the continuous learning loop
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, no continuous learning to stop")
            return False

        if hasattr(self, 'model_trainer'):
            self.model_trainer._exit_learning_loop = True
            print("Stopping continuous learning loop...")
            return True
        else:
            print("Warning: Self-learning not initialized.")
            return False
