import tensorflow as tf
import os

print("TensorFlow version:", tf.__version__)

# Check for GPU availability
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    print(f"GPU is available. Found {len(gpus)} GPU(s):")
    for gpu in gpus:
        print(f"  - {gpu.name}")
    
    # Get GPU details
    try:
        for gpu in gpus:
            # Get GPU details
            gpu_details = tf.config.experimental.get_device_details(gpu)
            print(f"  Details for {gpu.name}:")
            print(f"    {gpu_details}")
    except:
        print("  (Detailed GPU information not available)")
else:
    print("No GPU found. Using CPU only.")

# Check CPU information
cpus = tf.config.list_physical_devices('CPU')
print(f"\nCPU devices: {len(cpus)}")
for cpu in cpus:
    print(f"  - {cpu.name}")

# Print memory info
print("\nMemory configuration:")
try:
    memory_info = tf.config.experimental.get_memory_info('GPU:0')
    print(f"  GPU memory info: {memory_info}")
except:
    print("  GPU memory info not available")

# Print device placement
print("\nDevice placement:")
with tf.device('/CPU:0'):
    a = tf.constant([[1.0, 2.0], [3.0, 4.0]])
    print(f"  Tensor a created on: {a.device}")

if gpus:
    with tf.device('/GPU:0'):
        b = tf.constant([[5.0, 6.0], [7.0, 8.0]])
        print(f"  Tensor b created on: {b.device}")

# Test a simple operation
print("\nRunning a simple test operation...")
c = tf.matmul(a, a)
print(f"  Matrix multiplication result: {c}")
print(f"  Operation executed on: {c.device}")

print("\nTensorFlow is configured and ready to use.")
