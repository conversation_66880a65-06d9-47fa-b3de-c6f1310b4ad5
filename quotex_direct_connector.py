#!/usr/bin/env python
"""
Quotex Direct Connector
This module provides a direct way to connect to the Quotex API without using asyncio
"""

import os
import time
import threading
import subprocess
from PyQt5 import QtCore, QtWidgets

class QuotexDirectConnector(QtCore.QObject):
    """Direct connector for Quotex API"""

    # Define signals for connection events
    connection_started = QtCore.pyqtSignal()
    connection_success = QtCore.pyqtSignal(object)  # Pass the API client
    connection_failed = QtCore.pyqtSignal(str)  # Pass error message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.email = ""
        self.password = ""
        self.api_client = None
        self.api_connected = False
        self.read_credentials_from_config()

    def read_credentials_from_config(self):
        """Read credentials from config.ini file"""
        try:
            # List of possible config file locations
            config_files = [
                'config.ini',
                'settings/config.ini',
                'settings\\config.ini',
                'settings.ini'
            ]

            # Try to read directly from the files first
            for config_file in config_files:
                if os.path.exists(config_file):
                    print(f"Found config file: {config_file}")

                    # Try direct file reading
                    try:
                        with open(config_file, 'r') as f:
                            lines = f.readlines()
                            for line in lines:
                                if line.strip().startswith('email='):
                                    self.email = line.strip().replace('email=', '')
                                elif line.strip().startswith('password='):
                                    self.password = line.strip().replace('password=', '')

                            if self.email and self.password:
                                print(f"Loaded credentials from {config_file} for: {self.email}")
                                return True
                    except Exception as file_error:
                        print(f"Error reading {config_file} directly: {file_error}")

            # If no config file found, try environment variables
            if not self.email or not self.password:
                self.email = os.environ.get('QUOTEX_EMAIL', '')
                self.password = os.environ.get('QUOTEX_PASSWORD', '')
                if self.email and self.password:
                    print(f"Loaded credentials from environment variables for: {self.email}")
                    return True

            return False
        except Exception as e:
            print(f"Error reading credentials from config: {e}")
            return False

    def connect_to_api(self, parent_widget=None):
        """Connect to Quotex API"""
        # Check if we have credentials
        if not self.email or not self.password:
            if parent_widget:
                reply = QtWidgets.QMessageBox.question(
                    parent_widget,
                    "No API Credentials",
                    "No Quotex API credentials are set. Would you like to enter them now?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.Yes
                )

                if reply == QtWidgets.QMessageBox.Yes:
                    self.show_credentials_dialog(parent_widget)
                    # If credentials were set, try again
                    if self.email and self.password:
                        # Wait a moment and try again
                        QtCore.QTimer.singleShot(500, lambda: self.connect_to_api(parent_widget))
                    return False
            else:
                print("No credentials available for connection")
                return False

        # Emit signal that connection has started
        self.connection_started.emit()

        # Create and show PIN dialog
        if parent_widget:
            self.show_pin_dialog(parent_widget)
        else:
            # Start connection in a separate thread
            thread = threading.Thread(target=self._connect_thread)
            thread.daemon = True
            thread.start()

        return True

    def _connect_thread(self):
        """Thread function to connect to Quotex API"""
        try:
            # Import Quotex API
            try:
                from quotexapi.stable_api import Quotex
                print("Successfully imported Quotex API module")
            except ImportError as e:
                error_msg = f"Error importing Quotex API: {e}"
                print(error_msg)
                self.connection_failed.emit(error_msg)
                return False

            # Create client
            print(f"Creating Quotex client with email: {self.email}")
            client = Quotex(self.email, self.password)

            # Connect using a separate process to avoid event loop issues
            print("Connecting to Quotex API using a separate process...")

            # Create a temporary script to connect to the API
            script_path = "connect_quotex.py"
            with open(script_path, "w") as f:
                f.write(f"""#!/usr/bin/env python
import asyncio
import sys
import json
from quotexapi.stable_api import Quotex

async def connect():
    try:
        client = Quotex("{self.email}", "{self.password}")
        connected = await client.connect()
        if connected:
            print("Connection successful")
            # Get assets to verify connection
            assets = await client.get_all_asset()
            print(f"Assets: {{assets}}")
            return {{"success": True, "assets": assets}}
        else:
            print("Connection failed")
            return {{"success": False, "error": "Connection failed"}}
    except Exception as e:
        print(f"Error: {{e}}")
        return {{"success": False, "error": str(e)}}

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(connect())
    print(json.dumps(result))
    sys.exit(0 if result.get("success", False) else 1)
""")

            # Run the script in a separate process
            process = subprocess.Popen(
                ["python", script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # Read the output
            output = ""
            for line in process.stdout:
                print(line.strip())
                output += line

            # Wait for the process to complete
            process.wait()

            # Check if the process was successful
            if process.returncode == 0:
                print("Connection successful")

                # Create client
                self.api_client = client
                self.api_connected = True

                # Emit success signal with the client
                self.connection_success.emit(client)

                return True
            else:
                print(f"Connection failed: {output}")
                self.connection_failed.emit(f"Connection failed: {output}")
                return False
        except Exception as e:
            error_msg = str(e)
            print(f"Error in _connect_thread: {error_msg}")
            self.connection_failed.emit(error_msg)
            return False

    def show_pin_dialog(self, parent_widget):
        """Show dialog for PIN verification"""
        # Create dialog
        dialog = QtWidgets.QDialog(parent_widget)
        dialog.setWindowTitle("Quotex PIN Verification")
        dialog.setMinimumWidth(400)

        # Create layout
        layout = QtWidgets.QVBoxLayout(dialog)

        # Add header
        header_label = QtWidgets.QLabel("Quotex PIN Verification")
        header_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3;")
        header_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(header_label)

        # Add instructions
        instructions = QtWidgets.QLabel(
            "Quotex requires PIN verification for login.\n\n"
            "1. Enter your email and password below\n"
            "2. If you have received a PIN code, enter it below\n"
            "3. Click 'Connect' to start the connection process"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add form layout
        form_layout = QtWidgets.QFormLayout()

        # Add email field
        email_label = QtWidgets.QLabel("Email:")
        self.email_input = QtWidgets.QLineEdit()
        self.email_input.setText(self.email)
        form_layout.addRow(email_label, self.email_input)

        # Add password field
        password_label = QtWidgets.QLabel("Password:")
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        self.password_input.setText(self.password)
        form_layout.addRow(password_label, self.password_input)

        # Add PIN field
        pin_label = QtWidgets.QLabel("PIN Code (if required):")
        self.pin_input = QtWidgets.QLineEdit()
        self.pin_input.setPlaceholderText("Enter PIN from email if required")
        form_layout.addRow(pin_label, self.pin_input)

        layout.addLayout(form_layout)

        # Add console output
        console_label = QtWidgets.QLabel("Connection Log:")
        layout.addWidget(console_label)

        self.console = QtWidgets.QTextEdit()
        self.console.setReadOnly(True)
        self.console.setStyleSheet("background-color: #000; color: #0f0; font-family: monospace;")
        self.console.setMaximumHeight(150)
        layout.addWidget(self.console)

        # Add status label
        self.status_label = QtWidgets.QLabel("Ready to connect")
        self.status_label.setStyleSheet("color: blue;")
        layout.addWidget(self.status_label)

        # Add buttons
        button_layout = QtWidgets.QHBoxLayout()

        # Connect button
        connect_button = QtWidgets.QPushButton("Connect")
        connect_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        connect_button.clicked.connect(self._on_connect_clicked)

        # Cancel button
        cancel_button = QtWidgets.QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(connect_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # Store dialog reference
        self.pin_dialog = dialog

        # Show dialog
        dialog.exec_()

    def _on_connect_clicked(self):
        """Handle connect button click"""
        # Update credentials
        self.email = self.email_input.text()
        self.password = self.password_input.text()
        pin = self.pin_input.text().strip()

        # Update status
        self.status_label.setText("Connecting to Quotex API...")
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")

        # Create a temporary script to connect to the API
        script_path = "connect_quotex_with_pin.py"
        with open(script_path, "w") as f:
            f.write(f"""#!/usr/bin/env python
import asyncio
import sys
import json
import builtins
from quotexapi.stable_api import Quotex

# Override input function to return PIN
original_input = builtins.input
def mock_input(prompt):
    print(f"PIN prompt: {{prompt}}")
    print(f"Automatically entering PIN: {pin}")
    return "{pin}"

# Replace input function if PIN is provided
if "{pin}":
    builtins.input = mock_input

async def connect():
    try:
        client = Quotex("{self.email}", "{self.password}")
        connected = await client.connect()
        if connected:
            print("Connection successful")
            # Get assets to verify connection
            assets = await client.get_all_asset()
            print(f"Assets: {{assets}}")
            return {{"success": True, "assets": assets}}
        else:
            print("Connection failed")
            return {{"success": False, "error": "Connection failed"}}
    except Exception as e:
        print(f"Error: {{e}}")
        return {{"success": False, "error": str(e)}}
    finally:
        # Restore original input function
        if "{pin}":
            builtins.input = original_input

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(connect())
    print(json.dumps(result))
    sys.exit(0 if result.get("success", False) else 1)
""")

        # Run the script in a separate process
        try:
            process = subprocess.Popen(
                ["python", script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # Read the output
            for line in process.stdout:
                # Update the console
                self.console.append(line.strip())
                self.console.verticalScrollBar().setValue(self.console.verticalScrollBar().maximum())

                # Check for success message
                if "Connection successful" in line:
                    self.status_label.setText("Connection successful")
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")

                # Check for PIN prompt
                if "PIN prompt" in line:
                    self.status_label.setText("PIN verification in progress...")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")

            # Wait for the process to complete
            process.wait()

            # Check if the process was successful
            if process.returncode == 0:
                self.status_label.setText("Connection successful")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

                # Import Quotex API
                from quotexapi.stable_api import Quotex

                # Create client
                client = Quotex(self.email, self.password)
                self.api_client = client
                self.api_connected = True

                # Close the PIN dialog
                if self.pin_dialog:
                    self.pin_dialog.accept()

                # Emit success signal with the client
                self.connection_success.emit(client)
            else:
                self.status_label.setText("Connection failed")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.connection_failed.emit("Connection failed")
        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.connection_failed.emit(str(e))

    def show_credentials_dialog(self, parent_widget):
        """Show dialog to set API credentials"""
        dialog = QtWidgets.QDialog(parent_widget)
        dialog.setWindowTitle("Set Quotex API Credentials")
        dialog.setMinimumWidth(400)

        layout = QtWidgets.QVBoxLayout(dialog)

        # Add instructions
        instructions = QtWidgets.QLabel(
            "Enter your Quotex API credentials below. "
            "These will be used to connect to the Quotex API."
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add form layout
        form_layout = QtWidgets.QFormLayout()

        # Add email field
        email_label = QtWidgets.QLabel("Email:")
        email_input = QtWidgets.QLineEdit()
        email_input.setText(self.email)
        form_layout.addRow(email_label, email_input)

        # Add password field
        password_label = QtWidgets.QLabel("Password:")
        password_input = QtWidgets.QLineEdit()
        password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        password_input.setText(self.password)
        form_layout.addRow(password_label, password_input)

        layout.addLayout(form_layout)

        # Add buttons
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            # Save credentials
            self.email = email_input.text()
            self.password = password_input.text()

            # Update environment variables
            os.environ['QUOTEX_EMAIL'] = self.email
            os.environ['QUOTEX_PASSWORD'] = self.password

            print("API credentials updated")

            # Save to config.ini for future use
            try:
                with open('config.ini', 'w') as f:
                    f.write(f"email={self.email}\n")
                    f.write(f"password={self.password}\n")
                print("Credentials saved to config.ini")
                return True
            except Exception as e:
                print(f"Error saving credentials to config.ini: {e}")
                return False

        return False
