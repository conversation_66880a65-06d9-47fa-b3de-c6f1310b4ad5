#!/usr/bin/env python
"""
Request PIN Script for Quotex API
This script requests a new PIN from the Quotex API
"""

import os
import sys
import asyncio
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
except ImportError:
    print("Error: quotexapi module not found")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

def read_credentials_from_config():
    """Read credentials from config.ini file"""
    import configparser

    # Create config parser with no interpolation to handle special characters in passwords
    config = configparser.ConfigParser(interpolation=None)

    # Check if config.ini exists
    if os.path.exists('config.ini'):
        try:
            # Try to read config file
            config.read('config.ini')

            # Check if credentials section exists
            if 'credentials' in config:
                # Get credentials
                email = config['credentials'].get('email', '')
                password = config['credentials'].get('password', '')

                if email and password:
                    print(f"Loaded credentials from config.ini for: {email}")
                    return email, password
        except Exception as e:
            print(f"Error reading config file: {e}")

            # Try to read in legacy format
            try:
                email = ""
                password = ""
                with open('config.ini', 'r') as f:
                    for line in f:
                        if line.startswith('email='):
                            email = line.strip().split('=', 1)[1]
                        elif line.startswith('password='):
                            password = line.strip().split('=', 1)[1]

                if email and password:
                    print(f"Loaded credentials from legacy config.ini for: {email}")
                    return email, password
            except Exception as legacy_error:
                print(f"Error reading legacy config file: {legacy_error}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")

    # Get password without echoing
    if not password:
        import getpass
        password = getpass.getpass("Enter your Quotex password: ")

    return email, password

async def request_pin_async(email, password):
    """Request a PIN from Quotex API"""
    print(f"\n=== Requesting PIN for {email} ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # First try with QuotexPINHelper if available
        try:
            import quotex_pin_helper
            print("Using quotex_pin_helper for PIN request...")

            # Create PIN helper
            helper = quotex_pin_helper.QuotexPINHelper(email, password)

            # Request PIN
            print("Requesting PIN...")
            success, message = helper.request_pin()

            if success:
                print("PIN request successful!")
                return True
            else:
                print(f"PIN request failed: {message}")
                # Fall back to direct method
        except ImportError:
            print("quotex_pin_helper not available, using direct method...")
        except Exception as helper_error:
            print(f"Error using PIN helper: {helper_error}")
            print("Falling back to direct method...")

        # Create a new client for PIN request
        print("Creating Quotex client...")
        client = Quotex(email, password)

        # Try to find a method to request PIN
        if hasattr(client, 'request_pin'):
            print("Using request_pin method...")
            result = await client.request_pin()
            print(f"PIN request result: {result}")
            return True
        elif hasattr(client, 'send_pin_request'):
            print("Using send_pin_request method...")
            result = await client.send_pin_request()
            print(f"PIN request result: {result}")
            return True
        elif hasattr(client, 'resend_pin'):
            print("Using resend_pin method...")
            result = await client.resend_pin()
            print(f"PIN request result: {result}")
            return True
        else:
            # If no direct method exists, try to trigger PIN request by attempting to connect
            print("No direct PIN request method found. Attempting to trigger PIN request by connecting...")
            try:
                # Set a short timeout to just trigger the PIN request
                await asyncio.wait_for(client.connect(), timeout=5)
            except asyncio.TimeoutError:
                print("Connection attempt timed out, but this may have triggered a PIN request.")
                return True
            except Exception as connect_error:
                if "pin" in str(connect_error).lower() or "verification" in str(connect_error).lower():
                    print("PIN request triggered successfully.")
                    return True
                else:
                    print(f"Error during connection attempt: {connect_error}")
                    return False

            return True
    except Exception as e:
        print(f"Error requesting PIN: {e}")
        import traceback
        traceback.print_exc()
        return False

# Main function
def main():
    """Main function"""
    print("=== Quotex API PIN Request ===")

    # Read credentials
    email, password = read_credentials_from_config()

    if not email or not password:
        print("No credentials provided. Exiting.")
        sys.exit(1)

    # Create a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # Request PIN
    result = loop.run_until_complete(request_pin_async(email, password))

    # Close the loop
    loop.close()

    if result:
        print("\nPIN request sent successfully.")
        print("Please check your email for the PIN code.")
        sys.exit(0)
    else:
        print("\nFailed to request PIN.")
        print("Please try again later or check your credentials.")
        sys.exit(1)

# Run the main function
if __name__ == "__main__":
    try:
        # Run the main function
        main()
    except KeyboardInterrupt:
        print("\nRequest interrupted by user")
    except Exception as e:
        print(f"\nError in main: {e}")
        import traceback
        traceback.print_exc()
