#!/usr/bin/env python
"""
TradingView API client for fetching real-time market data
"""

import os
import time
import json
import requests
from datetime import datetime, timedelta
import pandas as pd

class TradingViewAPI:
    """Client for fetching data from TradingView API"""
    
    def __init__(self):
        """Initialize the TradingView API client"""
        self.base_url = "https://www.tradingview.com/chart/"
        self.api_url = "https://scanner.tradingview.com/forex/scan"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Content-Type': 'application/json'
        }
        
        # Cache for storing fetched data to reduce API calls
        self.cache = {}
        self.cache_expiry = 60  # Cache expiry in seconds
        
    def fetch_eurusd_data(self, count=100, interval='1m'):
        """
        Fetch EURUSD data from TradingView
        
        Args:
            count (int): Number of candles to fetch
            interval (str): Time interval ('1m', '5m', '15m', '1h', '4h', '1d')
            
        Returns:
            list: List of candle data in format compatible with our chart
        """
        print(f"Fetching EURUSD data from TradingView API (count={count}, interval={interval})")
        
        # Check cache first
        cache_key = f"EURUSD_{interval}_{count}"
        if cache_key in self.cache:
            cache_time, cache_data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_expiry:
                print(f"Using cached data for {cache_key} (age: {time.time() - cache_time:.1f}s)")
                return cache_data
        
        try:
            # Convert interval to TradingView format
            tv_interval = self._convert_interval(interval)
            
            # Prepare request payload
            payload = {
                "symbols": {
                    "tickers": ["EURUSD"],
                    "query": {"types": []}
                },
                "columns": [
                    "close",
                    "open",
                    "high",
                    "low",
                    "volume",
                    "Recommend.All"
                ],
                "range": [0, count],
                "interval": tv_interval
            }
            
            # Make API request
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            
            if response.status_code != 200:
                print(f"Error fetching data from TradingView API: {response.status_code}")
                print(f"Response: {response.text}")
                return None
            
            # Parse response
            data = response.json()
            
            if 'data' not in data:
                print(f"Unexpected response format from TradingView API: {data}")
                return None
            
            # Process the data into candles
            candles = self._process_tradingview_data(data['data'], interval)
            
            # Cache the result
            self.cache[cache_key] = (time.time(), candles)
            
            print(f"Successfully fetched {len(candles)} candles from TradingView API")
            return candles
            
        except Exception as e:
            print(f"Error fetching data from TradingView API: {e}")
            return None
    
    def _convert_interval(self, interval):
        """Convert our interval format to TradingView format"""
        interval_map = {
            '1m': '1',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '1h': '60',
            '4h': '240',
            '1d': '1D'
        }
        return interval_map.get(interval, '1')
    
    def _process_tradingview_data(self, data, interval):
        """Process TradingView data into our candle format"""
        candles = []
        
        # Get current time for timestamp calculation
        current_time = datetime.now()
        
        # Calculate interval in seconds
        interval_seconds = self._interval_to_seconds(interval)
        
        for i, item in enumerate(data):
            try:
                # Extract values
                values = item.get('d', [])
                if len(values) < 4:
                    continue
                
                close = float(values[0])
                open_price = float(values[1])
                high = float(values[2])
                low = float(values[3])
                volume = int(values[4]) if len(values) > 4 else 100
                
                # Calculate timestamp (most recent candle is last)
                candle_time = current_time - timedelta(seconds=interval_seconds * (len(data) - i - 1))
                timestamp = candle_time.strftime("%Y-%m-%d %H:%M:%S")
                unix_time = int(candle_time.timestamp())
                
                # Create candle object
                candle = {
                    'timestamp': timestamp,
                    'time': unix_time,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume,
                    'color': 'green' if close >= open_price else 'red',
                    'interval': interval,
                    'source': 'tradingview'
                }
                
                # Mark the most recent candle as in-progress
                if i == len(data) - 1:
                    candle['in_progress'] = True
                    seconds_in_current_interval = (current_time - candle_time).total_seconds()
                    candle['completion'] = min(1.0, seconds_in_current_interval / interval_seconds)
                
                candles.append(candle)
                
            except Exception as e:
                print(f"Error processing TradingView candle data: {e}")
                continue
        
        return candles
    
    def _interval_to_seconds(self, interval):
        """Convert interval string to seconds"""
        if interval == '1m':
            return 60
        elif interval == '5m':
            return 300
        elif interval == '15m':
            return 900
        elif interval == '30m':
            return 1800
        elif interval == '1h':
            return 3600
        elif interval == '4h':
            return 14400
        elif interval == '1d':
            return 86400
        else:
            return 60  # Default to 1 minute

# Alternative implementation using direct HTTP requests to TradingView
class TradingViewDirectAPI:
    """Alternative implementation using direct HTTP requests to TradingView"""
    
    def __init__(self):
        """Initialize the TradingView Direct API client"""
        self.base_url = "https://demo_feed.tradingview.com/history"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
    def fetch_eurusd_data(self, count=100, interval='1m'):
        """Fetch EURUSD data using direct HTTP requests"""
        try:
            # Convert interval to TradingView format
            resolution = self._convert_interval(interval)
            
            # Calculate time range
            end_time = int(time.time())
            start_time = end_time - (count * self._interval_to_seconds(interval))
            
            # Prepare request parameters
            params = {
                'symbol': 'EURUSD',
                'resolution': resolution,
                'from': start_time,
                'to': end_time,
                'countback': count
            }
            
            # Make API request
            response = requests.get(self.base_url, headers=self.headers, params=params)
            
            if response.status_code != 200:
                print(f"Error fetching data from TradingView Direct API: {response.status_code}")
                return None
            
            # Parse response
            data = response.json()
            
            # Process the data into candles
            candles = self._process_direct_api_data(data, interval)
            
            return candles
            
        except Exception as e:
            print(f"Error fetching data from TradingView Direct API: {e}")
            return None
    
    def _convert_interval(self, interval):
        """Convert our interval format to TradingView format"""
        interval_map = {
            '1m': '1',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '1h': '60',
            '4h': '240',
            '1d': 'D'
        }
        return interval_map.get(interval, '1')
    
    def _process_direct_api_data(self, data, interval):
        """Process TradingView Direct API data into our candle format"""
        candles = []
        
        # Check if we have all required fields
        required_fields = ['t', 'o', 'h', 'l', 'c', 'v']
        if not all(field in data for field in required_fields):
            print(f"Missing required fields in TradingView Direct API response")
            return candles
        
        # Process each candle
        for i in range(len(data['t'])):
            try:
                # Extract values
                unix_time = data['t'][i]
                open_price = data['o'][i]
                high = data['h'][i]
                low = data['l'][i]
                close = data['c'][i]
                volume = data['v'][i]
                
                # Convert timestamp
                candle_time = datetime.fromtimestamp(unix_time)
                timestamp = candle_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # Create candle object
                candle = {
                    'timestamp': timestamp,
                    'time': unix_time,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close,
                    'volume': volume,
                    'color': 'green' if close >= open_price else 'red',
                    'interval': interval,
                    'source': 'tradingview_direct'
                }
                
                # Mark the most recent candle as in-progress
                if i == len(data['t']) - 1:
                    current_time = datetime.now()
                    candle['in_progress'] = True
                    seconds_in_current_interval = (current_time - candle_time).total_seconds()
                    candle['completion'] = min(1.0, seconds_in_current_interval / self._interval_to_seconds(interval))
                
                candles.append(candle)
                
            except Exception as e:
                print(f"Error processing TradingView Direct API candle data: {e}")
                continue
        
        return candles
    
    def _interval_to_seconds(self, interval):
        """Convert interval string to seconds"""
        if interval == '1m':
            return 60
        elif interval == '5m':
            return 300
        elif interval == '15m':
            return 900
        elif interval == '30m':
            return 1800
        elif interval == '1h':
            return 3600
        elif interval == '4h':
            return 14400
        elif interval == '1d':
            return 86400
        else:
            return 60  # Default to 1 minute
