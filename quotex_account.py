#!/usr/bin/env python
import os
import sys
import time
import asyncio
import pyfiglet
from quotexapi.stable_api import Quotex
from quotexapi.utils.processor import process_candles, get_color

# ASCII art banner
custom_font = pyfiglet.Figlet(font="slant")
ascii_art = custom_font.renderText("Quotex API")
print(ascii_art)
print("Developed by: Your Name")
print("Based on PyQuotex by Cleiton Leonel Creton")
print("-" * 60)

# Load credentials from config.ini
client = Quotex(
    lang="en",  # Use English language
)

async def select_account():
    """Select between demo and real accounts"""
    account_type = input("Select account type (demo or real): ").lower()

    if account_type == "real":
        print("Real account selected")
        client.set_account_mode("REAL")
    else:
        print("Demo account selected")
        client.set_account_mode("PRACTICE")  # Default is PRACTICE/DEMO

    return account_type

async def get_account_info():
    """Get account information"""
    print("\nConnecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully.")

        # Get profile information
        profile = await client.get_profile()
        print(f"\nUser: {profile.nick_name}")
        print(f"Demo Balance: {profile.demo_balance}")
        print(f"Real Balance: {profile.live_balance}")
        print(f"ID: {profile.profile_id}")
        print(f"Country: {profile.country_name}")

        # Get current balance
        balance = await client.get_balance()
        print(f"\nCurrent Balance: {balance}")

        # Get available assets
        print("\nChecking available assets...")
        all_data = client.get_payment()

        # Count open assets
        open_assets = sum(1 for asset_data in all_data.values() if asset_data["open"])
        total_assets = len(all_data)

        print(f"Open assets: {open_assets}/{total_assets}")

        # Show some open assets as examples
        print("\nSome open assets (examples):")
        count = 0
        for asset_name, asset_data in all_data.items():
            if asset_data["open"] and count < 5:
                profit = f'Profit: {asset_data["profit"]["1M"]}%'
                print(f"- {asset_name} {profit}")
                count += 1
    else:
        print(f"Connection failed: {message}")

    print("\nExiting...")
    await client.close()

async def capture_candles(asset, period=60, count=10):
    """Capture and display recent candles for an asset"""
    print(f"\nCapturing recent candles for {asset}...")
    check_connect, message = await client.connect()

    if check_connect:
        # Get current time
        end_from_time = time.time()

        # Retrieve candles
        candles = await client.get_candles(asset, end_from_time, 3600, period)

        if len(candles) > 0:
            # Process candles if needed
            if not candles[0].get("open"):
                candles = process_candles(candles, period)

            # Display the most recent candles
            print(f"\nMost recent {min(count, len(candles))} candles for {asset}:")
            print("-" * 60)
            print(f"{'Time':<20} {'Open':<10} {'Close':<10} {'High':<10} {'Low':<10} {'Color'}")
            print("-" * 60)

            for candle in candles[-count:]:
                color = get_color(candle)
                time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(candle['time']))
                print(f"{time_str:<20} {candle['open']:<10.5f} {candle['close']:<10.5f} {candle['high']:<10.5f} {candle['low']:<10.5f} {color}")
        else:
            print("No candles retrieved.")
    else:
        print(f"Connection failed: {message}")

    await client.close()

async def main_menu():
    """Display main menu and handle user choices"""
    account_type = await select_account()

    while True:
        print("\n" + "=" * 60)
        print("MAIN MENU")
        print("=" * 60)
        print("1. View account information")
        print("2. Capture recent candles (EURUSD)")
        print("3. Capture recent candles (USDBRL/OTC)")
        print("4. List available assets")
        print("5. Exit")

        choice = input("\nChoose an option (1-5): ")

        if choice == "1":
            await get_account_info()
        elif choice == "2":
            await capture_candles("EURUSD", 60, 10)
        elif choice == "3":
            await capture_candles("BRLUSD_otc", 60, 10)
        elif choice == "4":
            # Get all assets
            print("\nConnecting to Quotex API...")
            check_connect, message = await client.connect()

            if check_connect:
                print("Connected successfully. Retrieving assets...")

                # Get all assets
                all_data = client.get_payment()

                # Print open assets
                print("\nOpen assets:")
                for asset_name, asset_data in all_data.items():
                    if asset_data["open"]:
                        profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
                        print(f"{asset_name} {profit}")

                await client.close()
            else:
                print(f"Connection failed: {message}")
        elif choice == "5":
            print("\nExiting the program. Goodbye!")
            break
        else:
            print("\nInvalid option. Please choose an option from 1 to 5.")

async def main():
    try:
        await main_menu()
    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user.")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
