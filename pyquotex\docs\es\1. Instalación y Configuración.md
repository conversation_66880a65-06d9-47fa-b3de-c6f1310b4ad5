# Instalación y Configuración de PyQuotex

## Tabla de Contenidos
- [Requisitos del Sistema](#requisitos-del-sistema)
- [Instalación](#instalación)
  - [Desde pip](#desde-pip)
  - [Desde <PERSON>it<PERSON>](#desde-github)
- [Configuración Inicial](#configuración-inicial)
- [Manejo de Credenciales](#manejo-de-credenciales)
- [Configuración de SSL/TLS](#configuración-de-ssltls)

## Requisitos del Sistema

Para utilizar PyQuotex, necesitarás:

- Python 3.8 o superior
- OpenSSL en su versión más reciente
- Sistema operativo compatible:
  - Linux
  - Windows
  - macOS

### Dependencias Principales
```
websocket-client>=1.8.0
requests>=2.31.0
beautifulsoup4>=4.12.2
```

### Dependencias Opcionales
```
playwright>=1.44.0
numpy>=2.2.3,<3.0.0
playwright-stealth>=1.0.6,<2.0.0
```

## Instalación y Poetry

### Desde pip
Puedes instalar PyQuotex directamente desde GitHub usando pip:

```bash
pip install git+https://github.com/cleitonleonel/pyquotex.git
```

### Desde GitHub
También puedes clonar el repositorio y realizar una instalación local:

- Primero [Instalar Poetry](https://python-poetry.org/docs/#installing-with-the-official-installer)

```bash
git clone https://github.com/cleitonleonel/pyquotex.git
cd pyquotex
poetry install
```

### Instalación de Navegadores para Playwright
Después de instalar PyQuotex, necesitas instalar los navegadores necesarios para Playwright:

```bash
playwright install
```

## Configuración Inicial

Para comenzar a usar PyQuotex, primero debes importar y configurar el cliente:

```python
from quotexapi.stable_api import Quotex

client = Quotex(
    email="<EMAIL>",
    password="tu_contraseña",
    lang="es"  # Idioma por defecto: "pt" (portugués)
)

# Habilitar modo debug (opcional)
client.debug_ws_enable = True
```

## Manejo de Credenciales

Hay dos formas principales de manejar las credenciales:

### 1. Archivo de Configuración
PyQuotex buscará automáticamente un archivo `config.ini` en la carpeta `settings`. Si no existe, lo creará solicitando las credenciales:

```ini
[settings]
email=<EMAIL>
password=tu_contraseña
```

### 2. Configuración Directa
Puedes proporcionar las credenciales directamente al crear la instancia del cliente:

```python
cliente = Quotex(
    email="<EMAIL>",
    password="tu_contraseña"
)
```

## Configuración de SSL/TLS

### Windows
Para Windows, es necesario instalar la última versión de OpenSSL:
1. Descarga el instalador desde [Openssl-Windows](https://slproweb.com/products/Win32OpenSSL.html)
2. Instala siguiendo las instrucciones del instalador

### Linux
En sistemas Linux, actualiza OpenSSL usando el gestor de paquetes:

```bash
sudo apt update
sudo apt install openssl
```

### Configuración SSL en el Código
PyQuotex maneja automáticamente la configuración SSL, pero puedes personalizarla:

```python
import ssl
import certifi

# Configuración del contexto SSL para usar TLS 1.3
ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1 | ssl.OP_NO_TLSv1_2
ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
ssl_context.load_verify_locations(certifi.where())
```

### Manejo de Certificados
PyQuotex utiliza certificados SSL para conexiones seguras:

```python
import os
import certifi

# Configurar la ruta del certificado
cert_path = os.path.join("../", "quotex.pem")
os.environ['SSL_CERT_FILE'] = cert_path
os.environ['WEBSOCKET_CLIENT_CA_BUNDLE'] = cert_path
```

---

Para más información y soporte, puedes unirte al [grupo de Telegram](https://t.me/+Uzcmc-NZvN4xNTQx) de la comunidad.