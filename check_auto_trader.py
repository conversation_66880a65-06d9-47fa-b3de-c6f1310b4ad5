import os
import sys

print("Checking auto_trader.py initialization...")

# Import tensorflow_check
try:
    from tensorflow_check import is_tensorflow_available, setup_tensorflow
    
    # Check if TensorFlow is available
    tf_available = is_tensorflow_available()
    print(f"TensorFlow available: {tf_available}")
    
    # Import model manager
    from Models.model_manager import Model<PERSON>anager, TENSORFLOW_AVAILABLE
    print(f"TENSORFLOW_AVAILABLE in model_manager: {TENSORFLOW_AVAILABLE}")
    
    # Create model manager
    model_manager = ModelManager(model_dir='models')
    
    # Load models
    print("\nLoading models...")
    loaded_models = model_manager.load_models()
    
    # Print loaded models
    print("\nLoaded models:")
    for model_name in loaded_models:
        print(f"- {model_name}")
    
    # Print available models for selection
    print("\nAvailable models for selection:")
    if 'lstm_gru' in loaded_models:
        print("1. LSTM-GRU Deep Learning (loaded)")
    else:
        print("1. LSTM-GRU Deep Learning (not loaded)")
        
    if 'transformer' in loaded_models:
        print("2. Transformer (loaded)")
    else:
        print("2. Transformer (not loaded)")
        
    if 'xgboost' in loaded_models:
        print("3. XGBoost (loaded)")
    else:
        print("3. XGBoost (not loaded)")
        
    if 'dqn' in loaded_models:
        print("4. DQN Reinforcement Learning (loaded)")
    else:
        print("4. DQN Reinforcement Learning (not loaded)")
    
    if hasattr(model_manager, 'ensemble') and model_manager.ensemble is not None:
        print("5. Ensemble (loaded)")
    else:
        print("5. Ensemble (not loaded)")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("\nCheck complete.")
