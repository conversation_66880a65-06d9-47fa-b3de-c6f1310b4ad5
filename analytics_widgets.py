#!/usr/bin/env python
"""
Analytics widgets for PyQuotex trading UI
Provides custom PyQt widgets for displaying model analytics and learning data
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from PyQt5 import QtCore, QtGui, QtWidgets
import pyqtgraph as pg


class ModelPerformanceWidget(QtWidgets.QWidget):
    """Widget for displaying model performance metrics"""

    def __init__(self, parent=None):
        super(ModelPerformanceWidget, self).__init__(parent)

        # Create layout
        self.layout = QtWidgets.QVBoxLayout(self)

        # Create group box for overall metrics
        self.metrics_group = QtWidgets.QGroupBox("Model Performance Metrics")
        self.metrics_layout = QtWidgets.QGridLayout()
        self.metrics_group.setLayout(self.metrics_layout)

        # Create labels for metrics
        self.create_metric_labels()

        # Add group box to layout
        self.layout.addWidget(self.metrics_group)

        # Create table for detailed metrics
        self.metrics_table = QtWidgets.QTableWidget()
        self.metrics_table.setColumnCount(5)
        self.metrics_table.setHorizontalHeaderLabels(["Model", "Accuracy", "Precision", "Recall", "F1 Score"])
        self.metrics_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)

        # Add table to layout
        self.layout.addWidget(self.metrics_table)

    def create_metric_labels(self):
        """Create labels for displaying metrics"""
        # Create labels for each metric
        metrics = [
            ("Overall Accuracy:", "overall_accuracy"),
            ("Recent Accuracy:", "recent_accuracy"),
            ("Win Rate:", "win_rate"),
            ("Profit Factor:", "profit_factor"),
            ("Improvement:", "improvement"),
            ("Learning Trend:", "learning_trend"),
            ("Trades Count:", "trades_count"),
            ("Successful Trades:", "successful_trades")
        ]

        self.metric_labels = {}

        # Add labels to grid layout
        for i, (label_text, metric_name) in enumerate(metrics):
            row = i // 2
            col = (i % 2) * 2

            # Create label
            label = QtWidgets.QLabel(label_text)
            value_label = QtWidgets.QLabel("N/A")
            value_label.setStyleSheet("font-weight: bold;")

            # Add to layout
            self.metrics_layout.addWidget(label, row, col)
            self.metrics_layout.addWidget(value_label, row, col + 1)

            # Store reference to value label
            self.metric_labels[metric_name] = value_label

    def update_metrics(self, metrics):
        """Update displayed metrics"""
        if not metrics:
            return

        # Update overall metrics
        if 'overall' in metrics and 'accuracy' in metrics['overall']:
            self.metric_labels['overall_accuracy'].setText(f"{metrics['overall']['accuracy']:.4f}")

        if 'recent' in metrics and 'accuracy' in metrics['recent']:
            self.metric_labels['recent_accuracy'].setText(f"{metrics['recent']['accuracy']:.4f}")

        if 'overall' in metrics and 'win_rate' in metrics['overall']:
            self.metric_labels['win_rate'].setText(f"{metrics['overall']['win_rate']:.2f}%")

        if 'overall' in metrics and 'profit_factor' in metrics['overall']:
            self.metric_labels['profit_factor'].setText(f"{metrics['overall']['profit_factor']:.2f}")

        if 'short_term' in metrics and 'improvement' in metrics['short_term']:
            improvement = metrics['short_term']['improvement']
            self.metric_labels['improvement'].setText(f"{improvement:.2f}%")

            # Set color based on improvement
            if improvement > 0:
                self.metric_labels['improvement'].setStyleSheet("font-weight: bold; color: green;")
            elif improvement < 0:
                self.metric_labels['improvement'].setStyleSheet("font-weight: bold; color: red;")
            else:
                self.metric_labels['improvement'].setStyleSheet("font-weight: bold;")

        if 'short_term' in metrics and 'trend' in metrics['short_term']:
            self.metric_labels['learning_trend'].setText(metrics['short_term']['trend'])

        if 'overall' in metrics and 'trades' in metrics['overall']:
            self.metric_labels['trades_count'].setText(str(metrics['overall']['trades']))

        if 'overall' in metrics and 'successful_trades' in metrics['overall']:
            self.metric_labels['successful_trades'].setText(str(metrics['overall']['successful_trades']))

        # Update model-specific metrics if available
        if 'models' in metrics:
            # Clear table
            self.metrics_table.setRowCount(0)

            # Add rows for each model
            for model_name, model_metrics in metrics['models'].items():
                row = self.metrics_table.rowCount()
                self.metrics_table.insertRow(row)

                # Add model name
                self.metrics_table.setItem(row, 0, QtWidgets.QTableWidgetItem(model_name))

                # Add metrics
                if 'accuracy' in model_metrics:
                    self.metrics_table.setItem(row, 1, QtWidgets.QTableWidgetItem(f"{model_metrics['accuracy']:.4f}"))

                if 'precision' in model_metrics:
                    self.metrics_table.setItem(row, 2, QtWidgets.QTableWidgetItem(f"{model_metrics['precision']:.4f}"))

                if 'recall' in model_metrics:
                    self.metrics_table.setItem(row, 3, QtWidgets.QTableWidgetItem(f"{model_metrics['recall']:.4f}"))

                if 'f1_score' in model_metrics:
                    self.metrics_table.setItem(row, 4, QtWidgets.QTableWidgetItem(f"{model_metrics['f1_score']:.4f}"))


class TradeHistoryWidget(QtWidgets.QWidget):
    """Widget for displaying trade history"""

    def __init__(self, parent=None):
        super(TradeHistoryWidget, self).__init__(parent)

        # Create layout
        self.layout = QtWidgets.QVBoxLayout(self)

        # Create table for trade history
        self.trade_table = QtWidgets.QTableWidget()
        self.trade_table.setColumnCount(7)
        self.trade_table.setHorizontalHeaderLabels([
            "Time", "Direction", "Entry", "Exit", "Result", "Model", "Confidence"
        ])
        self.trade_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)

        # Add table to layout
        self.layout.addWidget(self.trade_table)

    def update_trade_history(self, trades):
        """Update trade history table"""
        if not trades:
            return

        # Clear table
        self.trade_table.setRowCount(0)

        # Add rows for each trade
        for trade in trades:
            row = self.trade_table.rowCount()
            self.trade_table.insertRow(row)

            # Add trade details
            if 'timestamp' in trade:
                if isinstance(trade['timestamp'], datetime):
                    time_str = trade['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(trade['timestamp'])
                self.trade_table.setItem(row, 0, QtWidgets.QTableWidgetItem(time_str))

            if 'direction' in trade:
                direction_item = QtWidgets.QTableWidgetItem(trade['direction'])
                if trade['direction'] == 'call':
                    direction_item.setForeground(QtGui.QColor('green'))
                elif trade['direction'] == 'put':
                    direction_item.setForeground(QtGui.QColor('red'))
                self.trade_table.setItem(row, 1, direction_item)

            if 'entry_price' in trade:
                self.trade_table.setItem(row, 2, QtWidgets.QTableWidgetItem(f"{trade['entry_price']:.2f}"))

            if 'exit_price' in trade:
                self.trade_table.setItem(row, 3, QtWidgets.QTableWidgetItem(f"{trade['exit_price']:.2f}"))

            if 'result' in trade:
                result_item = QtWidgets.QTableWidgetItem(trade['result'])
                if trade['result'] == 'win':
                    result_item.setForeground(QtGui.QColor('green'))
                elif trade['result'] == 'loss':
                    result_item.setForeground(QtGui.QColor('red'))
                self.trade_table.setItem(row, 4, result_item)

            if 'model' in trade:
                self.trade_table.setItem(row, 5, QtWidgets.QTableWidgetItem(trade['model']))

            if 'confidence' in trade:
                self.trade_table.setItem(row, 6, QtWidgets.QTableWidgetItem(f"{trade['confidence']:.4f}"))


class LearningMetricsWidget(QtWidgets.QWidget):
    """Widget for displaying learning metrics and progress"""

    def __init__(self, parent=None):
        super(LearningMetricsWidget, self).__init__(parent)

        # Create layout
        self.layout = QtWidgets.QVBoxLayout(self)

        # Create group box for learning parameters
        self.params_group = QtWidgets.QGroupBox("Learning Parameters")
        self.params_layout = QtWidgets.QGridLayout()
        self.params_group.setLayout(self.params_layout)

        # Create labels for parameters
        self.create_param_labels()

        # Add group box to layout
        self.layout.addWidget(self.params_group)

        # Create progress bars for learning metrics
        self.progress_group = QtWidgets.QGroupBox("Learning Progress")
        self.progress_layout = QtWidgets.QVBoxLayout()
        self.progress_group.setLayout(self.progress_layout)

        # Create progress bars
        self.create_progress_bars()

        # Add progress group to layout
        self.layout.addWidget(self.progress_group)

        # Create model weights table
        self.weights_group = QtWidgets.QGroupBox("Model Weights")
        self.weights_layout = QtWidgets.QVBoxLayout()
        self.weights_group.setLayout(self.weights_layout)

        # Create table for model weights
        self.weights_table = QtWidgets.QTableWidget()
        self.weights_table.setColumnCount(2)
        self.weights_table.setHorizontalHeaderLabels(["Model", "Weight"])
        self.weights_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)

        # Add table to layout
        self.weights_layout.addWidget(self.weights_table)

        # Add weights group to layout
        self.layout.addWidget(self.weights_group)

    def create_param_labels(self):
        """Create labels for displaying learning parameters"""
        # Create labels for each parameter
        params = [
            ("Learning Rate:", "learning_rate"),
            ("Memory Factor:", "memory_factor"),
            ("Confidence Threshold:", "confidence_threshold"),
            ("Retraining Threshold:", "retraining_threshold"),
            ("Optimization Interval:", "optimization_interval"),
            ("Learning Iterations:", "learning_iterations")
        ]

        self.param_labels = {}

        # Add labels to grid layout
        for i, (label_text, param_name) in enumerate(params):
            row = i // 2
            col = (i % 2) * 2

            # Create label
            label = QtWidgets.QLabel(label_text)
            value_label = QtWidgets.QLabel("N/A")

            # Add to layout
            self.params_layout.addWidget(label, row, col)
            self.params_layout.addWidget(value_label, row, col + 1)

            # Store reference to value label
            self.param_labels[param_name] = value_label

    def create_progress_bars(self):
        """Create progress bars for learning metrics"""
        # Create progress bars for each metric
        metrics = [
            ("Overall Accuracy:", "overall_accuracy"),
            ("Recent Accuracy:", "recent_accuracy"),
            ("Learning Efficiency:", "learning_efficiency"),
            ("Model Adaptation:", "model_adaptation")
        ]

        self.progress_bars = {}

        # Add progress bars to layout
        for label_text, metric_name in metrics:
            # Create horizontal layout
            h_layout = QtWidgets.QHBoxLayout()

            # Create label
            label = QtWidgets.QLabel(label_text)
            label.setMinimumWidth(150)

            # Create progress bar
            progress_bar = QtWidgets.QProgressBar()
            progress_bar.setMinimum(0)
            progress_bar.setMaximum(100)
            progress_bar.setValue(0)

            # Create value label
            value_label = QtWidgets.QLabel("0.00")
            value_label.setMinimumWidth(50)

            # Add to layout
            h_layout.addWidget(label)
            h_layout.addWidget(progress_bar)
            h_layout.addWidget(value_label)

            # Add to main layout
            self.progress_layout.addLayout(h_layout)

            # Store references
            self.progress_bars[metric_name] = {
                'bar': progress_bar,
                'label': value_label
            }

    def update_learning_params(self, params):
        """Update displayed learning parameters"""
        if not params:
            return

        # Update parameter labels
        for param_name, label in self.param_labels.items():
            if param_name in params:
                if param_name in ['learning_rate', 'memory_factor', 'confidence_threshold', 'retraining_threshold']:
                    label.setText(f"{params[param_name]:.4f}")
                else:
                    label.setText(str(params[param_name]))

    def update_learning_progress(self, progress):
        """Update learning progress bars"""
        if not progress:
            return

        # Update progress bars
        for metric_name, components in self.progress_bars.items():
            if metric_name in progress:
                value = progress[metric_name]
                percentage = min(100, max(0, int(value * 100)))

                # Update progress bar
                components['bar'].setValue(percentage)

                # Update label
                components['label'].setText(f"{value:.4f}")

                # Set color based on value
                if value >= 0.8:
                    components['bar'].setStyleSheet("QProgressBar::chunk { background-color: green; }")
                elif value >= 0.6:
                    components['bar'].setStyleSheet("QProgressBar::chunk { background-color: yellow; }")
                else:
                    components['bar'].setStyleSheet("QProgressBar::chunk { background-color: red; }")

    def update_model_weights(self, weights):
        """Update model weights table"""
        if not weights:
            return

        # Clear table
        self.weights_table.setRowCount(0)

        # Filter out metadata keys and only show actual model weights
        metadata_keys = {'data_source', 'total_models', 'timestamp'}

        # Add rows for each model (excluding metadata)
        for model_name, weight in weights.items():
            # Skip metadata keys
            if model_name in metadata_keys:
                continue

            # Only add if weight is a number
            if isinstance(weight, (int, float)):
                row = self.weights_table.rowCount()
                self.weights_table.insertRow(row)

                # Add model name (capitalize first letter for display)
                display_name = model_name.replace('_', ' ').title()
                self.weights_table.setItem(row, 0, QtWidgets.QTableWidgetItem(display_name))

                # Add weight
                self.weights_table.setItem(row, 1, QtWidgets.QTableWidgetItem(f"{weight:.4f}"))

        print(f"Updated model weights table with {self.weights_table.rowCount()} models")


class PredictionDetailsWidget(QtWidgets.QWidget):
    """Widget for displaying detailed prediction information"""

    def __init__(self, parent=None):
        super(PredictionDetailsWidget, self).__init__(parent)

        # Create layout
        self.layout = QtWidgets.QVBoxLayout(self)

        # Create group box for current prediction
        self.current_group = QtWidgets.QGroupBox("Current Prediction")
        self.current_layout = QtWidgets.QGridLayout()
        self.current_group.setLayout(self.current_layout)

        # Create labels for current prediction
        self.create_current_labels()

        # Add group box to layout
        self.layout.addWidget(self.current_group)

        # Create group box for future predictions
        self.future_group = QtWidgets.QGroupBox("Future Predictions")
        self.future_layout = QtWidgets.QVBoxLayout()
        self.future_group.setLayout(self.future_layout)

        # Create table for future predictions
        self.future_table = QtWidgets.QTableWidget()
        self.future_table.setColumnCount(4)
        self.future_table.setHorizontalHeaderLabels([
            "Horizon", "Direction", "Confidence", "Model"
        ])
        self.future_table.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.Stretch)

        # Add table to layout
        self.future_layout.addWidget(self.future_table)

        # Add future group to layout
        self.layout.addWidget(self.future_group)

        # Create group box for market analysis
        self.market_group = QtWidgets.QGroupBox("Market Analysis")
        self.market_layout = QtWidgets.QGridLayout()
        self.market_group.setLayout(self.market_layout)

        # Create labels for market analysis
        self.create_market_labels()

        # Add group box to layout
        self.layout.addWidget(self.market_group)

    def create_current_labels(self):
        """Create labels for displaying current prediction"""
        # Create labels for each metric
        metrics = [
            ("Direction:", "direction"),
            ("Confidence:", "confidence"),
            ("Probability:", "probability"),
            ("Model:", "model"),
            ("Signal Strength:", "signal_strength"),
            ("Risk/Reward:", "risk_reward")
        ]

        self.current_labels = {}

        # Add labels to grid layout
        for i, (label_text, metric_name) in enumerate(metrics):
            row = i // 2
            col = (i % 2) * 2

            # Create label
            label = QtWidgets.QLabel(label_text)
            value_label = QtWidgets.QLabel("N/A")
            value_label.setStyleSheet("font-weight: bold;")

            # Add to layout
            self.current_layout.addWidget(label, row, col)
            self.current_layout.addWidget(value_label, row, col + 1)

            # Store reference to value label
            self.current_labels[metric_name] = value_label

    def create_market_labels(self):
        """Create labels for displaying market analysis"""
        # Create labels for each metric
        metrics = [
            ("Market Regime:", "market_regime"),
            ("Trend Strength:", "trend_strength"),
            ("Volatility:", "volatility"),
            ("RSI:", "rsi"),
            ("ADX:", "adx"),
            ("Support/Resistance:", "support_resistance")
        ]

        self.market_labels = {}

        # Add labels to grid layout
        for i, (label_text, metric_name) in enumerate(metrics):
            row = i // 2
            col = (i % 2) * 2

            # Create label
            label = QtWidgets.QLabel(label_text)
            value_label = QtWidgets.QLabel("N/A")

            # Add to layout
            self.market_layout.addWidget(label, row, col)
            self.market_layout.addWidget(value_label, row, col + 1)

            # Store reference to value label
            self.market_labels[metric_name] = value_label

    def update_current_prediction(self, prediction):
        """Update current prediction display"""
        if not prediction:
            return

        # Update direction with color
        if 'direction' in prediction:
            direction = prediction['direction']
            self.current_labels['direction'].setText(direction)

            if direction == 'call':
                self.current_labels['direction'].setStyleSheet("font-weight: bold; color: green;")
            elif direction == 'put':
                self.current_labels['direction'].setStyleSheet("font-weight: bold; color: red;")
            else:
                self.current_labels['direction'].setStyleSheet("font-weight: bold;")

        # Update other metrics
        if 'confidence' in prediction and prediction['confidence'] is not None:
            self.current_labels['confidence'].setText(f"{prediction['confidence']:.4f}")
        else:
            self.current_labels['confidence'].setText("N/A")

        if 'probability' in prediction and prediction['probability'] is not None:
            self.current_labels['probability'].setText(f"{prediction['probability']:.4f}")
        else:
            self.current_labels['probability'].setText("N/A")

        if 'model' in prediction:
            self.current_labels['model'].setText(str(prediction['model']))

        if 'signal_strength' in prediction and prediction['signal_strength'] is not None:
            self.current_labels['signal_strength'].setText(f"{prediction['signal_strength']:.2f}")
        else:
            self.current_labels['signal_strength'].setText("N/A")

        if 'risk_reward' in prediction and prediction['risk_reward'] is not None:
            self.current_labels['risk_reward'].setText(f"{prediction['risk_reward']:.2f}")
        else:
            self.current_labels['risk_reward'].setText("N/A")

    def update_future_predictions(self, predictions):
        """Update future predictions table"""
        if not predictions:
            return

        # Clear table
        self.future_table.setRowCount(0)

        # Add rows for each prediction
        for horizon, pred in predictions.items():
            row = self.future_table.rowCount()
            self.future_table.insertRow(row)

            # Add horizon
            self.future_table.setItem(row, 0, QtWidgets.QTableWidgetItem(f"{horizon} min"))

            # Add direction with color
            direction = pred.get('direction', 'unknown')
            direction_item = QtWidgets.QTableWidgetItem(direction)

            if direction == 'call':
                direction_item.setForeground(QtGui.QColor('green'))
            elif direction == 'put':
                direction_item.setForeground(QtGui.QColor('red'))

            self.future_table.setItem(row, 1, direction_item)

            # Add confidence
            if 'confidence' in pred and pred['confidence'] is not None:
                self.future_table.setItem(row, 2, QtWidgets.QTableWidgetItem(f"{pred['confidence']:.4f}"))
            elif 'confidence' in pred:
                self.future_table.setItem(row, 2, QtWidgets.QTableWidgetItem("N/A"))
            else:
                self.future_table.setItem(row, 2, QtWidgets.QTableWidgetItem("N/A"))

            # Add model
            if 'model' in pred:
                self.future_table.setItem(row, 3, QtWidgets.QTableWidgetItem(pred['model']))

    def update_market_analysis(self, analysis):
        """Update market analysis display"""
        if not analysis:
            return

        # Update market regime with color
        if 'market_regime' in analysis:
            regime = analysis['market_regime']
            self.market_labels['market_regime'].setText(regime)

            if regime == 'trending':
                self.market_labels['market_regime'].setStyleSheet("color: green;")
            elif regime == 'ranging':
                self.market_labels['market_regime'].setStyleSheet("color: blue;")
            elif regime == 'volatile':
                self.market_labels['market_regime'].setStyleSheet("color: red;")

        # Update other metrics
        if 'trend_strength' in analysis and analysis['trend_strength'] is not None:
            self.market_labels['trend_strength'].setText(f"{analysis['trend_strength']:.2f}")
        elif 'trend_strength' in analysis:
            self.market_labels['trend_strength'].setText("N/A")

        if 'volatility' in analysis and analysis['volatility'] is not None:
            self.market_labels['volatility'].setText(f"{analysis['volatility']:.4f}")
        elif 'volatility' in analysis:
            self.market_labels['volatility'].setText("N/A")

        if 'rsi' in analysis and analysis['rsi'] is not None:
            rsi = analysis['rsi']
            self.market_labels['rsi'].setText(f"{rsi:.2f}")

            # Color based on RSI value
            if rsi > 70:
                self.market_labels['rsi'].setStyleSheet("color: red;")  # Overbought
            elif rsi < 30:
                self.market_labels['rsi'].setStyleSheet("color: green;")  # Oversold
            else:
                self.market_labels['rsi'].setStyleSheet("")
        elif 'rsi' in analysis:
            self.market_labels['rsi'].setText("N/A")

        if 'adx' in analysis and analysis['adx'] is not None:
            adx = analysis['adx']
            self.market_labels['adx'].setText(f"{adx:.2f}")

            # Color based on ADX value
            if adx > 25:
                self.market_labels['adx'].setStyleSheet("color: green;")  # Strong trend
            else:
                self.market_labels['adx'].setStyleSheet("")
        elif 'adx' in analysis:
            self.market_labels['adx'].setText("N/A")

        if 'support_resistance' in analysis:
            self.market_labels['support_resistance'].setText(analysis['support_resistance'])

