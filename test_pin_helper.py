#!/usr/bin/env python
"""
Test script for Quotex PIN Helper
"""

import sys
import time
from PyQt5 import QtWidgets, QtCore

class PINHelperTestApp(QtWidgets.QWidget):
    """Test application for Quotex PIN Helper"""

    # Define the signals for PIN request and submit results
    pin_request_result = QtCore.pyqtSignal(bool, str)
    pin_submit_result = QtCore.pyqtSignal(bool, str)

    def __init__(self):
        super().__init__()
        # Connect the signals to the handlers
        self.pin_request_result.connect(self._handle_pin_request_result)
        self.pin_submit_result.connect(self._handle_pin_submit_result)
        self.init_ui()

    def init_ui(self):
        """Initialize the UI"""
        self.setWindowTitle("Quotex PIN Helper Test")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Create layout
        layout = QtWidgets.QVBoxLayout(self)

        # Add header
        header_label = QtWidgets.QLabel("Quotex PIN Helper Test")
        header_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3;")
        header_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(header_label)

        # Add form layout
        form_layout = QtWidgets.QFormLayout()

        # Add email field
        email_label = QtWidgets.QLabel("Email:")
        self.email_input = QtWidgets.QLineEdit()
        form_layout.addRow(email_label, self.email_input)

        # Add password field
        password_label = QtWidgets.QLabel("Password:")
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        form_layout.addRow(password_label, self.password_input)

        # Add PIN field
        pin_label = QtWidgets.QLabel("PIN Code:")
        self.pin_input = QtWidgets.QLineEdit()
        self.pin_input.setPlaceholderText("Enter PIN from email")
        form_layout.addRow(pin_label, self.pin_input)

        layout.addLayout(form_layout)

        # Add status label
        self.status_label = QtWidgets.QLabel("Ready to test")
        self.status_label.setStyleSheet("color: blue;")
        layout.addWidget(self.status_label)

        # Add console output
        self.console = QtWidgets.QTextEdit()
        self.console.setReadOnly(True)
        self.console.setStyleSheet("background-color: #000; color: #0f0; font-family: monospace;")
        layout.addWidget(self.console)

        # Add buttons
        button_layout = QtWidgets.QHBoxLayout()

        # Request PIN button
        self.request_pin_button = QtWidgets.QPushButton("Request PIN")
        self.request_pin_button.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold;")
        self.request_pin_button.clicked.connect(self.request_pin)

        # Submit PIN button
        self.submit_pin_button = QtWidgets.QPushButton("Submit PIN")
        self.submit_pin_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.submit_pin_button.clicked.connect(self.submit_pin)

        # Exit button
        self.exit_button = QtWidgets.QPushButton("Exit")
        self.exit_button.clicked.connect(self.close)

        button_layout.addWidget(self.request_pin_button)
        button_layout.addWidget(self.submit_pin_button)
        button_layout.addWidget(self.exit_button)

        layout.addLayout(button_layout)

        # Show the window
        self.show()

    def log(self, message):
        """Add a message to the console"""
        self.console.append(f"{time.strftime('%H:%M:%S')} - {message}")
        self.console.verticalScrollBar().setValue(self.console.verticalScrollBar().maximum())

    # This is a placeholder comment to maintain line numbers

    def __init__(self):
        super().__init__()
        # Connect the signals to the handlers
        self.pin_request_result.connect(self._handle_pin_request_result)
        self.pin_submit_result.connect(self._handle_pin_submit_result)
        self.init_ui()

    def request_pin(self):
        """Request a PIN from Quotex API"""
        # Get email and password
        email = self.email_input.text().strip()
        password = self.password_input.text().strip()

        if not email or not password:
            QtWidgets.QMessageBox.warning(
                self,
                "Missing Credentials",
                "Please enter both email and password to request a PIN."
            )
            return

        # Update status
        self.status_label.setText("Requesting PIN...")
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        self.log(f"Requesting PIN for {email}...")

        # Run the PIN request in a separate thread
        def run_pin_request():
            try:
                # Import the PIN helper
                import quotex_pin_helper

                # Create PIN helper
                helper = quotex_pin_helper.QuotexPINHelper(email, password)

                # Request PIN
                success, message = helper.request_pin()

                # Emit the signal with the result
                self.pin_request_result.emit(success, str(message))
            except Exception as e:
                # Handle any exceptions
                self.pin_request_result.emit(False, f"Error: {str(e)}")

        # Start the thread
        import threading
        thread = threading.Thread(target=run_pin_request)
        thread.daemon = True
        thread.start()

    def _handle_pin_request_result(self, success, message):
        """Handle the result of the PIN request"""
        if success:
            self.status_label.setText("PIN requested successfully")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.log(f"PIN request successful: {message}")

            # Show success message
            QtWidgets.QMessageBox.information(
                self,
                "PIN Requested",
                "A PIN code has been sent to your email.\n\n"
                "Please check your email and enter the PIN in the field below."
            )
        else:
            self.status_label.setText(f"Error: {message}")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.log(f"PIN request failed: {message}")

            # Show error message
            QtWidgets.QMessageBox.critical(
                self,
                "PIN Request Failed",
                f"Failed to request PIN from Quotex API.\n\n"
                f"Error: {message}\n\n"
                f"Please check your credentials and try again."
            )

    # This is a placeholder comment to maintain line numbers

    def submit_pin(self):
        """Submit a PIN to Quotex API"""
        # Get email, password and PIN
        email = self.email_input.text().strip()
        password = self.password_input.text().strip()
        pin = self.pin_input.text().strip()

        if not email or not password:
            QtWidgets.QMessageBox.warning(
                self,
                "Missing Credentials",
                "Please enter both email and password to submit a PIN."
            )
            return

        if not pin:
            QtWidgets.QMessageBox.warning(
                self,
                "Missing PIN",
                "Please enter the PIN code sent to your email."
            )
            return

        # Update status
        self.status_label.setText("Submitting PIN...")
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        self.log(f"Submitting PIN for {email}...")

        # Run the PIN submission in a separate thread
        def run_pin_submission():
            try:
                # Import the PIN helper
                import quotex_pin_helper

                # Create PIN helper
                helper = quotex_pin_helper.QuotexPINHelper(email, password)

                # Submit PIN
                success, message = helper.submit_pin(pin)

                # Emit the signal with the result
                self.pin_submit_result.emit(success, str(message))
            except Exception as e:
                # Handle any exceptions
                self.pin_submit_result.emit(False, f"Error: {str(e)}")

        # Start the thread
        import threading
        thread = threading.Thread(target=run_pin_submission)
        thread.daemon = True
        thread.start()

    def _handle_pin_submit_result(self, success, message):
        """Handle the result of the PIN submission"""
        if success:
            self.status_label.setText("PIN submitted successfully")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.log(f"PIN submission successful: {message}")

            # Show success message
            QtWidgets.QMessageBox.information(
                self,
                "PIN Submitted",
                "PIN code submitted successfully.\n\n"
                "You are now logged in to Quotex API."
            )
        else:
            self.status_label.setText(f"Error: {message}")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.log(f"PIN submission failed: {message}")

            # Show error message
            QtWidgets.QMessageBox.critical(
                self,
                "PIN Submission Failed",
                f"Failed to submit PIN to Quotex API.\n\n"
                f"Error: {message}\n\n"
                f"Please check your PIN and try again."
            )

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    ex = PINHelperTestApp()
    sys.exit(app.exec_())
