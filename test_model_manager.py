#!/usr/bin/env python
from Models.model_manager import <PERSON><PERSON><PERSON><PERSON>, TENSORFLOW_AVAILABLE

print(f"TensorFlow available: {TENSORFLOW_AVAILABLE}")

# Initialize model manager
model_manager = ModelManager(model_dir='models')

# Load models
models = model_manager.load_models()
print(f"Loaded models: {list(models.keys())}")

# Initialize self-learning
model_trainer = model_manager.initialize_self_learning()
print(f"Self-learning initialized: {model_trainer is not None}")

print("Test completed successfully!")
