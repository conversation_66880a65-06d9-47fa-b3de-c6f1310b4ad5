# Quotex Candle Data Capture

This script allows you to capture live candle data from the Quotex trading platform every minute.

## Features

- Capture live candle data every minute
- Retrieve historical data for the past 7 days (or any custom period)
- Save data to CSV files for analysis
- Support for USD/BRL (OTC) asset
- Continuous mode for long-term data collection

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Required Python packages:
  - numpy
  - websocket-client
  - requests
  - niquests
  - pyfiglet
  - beautifulsoup4

### Installation

1. Install the required packages:
   ```
   pip install numpy websocket-client requests niquests pyfiglet beautifulsoup4
   ```

2. Configure your Quotex credentials in `settings/config.ini`:
   ```
   [settings]
   email=<EMAIL>
   password=your_password
   ```

### Usage

#### Quick Start

Run one of the batch files:

1. To capture live candles with historical data (past 7 days):
```
capture_live_candles.bat
```

2. To only retrieve historical data (past 7 days):
```
get_historical_candles.bat
```

#### Command Line Options

You can also run the script directly with custom options:
```
python capture_live_candles.py --asset BRLUSD_otc --period 60 --continuous
```

Available options:
- `--asset`: Asset to capture (default: BRLUSD_otc)
- `--period`: Candle period in seconds (default: 60)
- `--duration`: Duration to capture in seconds (default: 3600 - 1 hour)
- `--output`: Output CSV file (default: asset_timestamp_candles.csv)
- `--continuous`: Run continuously until stopped
- `--historical`: Retrieve historical data before starting live capture
- `--days`: Number of days of historical data to retrieve (default: 7)

## Data Format

The CSV file contains the following columns:
- `timestamp`: Human-readable date and time
- `time`: Unix timestamp
- `open`: Opening price of the candle
- `high`: Highest price during the candle period
- `low`: Lowest price during the candle period
- `close`: Closing price of the candle
- `color`: Whether the candle is green (price went up), red (price went down), or gray (no change)
- `ticks`: Number of price updates during the candle period

## Examples

### Capture EUR/USD candles for 1 hour
```
python capture_live_candles.py --asset EURUSD_otc --duration 3600
```

### Capture USD/BRL (OTC) candles continuously
```
python capture_live_candles.py --asset BRLUSD_otc --continuous
```

### Capture 5-minute candles
```
python capture_live_candles.py --asset BRLUSD_otc --period 300 --continuous
```

### Retrieve 14 days of historical data and then capture live candles
```
python capture_live_candles.py --asset BRLUSD_otc --historical --days 14 --continuous
```

### Only retrieve 30 days of historical data (no live capture)
```
python capture_live_candles.py --asset BRLUSD_otc --historical --days 30
```
