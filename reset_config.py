#!/usr/bin/env python
"""
Reset config.ini file
This script completely resets the config.ini file and creates a new one with user-provided credentials
"""

import os
import sys
import configparser
import shutil

def reset_config_file():
    """Reset config.ini file"""
    print("=== Reset Config File ===")
    
    # Check if config.ini exists
    if os.path.exists('config.ini'):
        # Backup the old file
        backup_file = 'config.ini.old'
        try:
            shutil.copy2('config.ini', backup_file)
            print(f"Backed up old config file to {backup_file}")
        except Exception as backup_error:
            print(f"Warning: Could not backup old config file: {backup_error}")
        
        # Remove the old file
        try:
            os.remove('config.ini')
            print("Removed old config file")
        except Exception as remove_error:
            print(f"Warning: Could not remove old config file: {remove_error}")
    
    # Get new credentials
    print("\nPlease enter your Quotex credentials:")
    email = input("Email: ")
    
    # Get password without echoing
    import getpass
    password = getpass.getpass("Password: ")
    
    if not email or not password:
        print("Error: Email and password are required")
        return False
    
    # Create new config file
    config = configparser.ConfigParser(interpolation=None)
    config['credentials'] = {
        'email': email,
        'password': password
    }
    
    # Write new config file
    try:
        with open('config.ini', 'w') as f:
            config.write(f)
        print("\nNew config file created successfully")
        return True
    except Exception as e:
        print(f"\nError creating new config file: {e}")
        return False

if __name__ == "__main__":
    if reset_config_file():
        print("\nConfig file has been reset. You can now run the application.")
    else:
        print("\nFailed to reset config file. Please try again.")
