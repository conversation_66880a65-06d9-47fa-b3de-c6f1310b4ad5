import os
import csv
import time
import asyncio
import argparse
from datetime import datetime
from quotexapi.stable_api import Quotex
from quotexapi.utils.processor import process_candles, get_color

# Parse command line arguments
parser = argparse.ArgumentParser(description='Capture candle data from Quotex API')
parser.add_argument('--asset', type=str, default="EURUSD_otc", help='Asset to capture (default: EURUSD_otc)')
parser.add_argument('--period', type=int, default=60, help='Candle period in seconds (default: 60)')
parser.add_argument('--offset', type=int, default=3600, help='Time offset in seconds (default: 3600)')
parser.add_argument('--output', type=str, default="candles.csv", help='Output CSV file (default: candles.csv)')
parser.add_argument('--realtime', action='store_true', help='Capture real-time candles')
parser.add_argument('--duration', type=int, default=300, help='Duration to capture real-time candles in seconds (default: 300)')
parser.add_argument('--list-assets', action='store_true', help='List all available assets')
parser.add_argument('--search', type=str, help='Search for assets containing this string')
args = parser.parse_args()

# Load credentials from config.ini
client = Quotex(
    lang="en",  # Use English language
)

async def capture_historical_candles():
    """Capture historical candle data and save to CSV"""
    print(f"Connecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print(f"Connected successfully. Retrieving candle data for {args.asset}...")

        # Get current time
        end_from_time = time.time()

        # Retrieve candles
        candles = await client.get_candles(args.asset, end_from_time, args.offset, args.period)

        if len(candles) > 0:
            # Process candles if needed
            if not candles[0].get("open"):
                candles = process_candles(candles, args.period)

            # Add color information
            for candle in candles:
                candle['color'] = get_color(candle)

            # Save to CSV
            save_to_csv(candles, args.output)

            print(f"Successfully captured {len(candles)} candles and saved to {args.output}")
        else:
            print("No candles retrieved.")
    else:
        print(f"Connection failed: {message}")

    print("Exiting...")
    await client.close()

async def capture_realtime_candles():
    """Capture real-time candle data and save to CSV"""
    print(f"Connecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print(f"Connected successfully. Retrieving real-time candle data for {args.asset}...")

        # Get asset information
        asset_name, asset_data = await client.get_available_asset(args.asset, force_open=True)

        if asset_data[2]:  # Check if asset is open
            print(f"Asset {asset_name} is open. Starting real-time capture for {args.duration} seconds...")

            start_time = time.time()
            end_time = start_time + args.duration
            all_candles = []

            while time.time() < end_time:
                try:
                    # Get real-time candles
                    candles = await client.get_realtime_candles(asset_name, args.period)

                    # Convert dictionary to list if needed
                    if isinstance(candles, dict):
                        candle_list = []
                        for timestamp, data in candles.items():
                            data['timestamp'] = timestamp
                            candle_list.append(data)
                        candles = candle_list

                    # If we got a single candle as a dictionary, convert to list
                    if isinstance(candles, dict):
                        candles = [candles]

                    # Add color information
                    for candle in candles:
                        if 'open' in candle and 'close' in candle:
                            candle['color'] = get_color(candle)

                    # Add to our collection
                    all_candles.extend(candles)

                    # Print progress
                    remaining = int(end_time - time.time())
                    print(f"\rCapturing... {remaining} seconds remaining. Candles: {len(all_candles)}", end="")

                    # Wait a bit before next capture
                    await asyncio.sleep(1)

                except Exception as e:
                    print(f"\nError capturing candle: {e}")
                    await asyncio.sleep(1)

            print("\nCapture complete.")

            # Save to CSV
            if all_candles:
                # Add timestamp to filename to avoid overwriting
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"realtime_{args.asset}_{timestamp}.csv"
                save_to_csv(all_candles, output_file)
                print(f"Successfully captured {len(all_candles)} candles and saved to {output_file}")
            else:
                print("No candles captured.")
        else:
            print(f"Asset {asset_name} is closed.")
    else:
        print(f"Connection failed: {message}")

    print("Exiting...")
    await client.close()

def save_to_csv(candles, filename):
    """Save candle data to CSV file"""
    # Get all possible fields from candles
    all_fields = set()
    for candle in candles:
        all_fields.update(candle.keys())

    # Ensure essential fields are first in the CSV
    essential_fields = ['timestamp', 'time', 'open', 'high', 'low', 'close', 'color']
    fieldnames = [f for f in essential_fields if f in all_fields]
    fieldnames.extend([f for f in all_fields if f not in essential_fields])

    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(candles)

async def list_assets():
    """List all available assets or search for specific assets"""
    print("Connecting to Quotex API...")
    check_connect, message = await client.connect()

    if check_connect:
        print("Connected successfully. Retrieving assets...")

        # Get all assets
        all_data = client.get_payment()

        # Get all asset codes
        codes_asset = await client.get_all_assets()

        # Create a mapping from display name to code
        asset_codes = {}
        for code, name in codes_asset.items():
            asset_codes[name] = code

        if args.search:
            search_term = args.search.upper()
            print(f"\nAssets containing '{args.search}':")
            found = False
            for asset_name in all_data:
                if search_term in asset_name.upper():
                    asset_data = all_data[asset_name]
                    profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
                    status = " ==> Opened" if asset_data["open"] else " ==> Closed"
                    code = asset_codes.get(asset_name, "Unknown")
                    print(f"{asset_name} (Code: {code}) {status} {profit}")
                    found = True

            if not found:
                print(f"No assets found containing '{args.search}'")
        else:
            # Print all assets
            print("\nAll available assets:")
            for asset_name in all_data:
                asset_data = all_data[asset_name]
                profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
                status = " ==> Opened" if asset_data["open"] else " ==> Closed"
                code = asset_codes.get(asset_name, "Unknown")
                print(f"{asset_name} (Code: {code}) {status} {profit}")
    else:
        print(f"Connection failed: {message}")

    print("Exiting...")
    await client.close()

async def main():
    if args.list_assets or args.search:
        await list_assets()
    elif args.realtime:
        await capture_realtime_candles()
    else:
        await capture_historical_candles()

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("\nCapture interrupted by user.")
    finally:
        loop.close()
