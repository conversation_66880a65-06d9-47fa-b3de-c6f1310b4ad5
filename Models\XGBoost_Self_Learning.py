#!/usr/bin/env python
"""
XGBoost Self-Learning Module

This module provides self-learning capabilities for XGBoost models,
allowing the system to improve accuracy after every trade without requiring TensorFlow.
"""

import os
import time
import numpy as np
import pandas as pd
from datetime import datetime
import json
import joblib
import threading
from collections import deque
import random
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class XGBoostSelfLearner:
    """
    Self-learning system for XGBoost models that improves accuracy after every trade
    """
    
    def __init__(self, feature_engineer=None,
                 adaptation_rate=0.25, confidence_threshold=0.65, memory_factor=0.7,
                 retraining_threshold=0.6, optimization_interval=5, feedback_window=10):
        """
        Initialize the XGBoost self-learner
        
        Parameters:
        - feature_engineer: Function for engineering features
        - adaptation_rate: Rate at which weights adapt to new performance (0-1)
        - confidence_threshold: Threshold for high-confidence predictions
        - memory_factor: Factor for weighting recent vs historical performance (0-1)
        - retraining_threshold: Accuracy threshold below which models are retrained
        - optimization_interval: Number of iterations between hyperparameter optimizations
        - feedback_window: Number of recent trades to use for feedback learning
        """
        self.feature_engineer = feature_engineer
        self.xgb_model = None
        self.feature_importance = None
        self.selected_features = None
        self.performance_history = []
        
        # Self-learning parameters
        self.adaptation_rate = adaptation_rate
        self.confidence_threshold = confidence_threshold
        self.memory_factor = memory_factor
        self.retraining_threshold = retraining_threshold
        self.optimization_interval = optimization_interval
        self.feedback_window = feedback_window
        
        # Enhanced tracking variables
        self.prediction_feedback = deque(maxlen=1000)  # Use deque with max length
        self.trade_history = deque(maxlen=500)
        self.market_conditions = deque(maxlen=100)
        self.model_version = 1
        self.learning_iterations = 0
        self.last_retraining_time = datetime.now()
        
        # Performance metrics
        self.accuracy_history = []
        self.error_patterns = {}
        self.hyperparameters = {}
        self.hyperparameter_history = []
        
        # Learning flags
        self.force_retraining = False
        self.learning_in_progress = False
        self._exit_learning_loop = False
        
        # Create model directory
        os.makedirs('models', exist_ok=True)
        os.makedirs('models/snapshots', exist_ok=True)
        
        # Initialize error pattern detection
        self.initialize_error_patterns()
    
    def initialize_error_patterns(self):
        """Initialize error pattern detection system"""
        self.error_patterns = {
            'consecutive_up_errors': 0,
            'consecutive_down_errors': 0,
            'high_volatility_errors': 0,
            'low_volatility_errors': 0,
            'trend_reversal_errors': 0,
            'pattern_weights': {
                'consecutive_direction': 1.0,
                'volatility': 0.8,
                'trend_reversal': 1.2
            }
        }
    
    def initial_training(self, data):
        """
        Perform initial training of XGBoost model
        
        Parameters:
        - data: DataFrame with features for training
        
        Returns:
        - models: Dictionary with trained XGBoost model
        """
        print("Starting initial training of XGBoost model...")
        
        # Feature engineering if needed
        if self.feature_engineer and not 'rsi_14' in data.columns:
            print("Performing feature engineering...")
            featured_data = self.feature_engineer(data)
        else:
            featured_data = data
        
        # Define features for model
        feature_columns = [col for col in featured_data.columns
                          if col not in ['target_1min', 'target_5min', 'timestamp']]
        
        print(f"Using {len(feature_columns)} features for model training")
        
        # Train XGBoost model
        try:
            from Models.XGBoost_Model import train_xgboost_model
            
            print("Training XGBoost model...")
            xgb_model, xgb_feature_importance, xgb_evaluation, xgb_selected_features = train_xgboost_model(
                featured_data,
                feature_columns,
                optimize_hyperparams=True,
                feature_selection=True
            )
            
            # Store model and metadata
            self.xgb_model = xgb_model
            self.feature_importance = xgb_feature_importance
            self.selected_features = xgb_selected_features
            self.hyperparameters = xgb_model.get_params()
            
            # Save initial hyperparameters
            self.hyperparameter_history.append({
                'timestamp': datetime.now().isoformat(),
                'version': self.model_version,
                'hyperparameters': self.hyperparameters,
                'evaluation': xgb_evaluation
            })
            
            # Save model
            model_path = os.path.join('models', 'xgboost_model.pkl')
            joblib.dump(xgb_model, model_path)
            
            # Save feature importance
            feature_importance_path = os.path.join('models', 'xgboost_feature_importance.pkl')
            joblib.dump(xgb_feature_importance, feature_importance_path)
            
            # Save selected features
            selected_features_path = os.path.join('models', 'xgboost_selected_features.pkl')
            joblib.dump(xgb_selected_features, selected_features_path)
            
            print(f"XGBoost model trained and saved to {model_path}")
            
            # Return models dictionary
            models = {
                'xgboost': {
                    'model': xgb_model,
                    'feature_importance': xgb_feature_importance,
                    'selected_features': xgb_selected_features,
                    'evaluation': xgb_evaluation,
                    'version': self.model_version,
                    'last_trained': datetime.now().isoformat()
                }
            }
            
            return models
        
        except Exception as e:
            print(f"Error training XGBoost model: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def learn_from_trade(self, trade_data, was_successful):
        """
        Learn from a completed trade
        
        Parameters:
        - trade_data: Dictionary with trade details
        - was_successful: Whether the trade was successful
        
        Returns:
        - bool: Whether learning was applied
        """
        if self.learning_in_progress:
            print("Learning already in progress, queueing this trade for later learning")
            self.trade_history.append((trade_data, was_successful))
            return False
        
        print(f"\n{'='*50}")
        print(f"LEARNING FROM {'SUCCESSFUL' if was_successful else 'FAILED'} TRADE")
        print(f"{'='*50}")
        
        try:
            self.learning_in_progress = True
            
            # Extract prediction details
            prediction_details = trade_data.get('prediction_details', {})
            direction = prediction_details.get('current_prediction', {}).get('direction')
            confidence = prediction_details.get('current_prediction', {}).get('confidence', 0.5)
            
            # Convert direction to binary for learning
            prediction = 1 if direction == "UP" else 0
            actual = 1 if was_successful == (direction == "UP") else 0
            
            print(f"Direction: {direction}, Confidence: {confidence:.4f}")
            print(f"Prediction: {prediction}, Actual: {actual}")
            
            # Record feedback for self-learning
            self.record_prediction_feedback(prediction, actual, confidence, 
                                           timestamp=trade_data.get('timestamp'),
                                           prediction_details=prediction_details)
            
            # Store trade in history
            self.trade_history.append((trade_data, was_successful))
            
            # Check for error patterns
            self.detect_error_patterns(trade_data, was_successful)
            
            # Check if we need to trigger special learning
            if self.should_trigger_special_learning():
                print("Triggering special learning due to error patterns...")
                self.special_learning_from_mistakes()
            
            # Check if we need to retrain the model
            if len(self.accuracy_history) >= 10:
                recent_accuracy = sum(self.accuracy_history[-10:]) / 10
                if recent_accuracy < self.retraining_threshold:
                    print(f"Recent accuracy ({recent_accuracy:.4f}) below threshold ({self.retraining_threshold:.4f})")
                    print("Scheduling model retraining...")
                    self.force_retraining = True
            
            self.learning_iterations += 1
            print(f"Learning iteration {self.learning_iterations} completed")
            
            return True
        
        except Exception as e:
            print(f"Error in learn_from_trade: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            self.learning_in_progress = False
    
    def detect_error_patterns(self, trade_data, was_successful):
        """
        Detect patterns in trading errors to improve learning
        
        Parameters:
        - trade_data: Dictionary with trade details
        - was_successful: Whether the trade was successful
        """
        if was_successful:
            # Reset error counters on success
            self.error_patterns['consecutive_up_errors'] = 0
            self.error_patterns['consecutive_down_errors'] = 0
            return
        
        # Extract prediction details
        prediction_details = trade_data.get('prediction_details', {})
        direction = prediction_details.get('current_prediction', {}).get('direction')
        
        # Track consecutive direction errors
        if direction == "UP":
            self.error_patterns['consecutive_up_errors'] += 1
            self.error_patterns['consecutive_down_errors'] = 0
        else:
            self.error_patterns['consecutive_down_errors'] += 1
            self.error_patterns['consecutive_up_errors'] = 0
        
        # Check for volatility-related errors
        market_conditions = prediction_details.get('market_conditions', {})
        volatility = market_conditions.get('volatility', 0)
        
        if volatility > 0.02:  # High volatility
            self.error_patterns['high_volatility_errors'] += 1
        elif volatility < 0.005:  # Low volatility
            self.error_patterns['low_volatility_errors'] += 1
        
        # Check for trend reversal errors
        if len(self.trade_history) >= 2:
            prev_trade, prev_success = self.trade_history[-1]
            prev_direction = prev_trade.get('prediction_details', {}).get('current_prediction', {}).get('direction')
            
            if prev_direction != direction and not prev_success and not was_successful:
                self.error_patterns['trend_reversal_errors'] += 1
                print(f"Detected trend reversal error pattern: {prev_direction} → {direction}")
        
        # Print error pattern summary
        print("\nError Pattern Summary:")
        print(f"Consecutive UP errors: {self.error_patterns['consecutive_up_errors']}")
        print(f"Consecutive DOWN errors: {self.error_patterns['consecutive_down_errors']}")
        print(f"High volatility errors: {self.error_patterns['high_volatility_errors']}")
        print(f"Low volatility errors: {self.error_patterns['low_volatility_errors']}")
        print(f"Trend reversal errors: {self.error_patterns['trend_reversal_errors']}")
    
    def should_trigger_special_learning(self):
        """
        Determine if special learning should be triggered based on error patterns
        
        Returns:
        - bool: Whether to trigger special learning
        """
        # Check for consecutive direction errors
        if (self.error_patterns['consecutive_up_errors'] >= 2 or 
            self.error_patterns['consecutive_down_errors'] >= 2):
            return True
        
        # Check for high volatility errors
        if self.error_patterns['high_volatility_errors'] >= 3:
            return True
        
        # Check for trend reversal errors
        if self.error_patterns['trend_reversal_errors'] >= 2:
            return True
        
        return False
    
    def special_learning_from_mistakes(self, consecutive_losses=None):
        """
        Apply special learning techniques when consecutive losses are detected
        
        Parameters:
        - consecutive_losses: Number of consecutive losses (optional)
        
        Returns:
        - bool: Whether special learning was applied
        """
        print(f"\n{'='*50}")
        print(f"SPECIAL LEARNING FROM MISTAKES")
        print(f"{'='*50}")
        
        try:
            # Get recent trades for analysis
            recent_trades = list(self.trade_history)[-self.feedback_window:]
            if not recent_trades:
                print("No trade history available for special learning")
                return False
            
            # Analyze error patterns
            up_errors = self.error_patterns['consecutive_up_errors']
            down_errors = self.error_patterns['consecutive_down_errors']
            
            print(f"Analyzing error patterns: UP errors={up_errors}, DOWN errors={down_errors}")
            
            # Determine if there's a directional bias
            direction_bias = None
            if up_errors >= 2:
                direction_bias = "UP"
                print(f"Detected directional bias: System is incorrectly predicting UP")
            elif down_errors >= 2:
                direction_bias = "DOWN"
                print(f"Detected directional bias: System is incorrectly predicting DOWN")
            
            # Extract features from recent trades for targeted learning
            successful_trades = [(t, s) for t, s in recent_trades if s]
            failed_trades = [(t, s) for t, s in recent_trades if not s]
            
            print(f"Recent trades: {len(recent_trades)} total, {len(successful_trades)} successful, {len(failed_trades)} failed")
            
            # Schedule model retraining with adjusted hyperparameters
            if failed_trades:
                print("Scheduling model retraining with adjusted hyperparameters...")
                self.force_retraining = True
                
                # Adjust hyperparameters based on error patterns
                if direction_bias == "UP":
                    print("Adjusting hyperparameters to correct UP bias...")
                    # Adjust class weights to give more importance to DOWN predictions
                    self.hyperparameters['scale_pos_weight'] = self.hyperparameters.get('scale_pos_weight', 1.0) * 0.8
                elif direction_bias == "DOWN":
                    print("Adjusting hyperparameters to correct DOWN bias...")
                    # Adjust class weights to give more importance to UP predictions
                    self.hyperparameters['scale_pos_weight'] = self.hyperparameters.get('scale_pos_weight', 1.0) * 1.2
                
                # Adjust learning rate based on error patterns
                if self.error_patterns['high_volatility_errors'] >= 3:
                    print("Adjusting hyperparameters for high volatility conditions...")
                    # Reduce learning rate for more stable predictions in volatile markets
                    self.hyperparameters['learning_rate'] = self.hyperparameters.get('learning_rate', 0.1) * 0.8
                    # Increase regularization to prevent overfitting
                    self.hyperparameters['reg_lambda'] = self.hyperparameters.get('reg_lambda', 1.0) * 1.2
                
                # Save adjusted hyperparameters
                self.hyperparameter_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'version': self.model_version + 1,
                    'hyperparameters': self.hyperparameters,
                    'reason': f"Special learning from mistakes: {direction_bias} bias"
                })
            
            return True
        
        except Exception as e:
            print(f"Error in special_learning_from_mistakes: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def batch_learn_from_trades(self, trades):
        """
        Apply batch learning from multiple trades
        
        Parameters:
        - trades: List of trade data dictionaries
        
        Returns:
        - bool: Whether batch learning was applied
        """
        if not trades:
            print("No trades provided for batch learning")
            return False
        
        print(f"\n{'='*50}")
        print(f"BATCH LEARNING FROM {len(trades)} TRADES")
        print(f"{'='*50}")
        
        try:
            # Extract prediction details and outcomes
            training_data = []
            
            for trade in trades:
                # Skip trades without prediction details
                if 'prediction_details' not in trade:
                    continue
                
                # Extract prediction details
                prediction_details = trade['prediction_details']
                direction = prediction_details.get('current_prediction', {}).get('direction')
                result = trade.get('result')
                
                # Skip trades without direction or result
                if not direction or not result:
                    continue
                
                # Convert to training data
                prediction = 1 if direction == "UP" else 0
                actual = 1 if result == 'win' else 0
                
                # Add to training data
                training_data.append({
                    'prediction': prediction,
                    'actual': actual,
                    'confidence': prediction_details.get('current_prediction', {}).get('confidence', 0.5),
                    'market_conditions': prediction_details.get('market_conditions', {}),
                    'timestamp': trade.get('timestamp')
                })
            
            if not training_data:
                print("No valid training data extracted from trades")
                return False
            
            print(f"Extracted {len(training_data)} training samples from trades")
            
            # Calculate accuracy before learning
            correct_before = sum(1 for d in training_data if d['prediction'] == d['actual'])
            accuracy_before = correct_before / len(training_data)
            print(f"Accuracy before learning: {accuracy_before:.4f} ({correct_before}/{len(training_data)})")
            
            # Check if we need to retrain the model
            if accuracy_before < self.retraining_threshold:
                print(f"Accuracy ({accuracy_before:.4f}) below threshold ({self.retraining_threshold:.4f}), scheduling retraining")
                self.force_retraining = True
                
                # Adjust hyperparameters based on performance
                self.adjust_hyperparameters_from_feedback(training_data)
            
            return True
        
        except Exception as e:
            print(f"Error in batch_learn_from_trades: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def adjust_hyperparameters_from_feedback(self, training_data):
        """
        Adjust hyperparameters based on feedback from trades
        
        Parameters:
        - training_data: List of dictionaries with prediction feedback
        """
        # Calculate error rates for UP and DOWN predictions
        up_predictions = [d for d in training_data if d['prediction'] == 1]
        down_predictions = [d for d in training_data if d['prediction'] == 0]
        
        up_errors = sum(1 for d in up_predictions if d['actual'] != d['prediction'])
        down_errors = sum(1 for d in down_predictions if d['actual'] != d['prediction'])
        
        up_error_rate = up_errors / len(up_predictions) if up_predictions else 0
        down_error_rate = down_errors / len(down_predictions) if down_predictions else 0
        
        print(f"UP predictions: {len(up_predictions)}, errors: {up_errors}, error rate: {up_error_rate:.4f}")
        print(f"DOWN predictions: {len(down_predictions)}, errors: {down_errors}, error rate: {down_error_rate:.4f}")
        
        # Adjust scale_pos_weight based on error rates
        if up_error_rate > down_error_rate + 0.1:
            # UP predictions are less accurate, adjust weight to favor DOWN
            new_scale_pos_weight = self.hyperparameters.get('scale_pos_weight', 1.0) * 0.9
            print(f"Adjusting scale_pos_weight from {self.hyperparameters.get('scale_pos_weight', 1.0):.4f} to {new_scale_pos_weight:.4f}")
            self.hyperparameters['scale_pos_weight'] = new_scale_pos_weight
        elif down_error_rate > up_error_rate + 0.1:
            # DOWN predictions are less accurate, adjust weight to favor UP
            new_scale_pos_weight = self.hyperparameters.get('scale_pos_weight', 1.0) * 1.1
            print(f"Adjusting scale_pos_weight from {self.hyperparameters.get('scale_pos_weight', 1.0):.4f} to {new_scale_pos_weight:.4f}")
            self.hyperparameters['scale_pos_weight'] = new_scale_pos_weight
        
        # Adjust learning rate based on overall error rate
        overall_error_rate = (up_errors + down_errors) / len(training_data)
        if overall_error_rate > 0.4:
            # High error rate, reduce learning rate for more stable predictions
            new_learning_rate = self.hyperparameters.get('learning_rate', 0.1) * 0.9
            print(f"Adjusting learning_rate from {self.hyperparameters.get('learning_rate', 0.1):.4f} to {new_learning_rate:.4f}")
            self.hyperparameters['learning_rate'] = new_learning_rate
        
        # Save adjusted hyperparameters
        self.hyperparameter_history.append({
            'timestamp': datetime.now().isoformat(),
            'version': self.model_version + 1,
            'hyperparameters': self.hyperparameters,
            'reason': f"Batch learning adjustment: UP error rate={up_error_rate:.4f}, DOWN error rate={down_error_rate:.4f}"
        })
    
    def record_prediction_feedback(self, prediction, actual_outcome, confidence, timestamp=None, prediction_details=None):
        """
        Record feedback from a prediction for self-learning
        
        Parameters:
        - prediction: The prediction made (1 for UP, 0 for DOWN)
        - actual_outcome: The actual outcome (1 for UP, 0 for DOWN)
        - confidence: The confidence level of the prediction (0-1)
        - timestamp: Optional timestamp (defaults to current time)
        - prediction_details: Optional dictionary with additional prediction details
        
        Returns:
        - bool: Whether the prediction was correct
        """
        if timestamp is None:
            timestamp = datetime.now().isoformat()
        
        # Create feedback record
        feedback = {
            'timestamp': timestamp,
            'prediction': prediction,
            'actual': actual_outcome,
            'direction': "UP" if prediction == 1 else "DOWN",
            'confidence': confidence,
            'correct': prediction == actual_outcome,
            'processed': False
        }
        
        # Add market conditions if available
        if prediction_details and 'market_conditions' in prediction_details:
            feedback['market_conditions'] = prediction_details['market_conditions']
        
        # Add to feedback history
        self.prediction_feedback.append(feedback)
        
        # Print feedback summary
        print(f"\nPrediction Feedback:")
        print(f"Direction: {'UP' if prediction == 1 else 'DOWN'}, Actual: {'UP' if actual_outcome == 1 else 'DOWN'}")
        print(f"Confidence: {confidence:.4f}, Correct: {feedback['correct']}")
        
        # Update accuracy history
        self.accuracy_history.append(feedback['correct'])
        if len(self.accuracy_history) > 100:
            self.accuracy_history = self.accuracy_history[-100:]
        
        # Calculate recent accuracy
        recent_accuracy = sum(self.accuracy_history) / len(self.accuracy_history)
        print(f"Recent accuracy: {recent_accuracy:.4f} (last {len(self.accuracy_history)} predictions)")
        
        return feedback['correct']
    
    def start_continuous_learning(self, interval_hours=6):
        """
        Start the continuous learning loop in a background thread
        
        Parameters:
        - interval_hours: Hours between learning iterations
        
        Returns:
        - thread: The background thread running the continuous learning loop
        """
        # Reset exit flag
        self._exit_learning_loop = False
        
        # Create and start thread
        learning_thread = threading.Thread(
            target=self.continuous_learning_loop,
            args=(interval_hours,),
            daemon=True
        )
        
        learning_thread.start()
        print(f"Started continuous learning in background thread (interval: {interval_hours} hours)")
        
        return learning_thread
    
    def stop_continuous_learning(self):
        """Stop the continuous learning loop"""
        self._exit_learning_loop = True
        print("Stopping continuous learning loop (may take up to 5 minutes to complete)")
    
    def continuous_learning_loop(self, interval_hours=6):
        """
        Continuous learning loop that periodically retrains the model
        
        Parameters:
        - interval_hours: Hours between learning iterations
        """
        print(f"Starting continuous learning loop with {interval_hours} hour interval")
        
        # Wait a bit before starting to let the UI initialize
        print("Waiting 10 seconds before starting continuous learning to let UI initialize...")
        time.sleep(10)
        
        while not self._exit_learning_loop:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Running continuous learning iteration")
                
                # Process any queued trades
                self.process_queued_trades()
                
                # Check if we need to retrain the model
                if self.force_retraining or self.should_retrain_model():
                    print("Retraining XGBoost model...")
                    self.retrain_model()
                    self.force_retraining = False
                
                # Sleep for the specified interval in smaller chunks to be more responsive
                print(f"\nContinuous learning iteration complete. Next iteration in {interval_hours} hours.")
                
                # Sleep in 5-minute chunks to be more responsive to UI
                for _ in range(interval_hours * 12):  # 5-minute chunks for the specified hours
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit
                    if self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return
            
            except Exception as e:
                print(f"\nError in continuous learning loop: {e}")
                import traceback
                traceback.print_exc()
                print("Waiting 1 hour before trying again...")
                
                # Sleep in 5-minute chunks
                for _ in range(12):  # 5-minute chunks for 1 hour
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit
                    if self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return
    
    def process_queued_trades(self):
        """Process any queued trades for learning"""
        queued_trades = list(self.trade_history)
        if not queued_trades:
            return
        
        print(f"Processing {len(queued_trades)} queued trades for learning")
        
        # Apply batch learning to all queued trades
        self.batch_learn_from_trades([t[0] for t in queued_trades])
        
        # Clear the queue
        self.trade_history.clear()
    
    def should_retrain_model(self):
        """
        Determine if the model should be retrained based on performance and time
        
        Returns:
        - bool: Whether the model should be retrained
        """
        # Check if enough time has passed since last retraining
        time_since_retraining = (datetime.now() - self.last_retraining_time).total_seconds() / 3600
        if time_since_retraining < 12:  # Don't retrain more than once every 12 hours
            return False
        
        # Check recent accuracy
        if len(self.accuracy_history) >= 20:
            recent_accuracy = sum(self.accuracy_history[-20:]) / 20
            if recent_accuracy < self.retraining_threshold:
                print(f"Recent accuracy ({recent_accuracy:.4f}) below threshold ({self.retraining_threshold:.4f})")
                return True
        
        return False
    
    def retrain_model(self):
        """Retrain the XGBoost model with adjusted hyperparameters"""
        print(f"\n{'='*50}")
        print(f"RETRAINING XGBOOST MODEL")
        print(f"{'='*50}")
        
        try:
            # Update last retraining time
            self.last_retraining_time = datetime.now()
            self.model_version += 1
            
            print(f"Starting retraining of XGBoost model (version {self.model_version})...")
            print(f"Using hyperparameters: {self.hyperparameters}")
            
            # TODO: Implement model retraining with historical data
            # This would involve:
            # 1. Loading historical data
            # 2. Engineering features
            # 3. Retraining the XGBoost model with adjusted hyperparameters
            
            print("Model retraining not fully implemented yet")
            print("In a real implementation, this would retrain the XGBoost model with adjusted hyperparameters")
            
            # Save a snapshot of the retraining
            snapshot = {
                'timestamp': datetime.now().isoformat(),
                'version': self.model_version,
                'hyperparameters': self.hyperparameters,
                'accuracy_history': self.accuracy_history[-20:] if len(self.accuracy_history) >= 20 else self.accuracy_history,
                'error_patterns': self.error_patterns
            }
            
            # Save snapshot to file
            snapshot_path = os.path.join('models', 'snapshots', f'xgboost_retraining_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            with open(snapshot_path, 'w') as f:
                json.dump(snapshot, f, indent=2)
            
            print(f"Saved retraining snapshot to {snapshot_path}")
            
            return True
        
        except Exception as e:
            print(f"Error in retrain_model: {e}")
            import traceback
            traceback.print_exc()
            return False
