#!/usr/bin/env python
"""
Test script for Quotex API
This script tests the Quotex API directly
"""

import os
import sys
import asyncio
from datetime import datetime

def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""

    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")

            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')

            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")

    return email, password

async def test_quotex_api():
    """Test the Quotex API directly"""
    print("=== Testing Quotex API ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Read credentials
    email, password = read_credentials()

    if not email or not password:
        print("No credentials provided. Exiting.")
        return

    # Print password details for debugging
    print(f"Password length: {len(password)}")
    print(f"Password contains special chars: {'+' in password or '%' in password or '-' in password}")
    if len(password) > 0:
        print(f"First character: {password[0]}")
        print(f"Last character: {password[-1]}")

    # Import the Quotex API
    try:
        from quotexapi.stable_api import Quotex
        print("Successfully imported quotexapi module")
    except ImportError as e:
        print(f"Error importing quotexapi module: {e}")
        print("Please install it with: pip install quotexapi")
        return

    # Create Quotex client
    print("Creating Quotex client...")
    client = Quotex(email, password)

    # Try to connect
    print("Connecting to Quotex API...")
    try:
        connected = await client.connect()

        if connected:
            print("Successfully connected to Quotex API!")

            # Get available assets
            print("Getting available assets...")
            try:
                # Try different method names that might exist
                if hasattr(client, 'get_all_asset'):
                    assets = await client.get_all_asset()
                elif hasattr(client, 'get_all_assets'):
                    assets = await client.get_all_assets()
                elif hasattr(client, 'get_assets'):
                    assets = await client.get_assets()
                elif hasattr(client, 'get_asset_list'):
                    assets = await client.get_asset_list()
                else:
                    print("Could not find a method to get assets. Available methods:")
                    for method in dir(client):
                        if not method.startswith('_'):
                            print(f"- {method}")
                    assets = None

                print(f"Available assets: {assets}")
            except Exception as asset_error:
                print(f"Error getting assets: {asset_error}")
                import traceback
                traceback.print_exc()

            # Disconnect
            await client.close()
            print("Disconnected from Quotex API")
        else:
            print("Failed to connect to Quotex API")
    except Exception as e:
        print(f"Error connecting to Quotex API: {e}")
        import traceback
        traceback.print_exc()

        # Check if it's a PIN verification error
        error_str = str(e).lower()
        if "pin" in error_str or "verification" in error_str or "code" in error_str:
            print("PIN verification required")
            print("Please check your email for the PIN code")

            # Ask for PIN
            pin = input("Enter the PIN from your email: ")
            if pin:
                print(f"Trying to connect with PIN: {pin}")
                try:
                    # Try to connect with PIN
                    # This is a workaround since the API doesn't have a direct method to submit PIN
                    # We'll set the PIN as a global variable that the API can access
                    import builtins
                    original_input = builtins.input

                    # Mock the input function to return the PIN
                    def mock_input(prompt):
                        print(f"PIN prompt: {prompt}")
                        return pin

                    # Replace the input function
                    builtins.input = mock_input

                    # Try to connect again
                    print("Reconnecting with PIN...")
                    connected = await client.connect()

                    # Restore the original input function
                    builtins.input = original_input

                    if connected:
                        print("Successfully connected to Quotex API with PIN!")

                        # Get available assets
                        print("Getting available assets...")
                        try:
                            # Try different method names that might exist
                            if hasattr(client, 'get_all_asset'):
                                assets = await client.get_all_asset()
                            elif hasattr(client, 'get_all_assets'):
                                assets = await client.get_all_assets()
                            elif hasattr(client, 'get_assets'):
                                assets = await client.get_assets()
                            elif hasattr(client, 'get_asset_list'):
                                assets = await client.get_asset_list()
                            else:
                                print("Could not find a method to get assets. Available methods:")
                                for method in dir(client):
                                    if not method.startswith('_'):
                                        print(f"- {method}")
                                assets = None

                            print(f"Available assets: {assets}")
                        except Exception as asset_error:
                            print(f"Error getting assets: {asset_error}")
                            import traceback
                            traceback.print_exc()

                        # Disconnect
                        await client.close()
                        print("Disconnected from Quotex API")
                    else:
                        print("Failed to connect to Quotex API with PIN")
                except Exception as pin_error:
                    print(f"Error connecting with PIN: {pin_error}")
                    import traceback
                    traceback.print_exc()

                    # Restore the original input function
                    builtins.input = original_input

if __name__ == "__main__":
    try:
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Run the test function
        loop.run_until_complete(test_quotex_api())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the event loop
        loop.close()
