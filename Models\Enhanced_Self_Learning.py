#!/usr/bin/env python
"""
Enhanced Self-Learning Module

This module provides advanced self-learning capabilities for trading models,
with improved accuracy after every trade and better learning from mistakes.
"""

import os
import time
import numpy as np
import pandas as pd
from datetime import datetime
import json
import joblib
import threading
from collections import deque

# Import our learning progress tracker
from Models.Learning_Progress_Tracker import LearningProgressTracker

# Check if TensorFlow is available
try:
    import tensorflow as tf
    from tensorflow.keras.models import Model, load_model, clone_model
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Input
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
    print("TensorFlow is available in Enhanced Self-Learning module.")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow is not available in Enhanced Self-Learning module. Some features will be limited.")

class EnhancedModelTrainer:
    """
    Enhanced model trainer with improved self-learning capabilities
    """

    def __init__(self, data_collector=None, feature_engineer=None,
                 adaptation_rate=0.25, confidence_threshold=0.65, memory_factor=0.7,
                 retraining_threshold=0.6, optimization_interval=5, feedback_window=10):
        """
        Initialize the enhanced model trainer

        Parameters:
        - data_collector: Data collector object for fetching market data
        - feature_engineer: Function for engineering features
        - adaptation_rate: Rate at which weights adapt to new performance (0-1)
        - confidence_threshold: Threshold for high-confidence predictions
        - memory_factor: Factor for weighting recent vs historical performance (0-1)
        - retraining_threshold: Accuracy threshold below which models are retrained
        - optimization_interval: Number of iterations between hyperparameter optimizations
        - feedback_window: Number of recent trades to use for feedback learning
        """
        self.data_collector = data_collector
        self.feature_engineer = feature_engineer
        self.models = {}
        self.ensemble = None
        self.performance_history = []

        # Self-learning parameters
        self.adaptation_rate = adaptation_rate
        self.confidence_threshold = confidence_threshold
        self.memory_factor = memory_factor
        self.retraining_threshold = retraining_threshold
        self.optimization_interval = optimization_interval
        self.feedback_window = feedback_window

        # Enhanced tracking variables
        self.prediction_feedback = deque(maxlen=1000)  # Use deque with max length
        self.trade_history = deque(maxlen=500)
        self.market_conditions = deque(maxlen=100)
        self.model_versions = {'lstm': 1, 'xgboost': 1, 'dqn': 1, 'transformer': 1, 'cnn_lstm': 1}
        self.learning_iterations = 0
        self.last_retraining_time = datetime.now()

        # Performance metrics
        self.accuracy_history = []
        self.error_patterns = {}
        self.model_weights_history = []

        # Learning flags
        self.force_retraining = False
        self.learning_in_progress = False
        self._exit_learning_loop = False

        # Create model directory
        os.makedirs('models', exist_ok=True)
        os.makedirs('models/snapshots', exist_ok=True)
        os.makedirs('learning_progress', exist_ok=True)

        # Initialize error pattern detection
        self.initialize_error_patterns()

        # Initialize learning progress tracker
        self.progress_tracker = LearningProgressTracker(save_dir='learning_progress')
        print("Learning progress tracker initialized - tracking day-by-day improvements")

    def initialize_error_patterns(self):
        """Initialize error pattern detection system"""
        self.error_patterns = {
            'consecutive_up_errors': 0,
            'consecutive_down_errors': 0,
            'high_volatility_errors': 0,
            'low_volatility_errors': 0,
            'trend_reversal_errors': 0,
            'pattern_weights': {
                'consecutive_direction': 1.0,
                'volatility': 0.8,
                'trend_reversal': 1.2
            }
        }

    def learn_from_trade(self, trade_data, was_successful):
        """
        Learn from a completed trade with enhanced feedback mechanisms

        Parameters:
        - trade_data: Dictionary with trade details
        - was_successful: Whether the trade was successful

        Returns:
        - bool: Whether learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping enhanced learning")
            return False

        if self.learning_in_progress:
            print("Learning already in progress, queueing this trade for later learning")
            self.trade_history.append((trade_data, was_successful))
            return False

        print(f"\n{'='*50}")
        print(f"LEARNING FROM {'SUCCESSFUL' if was_successful else 'FAILED'} TRADE")
        print(f"{'='*50}")

        try:
            self.learning_in_progress = True

            # Extract prediction details
            prediction_details = trade_data.get('prediction_details', {})
            direction = prediction_details.get('current_prediction', {}).get('direction')
            confidence = prediction_details.get('current_prediction', {}).get('confidence', 0.5)
            model_used = prediction_details.get('current_prediction', {}).get('model', 'unknown')

            # Convert direction to binary for learning
            prediction = 1 if direction == "UP" else 0
            actual = 1 if was_successful == (direction == "UP") else 0

            print(f"Direction: {direction}, Confidence: {confidence:.4f}, Model: {model_used}")
            print(f"Prediction: {prediction}, Actual: {actual}")

            # Record feedback for self-learning
            self.record_prediction_feedback(prediction, actual, confidence,
                                           timestamp=trade_data.get('timestamp'),
                                           prediction_details=prediction_details)

            # Store trade in history
            self.trade_history.append((trade_data, was_successful))

            # Check for error patterns
            self.detect_error_patterns(trade_data, was_successful)

            # Apply immediate model adjustments for high confidence errors
            if not was_successful and confidence > 0.8 and self.ensemble:
                print("High confidence error detected! Applying immediate model adjustments...")

                # Calculate adjustment factor based on confidence
                adjustment_factor = min(0.3, confidence * 0.3)  # Cap at 0.3

                # Apply stronger adjustment for high confidence errors
                self.ensemble.apply_immediate_adjustment(model_used, -adjustment_factor)
                print(f"Applied negative adjustment of {adjustment_factor:.4f} to {model_used} model")

                # Boost complementary models
                for model_name in self.ensemble.model_weights:
                    if model_name != model_used:
                        boost_factor = adjustment_factor / (len(self.ensemble.model_weights) - 1)
                        self.ensemble.apply_immediate_adjustment(model_name, boost_factor)
                        print(f"Applied positive adjustment of {boost_factor:.4f} to {model_name} model")

            # Check if we need to trigger special learning
            if self.should_trigger_special_learning():
                print("Triggering special learning due to error patterns...")
                self.special_learning_from_mistakes()

            # Save snapshot of model weights
            self.save_model_weights_snapshot()

            self.learning_iterations += 1
            print(f"Learning iteration {self.learning_iterations} completed")

            return True

        except Exception as e:
            print(f"Error in learn_from_trade: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            self.learning_in_progress = False

    def detect_error_patterns(self, trade_data, was_successful):
        """
        Detect patterns in trading errors to improve learning

        Parameters:
        - trade_data: Dictionary with trade details
        - was_successful: Whether the trade was successful
        """
        if was_successful:
            # Reset error counters on success
            self.error_patterns['consecutive_up_errors'] = 0
            self.error_patterns['consecutive_down_errors'] = 0
            return

        # Extract prediction details
        prediction_details = trade_data.get('prediction_details', {})
        direction = prediction_details.get('current_prediction', {}).get('direction')

        # Track consecutive direction errors
        if direction == "UP":
            self.error_patterns['consecutive_up_errors'] += 1
            self.error_patterns['consecutive_down_errors'] = 0
        else:
            self.error_patterns['consecutive_down_errors'] += 1
            self.error_patterns['consecutive_up_errors'] = 0

        # Check for volatility-related errors
        market_conditions = prediction_details.get('market_conditions', {})
        volatility = market_conditions.get('volatility', 0)

        if volatility > 0.02:  # High volatility
            self.error_patterns['high_volatility_errors'] += 1
        elif volatility < 0.005:  # Low volatility
            self.error_patterns['low_volatility_errors'] += 1

        # Check for trend reversal errors
        if len(self.trade_history) >= 2:
            prev_trade, prev_success = self.trade_history[-1]
            prev_direction = prev_trade.get('prediction_details', {}).get('current_prediction', {}).get('direction')

            if prev_direction != direction and not prev_success and not was_successful:
                self.error_patterns['trend_reversal_errors'] += 1
                print(f"Detected trend reversal error pattern: {prev_direction} → {direction}")

        # Print error pattern summary
        print("\nError Pattern Summary:")
        print(f"Consecutive UP errors: {self.error_patterns['consecutive_up_errors']}")
        print(f"Consecutive DOWN errors: {self.error_patterns['consecutive_down_errors']}")
        print(f"High volatility errors: {self.error_patterns['high_volatility_errors']}")
        print(f"Low volatility errors: {self.error_patterns['low_volatility_errors']}")
        print(f"Trend reversal errors: {self.error_patterns['trend_reversal_errors']}")

    def should_trigger_special_learning(self):
        """
        Determine if special learning should be triggered based on error patterns

        Returns:
        - bool: Whether to trigger special learning
        """
        # Check for consecutive direction errors
        if (self.error_patterns['consecutive_up_errors'] >= 2 or
            self.error_patterns['consecutive_down_errors'] >= 2):
            return True

        # Check for high volatility errors
        if self.error_patterns['high_volatility_errors'] >= 3:
            return True

        # Check for trend reversal errors
        if self.error_patterns['trend_reversal_errors'] >= 2:
            return True

        return False

    def special_learning_from_mistakes(self, consecutive_losses=None):
        """
        Apply special learning techniques when consecutive losses are detected

        Parameters:
        - consecutive_losses: Number of consecutive losses (optional)

        Returns:
        - bool: Whether special learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping special learning")
            return False

        print(f"\n{'='*50}")
        print(f"SPECIAL LEARNING FROM MISTAKES")
        print(f"{'='*50}")

        try:
            # Get recent trades for analysis
            recent_trades = list(self.trade_history)[-self.feedback_window:]
            if not recent_trades:
                print("No trade history available for special learning")
                return False

            # Analyze error patterns
            up_errors = self.error_patterns['consecutive_up_errors']
            down_errors = self.error_patterns['consecutive_down_errors']

            print(f"Analyzing error patterns: UP errors={up_errors}, DOWN errors={down_errors}")

            # Determine if there's a directional bias
            direction_bias = None
            if up_errors >= 2:
                direction_bias = "UP"
                print(f"Detected directional bias: System is incorrectly predicting UP")
            elif down_errors >= 2:
                direction_bias = "DOWN"
                print(f"Detected directional bias: System is incorrectly predicting DOWN")

            # Apply bias correction to ensemble
            if direction_bias and self.ensemble:
                bias_correction = -0.3 if direction_bias == "UP" else 0.3
                self.ensemble.bias_correction = bias_correction
                print(f"Applied bias correction of {bias_correction} to counteract {direction_bias} bias")

            # Extract features from recent trades for targeted learning
            successful_trades = [(t, s) for t, s in recent_trades if s]
            failed_trades = [(t, s) for t, s in recent_trades if not s]

            print(f"Recent trades: {len(recent_trades)} total, {len(successful_trades)} successful, {len(failed_trades)} failed")

            if failed_trades:
                # Extract market conditions from failed trades
                failed_conditions = []
                for trade, _ in failed_trades:
                    if 'prediction_details' in trade and 'market_conditions' in trade['prediction_details']:
                        failed_conditions.append(trade['prediction_details']['market_conditions'])

                if failed_conditions:
                    # Analyze common conditions in failed trades
                    print("Analyzing market conditions in failed trades...")

                    # Check for common regimes
                    regimes = [c.get('regime') for c in failed_conditions if 'regime' in c]
                    if regimes:
                        common_regime = max(set(regimes), key=regimes.count)
                        regime_count = regimes.count(common_regime)

                        if regime_count / len(regimes) > 0.5:  # More than 50% have same regime
                            print(f"Found common market regime in failed trades: {common_regime} ({regime_count}/{len(regimes)})")

                            # Apply regime-specific adjustments
                            if self.ensemble:
                                if common_regime == "bullish":
                                    # In bullish regime, reduce weight of models that predict UP incorrectly
                                    for model_name in self.ensemble.model_weights:
                                        if model_name in ['lstm_gru', 'transformer']:  # These models might be more trend-following
                                            self.ensemble.apply_immediate_adjustment(model_name, -0.15)
                                            print(f"Reduced weight of {model_name} by 0.15 for bullish regime errors")

                                elif common_regime == "bearish":
                                    # In bearish regime, reduce weight of models that predict DOWN incorrectly
                                    for model_name in self.ensemble.model_weights:
                                        if model_name in ['xgboost', 'dqn']:  # These models might be more reversal-focused
                                            self.ensemble.apply_immediate_adjustment(model_name, -0.15)
                                            print(f"Reduced weight of {model_name} by 0.15 for bearish regime errors")

            # Force retraining of the worst performing model
            if self.ensemble and hasattr(self.ensemble, 'model_performance'):
                worst_model = min(self.ensemble.model_performance,
                                 key=lambda k: self.ensemble.model_performance[k]['accuracy'])

                print(f"Scheduling retraining for worst performing model: {worst_model}")
                self.force_retraining = True
                self.models_to_retrain = [worst_model]

            # Save the updated ensemble
            if self.ensemble:
                self.save_model_weights_snapshot(special=True)

            return True

        except Exception as e:
            print(f"Error in special_learning_from_mistakes: {e}")
            import traceback
            traceback.print_exc()
            return False

    def batch_learn_from_trades(self, trades):
        """
        Apply batch learning from multiple trades

        Parameters:
        - trades: List of trade data dictionaries

        Returns:
        - bool: Whether batch learning was applied
        """
        if not trades:
            print("No trades provided for batch learning")
            return False

        print(f"\n{'='*50}")
        print(f"BATCH LEARNING FROM {len(trades)} TRADES")
        print(f"{'='*50}")

        try:
            # Extract prediction details and outcomes
            training_data = []

            for trade in trades:
                # Skip trades without prediction details
                if 'prediction_details' not in trade:
                    continue

                # Extract prediction details
                prediction_details = trade['prediction_details']
                direction = prediction_details.get('current_prediction', {}).get('direction')
                result = trade.get('result')

                # Skip trades without direction or result
                if not direction or not result:
                    continue

                # Convert to training data
                prediction = 1 if direction == "UP" else 0
                actual = 1 if result == 'win' else 0

                # Add to training data
                training_data.append({
                    'prediction': prediction,
                    'actual': actual,
                    'confidence': prediction_details.get('current_prediction', {}).get('confidence', 0.5),
                    'market_conditions': prediction_details.get('market_conditions', {}),
                    'timestamp': trade.get('timestamp')
                })

            if not training_data:
                print("No valid training data extracted from trades")
                return False

            print(f"Extracted {len(training_data)} training samples from trades")

            # Calculate accuracy before learning
            correct_before = sum(1 for d in training_data if d['prediction'] == d['actual'])
            accuracy_before = correct_before / len(training_data)
            print(f"Accuracy before learning: {accuracy_before:.4f} ({correct_before}/{len(training_data)})")

            # Apply learning to ensemble weights
            if self.ensemble:
                print("Applying batch learning to ensemble weights...")

                # Calculate adjustments for each model
                model_adjustments = {}

                for model_name in self.ensemble.model_weights:
                    # Start with no adjustment
                    model_adjustments[model_name] = 0

                    # Count correct and incorrect predictions for this model
                    correct = 0
                    incorrect = 0

                    for data in training_data:
                        # Skip if this model wasn't used for this prediction
                        prediction_model = data.get('model', 'ensemble')
                        if prediction_model != model_name and prediction_model != 'ensemble':
                            continue

                        if data['prediction'] == data['actual']:
                            correct += 1
                        else:
                            incorrect += 1

                    # Calculate adjustment based on performance
                    if correct + incorrect > 0:
                        accuracy = correct / (correct + incorrect)

                        # Adjust more for models with more predictions
                        sample_weight = min(1.0, (correct + incorrect) / 5)

                        # Calculate adjustment
                        if accuracy > 0.6:  # Good performance
                            adjustment = 0.05 * sample_weight
                        elif accuracy < 0.4:  # Poor performance
                            adjustment = -0.05 * sample_weight
                        else:  # Neutral performance
                            adjustment = 0

                        model_adjustments[model_name] = adjustment
                        print(f"Model {model_name}: accuracy={accuracy:.4f}, adjustment={adjustment:.4f}")

                # Apply adjustments
                for model_name, adjustment in model_adjustments.items():
                    if adjustment != 0:
                        self.ensemble.apply_immediate_adjustment(model_name, adjustment)

                # Save updated weights
                self.save_model_weights_snapshot(batch=True)

            # Check if we should retrain any models
            if len(training_data) >= 10:
                print("Checking if models need retraining...")

                # Calculate overall accuracy
                correct_after = sum(1 for d in training_data if d['prediction'] == d['actual'])
                accuracy_after = correct_after / len(training_data)

                # If accuracy is below threshold, schedule retraining
                if accuracy_after < self.retraining_threshold:
                    print(f"Accuracy ({accuracy_after:.4f}) below threshold ({self.retraining_threshold:.4f}), scheduling retraining")
                    self.force_retraining = True

            return True

        except Exception as e:
            print(f"Error in batch_learn_from_trades: {e}")
            import traceback
            traceback.print_exc()
            return False

    def record_prediction_feedback(self, prediction, actual_outcome, confidence, timestamp=None, prediction_details=None):
        """
        Record feedback from a prediction for enhanced self-learning

        Parameters:
        - prediction: The prediction made (1 for UP, 0 for DOWN)
        - actual_outcome: The actual outcome (1 for UP, 0 for DOWN)
        - confidence: The confidence level of the prediction (0-1)
        - timestamp: Optional timestamp (defaults to current time)
        - prediction_details: Optional dictionary with additional prediction details

        Returns:
        - bool: Whether the prediction was correct
        """
        if timestamp is None:
            timestamp = datetime.now()
        elif isinstance(timestamp, str):
            try:
                timestamp = datetime.fromisoformat(timestamp)
            except:
                timestamp = datetime.now()

        # Create feedback record
        feedback = {
            'timestamp': timestamp.isoformat() if isinstance(timestamp, datetime) else timestamp,
            'prediction': prediction,
            'actual': actual_outcome,
            'direction': "UP" if prediction == 1 else "DOWN",
            'confidence': confidence,
            'correct': prediction == actual_outcome,
            'processed': False
        }

        # Add market conditions if available
        if prediction_details and 'market_conditions' in prediction_details:
            feedback['market_conditions'] = prediction_details['market_conditions']

        # Add model information if available
        model_name = 'unknown'
        if prediction_details and 'current_prediction' in prediction_details:
            model_name = prediction_details['current_prediction'].get('model', 'unknown')
            feedback['model'] = model_name

        # Add to feedback history
        self.prediction_feedback.append(feedback)

        # Print feedback summary
        print(f"\nPrediction Feedback:")
        print(f"Direction: {'UP' if prediction == 1 else 'DOWN'}, Actual: {'UP' if actual_outcome == 1 else 'DOWN'}")
        print(f"Confidence: {confidence:.4f}, Correct: {feedback['correct']}")

        # Update accuracy history
        self.accuracy_history.append(feedback['correct'])
        if len(self.accuracy_history) > 100:
            self.accuracy_history = self.accuracy_history[-100:]

        # Calculate recent accuracy
        recent_accuracy = sum(self.accuracy_history) / len(self.accuracy_history)
        print(f"Recent accuracy: {recent_accuracy:.4f} (last {len(self.accuracy_history)} predictions)")

        # Record in progress tracker for day-by-day improvement tracking
        self.progress_tracker.record_trade(
            prediction=prediction,
            actual=actual_outcome,
            confidence=confidence,
            model_name=model_name,
            timestamp=timestamp if isinstance(timestamp, datetime) else datetime.now()
        )

        # Generate a progress report every 10 trades
        if len(self.accuracy_history) % 10 == 0:
            report = self.progress_tracker.generate_progress_report()

            # Print improvement metrics
            print("\nLearning Progress Report:")
            print(f"Overall accuracy: {report['overall']['accuracy']:.4f} ({report['overall']['correct_trades']}/{report['overall']['total_trades']})")
            print(f"Recent accuracy: {report['recent']['accuracy']:.4f} (last {report['recent']['trades']} trades)")

            # Print day-by-day improvement
            if report['short_term']['improvement'] != 0:
                print(f"Week-over-week improvement: {report['short_term']['improvement']:.2f}%")
                print(f"Learning trend: {report['short_term']['trend']}")

            # Save a progress chart
            try:
                self.progress_tracker.plot_learning_progress(save_path='learning_progress/progress_chart.png')
                print("Updated learning progress chart saved to learning_progress/progress_chart.png")
            except Exception as e:
                print(f"Error saving progress chart: {e}")

        return feedback['correct']

    def save_model_weights_snapshot(self, special=False, batch=False):
        """
        Save a snapshot of the current model weights

        Parameters:
        - special: Whether this is from special learning
        - batch: Whether this is from batch learning
        """
        if not self.ensemble or not hasattr(self.ensemble, 'model_weights'):
            return

        # Create snapshot
        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'weights': dict(self.ensemble.model_weights),
            'iteration': self.learning_iterations,
            'special': special,
            'batch': batch
        }

        # Add to history
        self.model_weights_history.append(snapshot)

        # Keep history limited
        if len(self.model_weights_history) > 100:
            self.model_weights_history = self.model_weights_history[-100:]

        # Save to file
        try:
            snapshot_type = "special" if special else "batch" if batch else "regular"
            filename = f"models/snapshots/weights_{snapshot_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w') as f:
                json.dump(snapshot, f, indent=2)

            print(f"Saved model weights snapshot to {filename}")
        except Exception as e:
            print(f"Error saving model weights snapshot: {e}")

    def start_continuous_learning(self, interval_hours=6):
        """
        Start the continuous learning loop in a background thread

        Parameters:
        - interval_hours: Hours between learning iterations

        Returns:
        - thread: The background thread running the continuous learning loop
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow is not available, cannot start continuous learning")
            return None

        # Reset exit flag
        self._exit_learning_loop = False

        # Create and start thread
        learning_thread = threading.Thread(
            target=self.continuous_learning_loop,
            args=(interval_hours,),
            daemon=True
        )

        learning_thread.start()
        print(f"Started continuous learning in background thread (interval: {interval_hours} hours)")

        return learning_thread

    def stop_continuous_learning(self):
        """Stop the continuous learning loop"""
        self._exit_learning_loop = True
        print("Stopping continuous learning loop (may take up to 5 minutes to complete)")

    def continuous_learning_loop(self, interval_hours=6):
        """
        Advanced continuous learning loop with self-adaptation capabilities

        Parameters:
        - interval_hours: Hours between learning iterations
        """
        print(f"Starting continuous learning loop with {interval_hours} hour interval")

        # Wait a bit before starting to let the UI initialize
        print("Waiting 10 seconds before starting continuous learning to let UI initialize...")
        time.sleep(10)

        while not self._exit_learning_loop:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Running continuous learning iteration")

                # Process any queued trades
                self.process_queued_trades()

                # Check if we need to retrain models
                if self.force_retraining or self.should_retrain_models():
                    print("Retraining models...")
                    self.retrain_models()
                    self.force_retraining = False

                # Save current state
                self.save_learning_state()

                # Record learning iteration in progress tracker
                if self.ensemble and hasattr(self.ensemble, 'model_weights'):
                    # Get current model weights
                    model_weights = dict(self.ensemble.model_weights)

                    # Calculate metrics from recent accuracy history
                    metrics = {}
                    if len(self.accuracy_history) > 0:
                        recent_accuracy = sum(self.accuracy_history[-min(20, len(self.accuracy_history)):]) / min(20, len(self.accuracy_history))
                        metrics['recent_accuracy'] = recent_accuracy

                    # Record learning iteration
                    self.progress_tracker.record_learning_iteration(metrics, model_weights)
                    print(f"Recorded learning iteration {self.learning_iterations + 1} in progress tracker")

                    # Generate and save a progress report
                    report = self.progress_tracker.generate_progress_report()
                    print("\nLearning Progress Summary:")
                    print(f"Overall accuracy: {report['overall']['accuracy']:.4f}")
                    print(f"Recent accuracy: {report['recent']['accuracy']:.4f}")
                    print(f"Week-over-week improvement: {report['short_term']['improvement']:.2f}%")
                    print(f"Learning trend: {report['short_term']['trend']}")

                    # Save progress chart
                    try:
                        self.progress_tracker.plot_learning_progress(save_path='learning_progress/progress_chart.png')
                        print("Updated learning progress chart saved")
                    except Exception as e:
                        print(f"Error saving progress chart: {e}")

                # Increment learning iterations
                self.learning_iterations += 1

                # Sleep for the specified interval in smaller chunks to be more responsive
                print(f"\nContinuous learning iteration complete. Next iteration in {interval_hours} hours.")

                # Sleep in 5-minute chunks to be more responsive to UI
                for _ in range(interval_hours * 12):  # 5-minute chunks for the specified hours
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit
                    if self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return

            except Exception as e:
                print(f"\nError in continuous learning loop: {e}")
                import traceback
                traceback.print_exc()
                print("Waiting 1 hour before trying again...")

                # Sleep in 5-minute chunks
                for _ in range(12):  # 5-minute chunks for 1 hour
                    time.sleep(300)  # 5 minutes
                    # Check if we should exit
                    if self._exit_learning_loop:
                        print("Exiting continuous learning loop")
                        return

    def process_queued_trades(self):
        """Process any queued trades for learning"""
        queued_trades = list(self.trade_history)
        if not queued_trades:
            return

        print(f"Processing {len(queued_trades)} queued trades for learning")

        # Apply batch learning to all queued trades
        self.batch_learn_from_trades([t[0] for t in queued_trades])

        # Clear the queue
        self.trade_history.clear()

    def should_retrain_models(self):
        """
        Determine if models should be retrained based on performance and time

        Returns:
        - bool: Whether models should be retrained
        """
        # Check if enough time has passed since last retraining
        time_since_retraining = (datetime.now() - self.last_retraining_time).total_seconds() / 3600
        if time_since_retraining < 12:  # Don't retrain more than once every 12 hours
            return False

        # Check recent accuracy
        if len(self.accuracy_history) >= 20:
            recent_accuracy = sum(self.accuracy_history[-20:]) / 20
            if recent_accuracy < self.retraining_threshold:
                print(f"Recent accuracy ({recent_accuracy:.4f}) below threshold ({self.retraining_threshold:.4f})")
                return True

        return False

    def retrain_models(self):
        """Retrain models to improve performance"""
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping model retraining")
            return

        print(f"\n{'='*50}")
        print(f"RETRAINING MODELS")
        print(f"{'='*50}")

        try:
            # Update last retraining time
            self.last_retraining_time = datetime.now()

            # 1. Fetch recent market data
            print("Fetching recent market data for retraining...")
            if self.data_collector is None:
                print("No data collector available. Creating a simple one...")

                # Create a simple data collector if none is provided
                class SimpleDataCollector:
                    def __init__(self):
                        self.data_dir = 'data'
                        os.makedirs(self.data_dir, exist_ok=True)

                    def fetch_historical_data(self, lookback_days=60):
                        """Fetch historical data from saved CSV files"""
                        print(f"Looking for historical data files in {self.data_dir}...")
                        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]

                        if not csv_files:
                            print("No historical data files found.")
                            return None

                        print(f"Found {len(csv_files)} data files.")

                        # Load and combine all CSV files
                        all_data = []
                        for file in csv_files:
                            try:
                                df = pd.read_csv(os.path.join(self.data_dir, file))
                                all_data.append(df)
                                print(f"Loaded {len(df)} rows from {file}")
                            except Exception as e:
                                print(f"Error loading {file}: {e}")

                        if not all_data:
                            print("No data could be loaded from CSV files.")
                            return None

                        # Combine all dataframes
                        combined_df = pd.concat(all_data, ignore_index=True)

                        # Sort by timestamp and remove duplicates
                        if 'timestamp' in combined_df.columns:
                            combined_df['timestamp'] = pd.to_datetime(combined_df['timestamp'])
                            combined_df = combined_df.sort_values('timestamp')
                            combined_df = combined_df.drop_duplicates(subset=['timestamp'])

                        print(f"Combined data: {len(combined_df)} rows")
                        return combined_df

                    def preprocess_data(self, data):
                        """Basic preprocessing of data"""
                        if data is None:
                            return None

                        # Ensure all required columns exist
                        required_cols = ['open', 'high', 'low', 'close']
                        if not all(col in data.columns for col in required_cols):
                            print(f"Data missing required columns: {required_cols}")
                            return None

                        # Convert to numeric
                        for col in required_cols:
                            data[col] = pd.to_numeric(data[col], errors='coerce')

                        # Fill missing values
                        data = data.fillna(method='ffill').fillna(method='bfill')

                        return data

                self.data_collector = SimpleDataCollector()

            # Fetch historical data
            historical_data = self.data_collector.fetch_historical_data(lookback_days=60)

            if historical_data is None or len(historical_data) < 100:
                print("Insufficient historical data for retraining.")
                return

            # Preprocess the data
            processed_data = self.data_collector.preprocess_data(historical_data)
            if processed_data is None:
                print("Failed to preprocess data for retraining.")
                return

            print(f"Successfully prepared {len(processed_data)} data points for retraining.")

            # 2. Engineer features
            if self.feature_engineer is None:
                print("No feature engineer available. Cannot proceed with retraining.")
                return

            print("Engineering features for model retraining...")
            try:
                featured_data = self.feature_engineer(processed_data)
                print(f"Generated {featured_data.shape[1]} features for training.")
            except Exception as e:
                print(f"Error engineering features: {e}")
                import traceback
                traceback.print_exc()
                return

            # 3. Retrain models with new data
            print("Retraining models with new data...")

            # Import necessary modules for model training
            try:
                from Models.LSTM_GRU_Deep_Learning_Model import train_lstm_gru_model
                from Models.Transformer_Model import train_transformer_model
                from Models.XGBoost_Model import train_xgboost_model

                # Determine which models to retrain
                models_to_retrain = getattr(self, 'models_to_retrain', ['lstm_gru', 'transformer', 'xgboost'])
                print(f"Models selected for retraining: {models_to_retrain}")

                # Prepare training data
                X = featured_data.drop(['target'], axis=1) if 'target' in featured_data.columns else featured_data
                y = featured_data['target'] if 'target' in featured_data.columns else None

                # If target is not available, create a simple target based on price movement
                if y is None and 'close' in featured_data.columns:
                    print("Creating target variable based on price movement...")
                    featured_data['target'] = (featured_data['close'].shift(-1) > featured_data['close']).astype(int)
                    featured_data = featured_data.dropna()
                    X = featured_data.drop(['target'], axis=1)
                    y = featured_data['target']

                if y is None:
                    print("Cannot create target variable for training. Aborting retraining.")
                    return

                print(f"Training data prepared: X shape={X.shape}, y shape={y.shape}")

                # Retrain each selected model
                retrained_models = {}

                if 'lstm_gru' in models_to_retrain:
                    print("Retraining LSTM-GRU model...")
                    try:
                        lstm_model, lstm_info = train_lstm_gru_model(X, y, epochs=10, batch_size=32, verbose=1)
                        retrained_models['lstm_gru'] = {'model': lstm_model, 'info': lstm_info}
                        print("LSTM-GRU model retrained successfully.")
                    except Exception as e:
                        print(f"Error retraining LSTM-GRU model: {e}")

                if 'transformer' in models_to_retrain:
                    print("Retraining Transformer model...")
                    try:
                        transformer_model, transformer_info = train_transformer_model(X, y, epochs=10, batch_size=32, verbose=1)
                        retrained_models['transformer'] = {'model': transformer_model, 'info': transformer_info}
                        print("Transformer model retrained successfully.")
                    except Exception as e:
                        print(f"Error retraining Transformer model: {e}")

                if 'xgboost' in models_to_retrain:
                    print("Retraining XGBoost model...")
                    try:
                        xgboost_model, xgboost_info = train_xgboost_model(X, y)
                        retrained_models['xgboost'] = {'model': xgboost_model, 'info': xgboost_info}
                        print("XGBoost model retrained successfully.")
                    except Exception as e:
                        print(f"Error retraining XGBoost model: {e}")

                # 4. Save retrained models
                if retrained_models:
                    print(f"Saving {len(retrained_models)} retrained models...")
                    for model_name, model_data in retrained_models.items():
                        model = model_data['model']
                        info = model_data['info']

                        # Increment model version
                        if model_name in self.model_versions:
                            self.model_versions[model_name] += 1
                        else:
                            self.model_versions[model_name] = 1

                        version = self.model_versions[model_name]

                        # Save model with consistent filename to maintain continuity
                        # Use fixed base names with version suffix instead of timestamps
                        model_path = f"models/{model_name}_v{version}"

                        print(f"Saving updated model: {model_path}")

                        if model_name in ['lstm_gru', 'transformer']:
                            # Save TensorFlow model
                            model.save(f"{model_path}.h5")

                            # Save feature list
                            with open(f"{model_path}_features.txt", 'w') as f:
                                f.write('\n'.join(X.columns.tolist()))

                            # Create a backup of the previous version
                            if os.path.exists(f"{model_path}_previous.h5"):
                                os.remove(f"{model_path}_previous.h5")
                            if os.path.exists(f"{model_path}.h5"):
                                os.rename(f"{model_path}.h5", f"{model_path}_previous.h5")

                        elif model_name == 'xgboost':
                            # Create a backup of the previous version
                            if os.path.exists(f"{model_path}_previous.joblib"):
                                os.remove(f"{model_path}_previous.joblib")
                            if os.path.exists(f"{model_path}.joblib"):
                                os.rename(f"{model_path}.joblib", f"{model_path}_previous.joblib")

                            # Save XGBoost model
                            joblib.dump(model, f"{model_path}.joblib")

                            # Save feature list
                            with open(f"{model_path}_features.txt", 'w') as f:
                                f.write('\n'.join(X.columns.tolist()))

                        print(f"Saved retrained {model_name} model to {model_path}")

                        # Update models dictionary
                        self.models[model_name] = {
                            'model': model,
                            'info': info,
                            'features': X.columns.tolist(),
                            'version': version,
                            'timestamp': timestamp
                        }

                    # 5. Update the ensemble if it exists
                    if hasattr(self, 'ensemble') and self.ensemble is not None:
                        print("Updating ensemble with retrained models...")
                        try:
                            from Models.Ensemble_Model_Integration import update_ensemble
                            self.ensemble = update_ensemble(self.ensemble, retrained_models)
                            print("Ensemble updated successfully with retrained models.")
                        except Exception as e:
                            print(f"Error updating ensemble: {e}")

                    print("Model retraining completed successfully!")

                    # Record retraining in progress tracker
                    metrics = {
                        'retraining': True,
                        'models_retrained': list(retrained_models.keys()),
                        'data_points': len(featured_data)
                    }
                    self.progress_tracker.record_learning_iteration(metrics, self.model_versions)

                else:
                    print("No models were successfully retrained.")

            except Exception as e:
                print(f"Error during model retraining: {e}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"Error in retrain_models: {e}")
            import traceback
            traceback.print_exc()

    def save_learning_state(self):
        """Save the current learning state to disk"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'learning_iterations': self.learning_iterations,
                'accuracy_history': list(self.accuracy_history),
                'error_patterns': self.error_patterns,
                'model_weights': self.model_weights_history[-1]['weights'] if self.model_weights_history else None
            }

            filename = f"models/learning_state_{datetime.now().strftime('%Y%m%d')}.json"

            with open(filename, 'w') as f:
                json.dump(state, f, indent=2)

            print(f"Saved learning state to {filename}")
        except Exception as e:
            print(f"Error saving learning state: {e}")

    def learn_from_candle(self, candle_data, previous_candles=None):
        """
        Learn from a single candle in real-time with enhanced learning capabilities

        Parameters:
        - candle_data: Dictionary or DataFrame row with candle data
        - previous_candles: Optional list of previous candles for context

        Returns:
        - bool: Whether learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping candle learning")
            return False

        try:
            print(f"\nLearning from new candle data...")

            # Convert candle to DataFrame if it's a dictionary
            if isinstance(candle_data, dict):
                candle_df = pd.DataFrame([candle_data])
            elif isinstance(candle_data, pd.Series):
                candle_df = pd.DataFrame([candle_data.to_dict()])
            else:
                candle_df = pd.DataFrame([candle_data])

            # Combine with previous candles if provided
            if previous_candles is not None:
                if isinstance(previous_candles, list):
                    # Convert list of dictionaries to DataFrame
                    prev_df = pd.DataFrame(previous_candles)
                    combined_df = pd.concat([prev_df, candle_df], ignore_index=True)
                elif isinstance(previous_candles, pd.DataFrame):
                    combined_df = pd.concat([previous_candles, candle_df], ignore_index=True)
                else:
                    print("Unsupported type for previous_candles. Using only current candle.")
                    combined_df = candle_df
            else:
                combined_df = candle_df

            # Check if we have enough data - reduced minimum requirement to 5 candles
            if len(combined_df) < 5:
                print(f"Not enough candles for learning ({len(combined_df)} available, need at least 5)")
                return False

            # Apply feature engineering if available
            if self.feature_engineer is not None:
                try:
                    print("Applying feature engineering to candle data...")
                    featured_data = self.feature_engineer(combined_df)
                    print(f"Generated {featured_data.shape[1]} features from candle data")
                except Exception as e:
                    print(f"Error engineering features for candle: {e}")
                    return False
            else:
                featured_data = combined_df

            # Extract market conditions from the latest candle
            market_conditions = {}
            try:
                # Calculate basic market conditions if not already present
                if 'volatility_20' in featured_data.columns:
                    market_conditions['volatility'] = featured_data['volatility_20'].iloc[-1]

                if 'adx_14' in featured_data.columns:
                    market_conditions['trend_strength'] = featured_data['adx_14'].iloc[-1] / 100.0

                if 'bullish_regime' in featured_data.columns and featured_data['bullish_regime'].iloc[-1] == 1:
                    market_conditions['regime'] = 'bullish'
                elif 'bearish_regime' in featured_data.columns and featured_data['bearish_regime'].iloc[-1] == 1:
                    market_conditions['regime'] = 'bearish'
                else:
                    market_conditions['regime'] = 'neutral'

                print(f"Extracted market conditions: {market_conditions}")
            except Exception as e:
                print(f"Error extracting market conditions: {e}")
                # Continue anyway, market conditions are optional

            # Create target variable based on price movement
            if 'close' in featured_data.columns:
                featured_data['target'] = (featured_data['close'].shift(-1) > featured_data['close']).astype(int)
                featured_data = featured_data.dropna()

            # Check if we have enough data after feature engineering
            if len(featured_data) < 5:
                print("Not enough data after feature engineering")
                return False

            # Apply incremental learning to models
            updated_models = []

            # Get the most recent data point for prediction validation
            validation_data = featured_data.iloc[-1:]
            training_data = featured_data.iloc[:-1]

            # Prepare X and y for training
            X_train = training_data.drop(['target'], axis=1) if 'target' in training_data.columns else training_data
            y_train = training_data['target'] if 'target' in training_data.columns else None

            if y_train is None or len(y_train) < 5:
                print("Not enough training data with target variable")
                return False

            # Prepare validation data
            X_val = validation_data.drop(['target'], axis=1) if 'target' in validation_data.columns else validation_data
            y_val = validation_data['target'].values[0] if 'target' in validation_data.columns else None

            if y_val is None:
                print("No validation target available")
                return False

            print(f"Prepared training data: {len(X_train)} samples, validation: 1 sample")
            print(f"Target distribution in training: {y_train.value_counts().to_dict()}")
            print(f"Validation target: {'UP' if y_val == 1 else 'DOWN'}")

            # Apply incremental learning to each model
            for model_name, model_data in self.models.items():
                if model_name not in ['lstm_gru', 'transformer', 'xgboost']:
                    continue

                print(f"Applying incremental learning to {model_name} model...")

                try:
                    model = model_data.get('model')
                    if model is None:
                        print(f"No model object available for {model_name}")
                        continue

                    # Make prediction before updating
                    prediction_before = None
                    try:
                        if model_name == 'lstm_gru':
                            from Models.LSTM_GRU_Deep_Learning_Model import predict_with_lstm_gru
                            pred_result = predict_with_lstm_gru(model, X_val)
                            prediction_before = pred_result.get('prediction')
                        elif model_name == 'transformer':
                            from Models.Transformer_Model import predict_with_transformer
                            pred_result = predict_with_transformer(model_data, X_val)
                            prediction_before = pred_result.get('prediction')
                        elif model_name == 'xgboost':
                            from Models.XGBoost_Model import predict_with_xgboost
                            pred_result = predict_with_xgboost(model, X_val)
                            prediction_before = pred_result.get('prediction')
                    except Exception as e:
                        print(f"Error making prediction with {model_name} before update: {e}")

                    # Apply incremental learning
                    updated = False
                    if model_name == 'lstm_gru' or model_name == 'transformer':
                        # For TensorFlow models, do a quick fine-tuning
                        if hasattr(model, 'fit'):
                            # Prepare data in the right format
                            if model_name == 'lstm_gru':
                                # LSTM requires 3D input [samples, time_steps, features]
                                seq_length = model_data.get('seq_length', 10)
                                if len(X_train) >= seq_length:
                                    # Create sequences
                                    sequences = []
                                    for i in range(len(X_train) - seq_length + 1):
                                        sequences.append(X_train.iloc[i:i+seq_length].values)
                                    X_sequences = np.array(sequences)
                                    y_sequences = y_train.iloc[seq_length-1:].values

                                    # Fine-tune the model
                                    model.fit(
                                        X_sequences, y_sequences,
                                        epochs=1,
                                        batch_size=1,
                                        verbose=0
                                    )
                                    updated = True
                                    print(f"Fine-tuned LSTM-GRU model with {len(sequences)} sequences")
                            else:  # transformer
                                # Transformer can work with 2D input [samples, features]
                                model.fit(
                                    X_train.values, y_train.values,
                                    epochs=1,
                                    batch_size=1,
                                    verbose=0
                                )
                                updated = True
                                print(f"Fine-tuned Transformer model with {len(X_train)} samples")
                    elif model_name == 'xgboost':
                        # For XGBoost, use partial_fit or a small update
                        import xgboost as xgb
                        # Create DMatrix
                        dtrain = xgb.DMatrix(X_train, label=y_train)
                        # Update model with a single iteration
                        model.update(dtrain, model.num_boosted_rounds())
                        updated = True
                        print(f"Updated XGBoost model with {len(X_train)} samples")

                    # Make prediction after updating
                    prediction_after = None
                    if updated:
                        try:
                            if model_name == 'lstm_gru':
                                pred_result = predict_with_lstm_gru(model, X_val)
                                prediction_after = pred_result.get('prediction')
                            elif model_name == 'transformer':
                                pred_result = predict_with_transformer(model_data, X_val)
                                prediction_after = pred_result.get('prediction')
                            elif model_name == 'xgboost':
                                pred_result = predict_with_xgboost(model, X_val)
                                prediction_after = pred_result.get('prediction')

                            # Check if prediction improved
                            correct_before = prediction_before == y_val
                            correct_after = prediction_after == y_val

                            if correct_before != correct_after:
                                if correct_after:
                                    print(f"✅ {model_name} prediction IMPROVED after learning!")
                                else:
                                    print(f"❌ {model_name} prediction WORSENED after learning!")
                            else:
                                print(f"⚠️ {model_name} prediction unchanged after learning")

                            # Record the learning result
                            self.record_prediction_feedback(
                                prediction=prediction_after,
                                actual_outcome=y_val,
                                confidence=pred_result.get('confidence', 0.5),
                                timestamp=datetime.now(),
                                prediction_details={'model': model_name, 'incremental_learning': True}
                            )

                            # Add to updated models list
                            updated_models.append(model_name)

                        except Exception as e:
                            print(f"Error making prediction with {model_name} after update: {e}")

                except Exception as e:
                    print(f"Error applying incremental learning to {model_name}: {e}")
                    import traceback
                    traceback.print_exc()

            # Update ensemble if models were updated
            if updated_models and self.ensemble:
                print(f"Updating ensemble weights based on {len(updated_models)} updated models...")
                # Slightly boost the weights of models that were just updated
                for model_name in updated_models:
                    self.ensemble.apply_immediate_adjustment(model_name, 0.02)
                print("Ensemble weights updated")

            # Increment learning iterations
            self.learning_iterations += 1

            # Save learning state periodically
            if self.learning_iterations % 10 == 0:
                self.save_learning_state()

            return len(updated_models) > 0

        except Exception as e:
            print(f"Error in learn_from_candle: {e}")
            import traceback
            traceback.print_exc()
            return False

    def learn_from_candles_batch(self, candles_data):
        """
        Learn from a batch of candles

        Parameters:
        - candles_data: DataFrame or list of dictionaries with candle data

        Returns:
        - bool: Whether learning was applied
        """
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow not available, skipping batch candle learning")
            return False

        try:
            # Convert to DataFrame if needed
            if isinstance(candles_data, list):
                candles_df = pd.DataFrame(candles_data)
            else:
                candles_df = candles_data

            print(f"\nLearning from batch of {len(candles_df)} candles...")

            # Check if we have enough data
            if len(candles_df) < 20:
                print(f"Not enough candles for batch learning ({len(candles_df)} available, need at least 20)")
                return False

            # Apply feature engineering if available
            if self.feature_engineer is not None:
                try:
                    print("Applying feature engineering to candle batch...")
                    featured_data = self.feature_engineer(candles_df)
                    print(f"Generated {featured_data.shape[1]} features from candle batch")
                except Exception as e:
                    print(f"Error engineering features for candle batch: {e}")
                    return False
            else:
                featured_data = candles_df

            # Create target variable based on price movement
            if 'close' in featured_data.columns:
                featured_data['target'] = (featured_data['close'].shift(-1) > featured_data['close']).astype(int)
                featured_data = featured_data.dropna()

            # Check if we have enough data after feature engineering
            if len(featured_data) < 10:
                print("Not enough data after feature engineering")
                return False

            # Split into training and validation sets
            validation_size = min(int(len(featured_data) * 0.2), 10)  # 20% or max 10 samples
            validation_data = featured_data.iloc[-validation_size:]
            training_data = featured_data.iloc[:-validation_size]

            # Prepare X and y for training
            X_train = training_data.drop(['target'], axis=1) if 'target' in training_data.columns else training_data
            y_train = training_data['target'] if 'target' in training_data.columns else None

            if y_train is None or len(y_train) < 10:
                print("Not enough training data with target variable")
                return False

            # Prepare validation data
            X_val = validation_data.drop(['target'], axis=1) if 'target' in validation_data.columns else validation_data
            y_val = validation_data['target'] if 'target' in validation_data.columns else None

            if y_val is None or len(y_val) < 1:
                print("No validation target available")
                return False

            print(f"Prepared training data: {len(X_train)} samples, validation: {len(X_val)} samples")
            print(f"Target distribution in training: {y_train.value_counts().to_dict()}")

            # Apply batch learning to each model
            updated_models = []
            for model_name, model_data in self.models.items():
                if model_name not in ['lstm_gru', 'transformer', 'xgboost']:
                    continue

                print(f"Applying batch learning to {model_name} model...")

                try:
                    model = model_data.get('model')
                    if model is None:
                        print(f"No model object available for {model_name}")
                        continue

                    # Make predictions before updating
                    accuracy_before = 0
                    try:
                        if model_name == 'lstm_gru':
                            from Models.LSTM_GRU_Deep_Learning_Model import predict_with_lstm_gru
                            correct = 0
                            for i in range(len(X_val)):
                                pred_result = predict_with_lstm_gru(model, X_val.iloc[i:i+1])
                                if pred_result.get('prediction') == y_val.iloc[i]:
                                    correct += 1
                            accuracy_before = correct / len(X_val)
                        elif model_name == 'transformer':
                            from Models.Transformer_Model import predict_with_transformer
                            correct = 0
                            for i in range(len(X_val)):
                                pred_result = predict_with_transformer(model_data, X_val.iloc[i:i+1])
                                if pred_result.get('prediction') == y_val.iloc[i]:
                                    correct += 1
                            accuracy_before = correct / len(X_val)
                        elif model_name == 'xgboost':
                            from Models.XGBoost_Model import predict_with_xgboost
                            correct = 0
                            for i in range(len(X_val)):
                                pred_result = predict_with_xgboost(model, X_val.iloc[i:i+1])
                                if pred_result.get('prediction') == y_val.iloc[i]:
                                    correct += 1
                            accuracy_before = correct / len(X_val)
                    except Exception as e:
                        print(f"Error making predictions with {model_name} before update: {e}")

                    # Apply batch learning
                    updated = False
                    if model_name == 'lstm_gru' or model_name == 'transformer':
                        # For TensorFlow models, do a quick fine-tuning
                        if hasattr(model, 'fit'):
                            # Prepare data in the right format
                            if model_name == 'lstm_gru':
                                # LSTM requires 3D input [samples, time_steps, features]
                                seq_length = model_data.get('seq_length', 10)
                                if len(X_train) >= seq_length:
                                    # Create sequences
                                    sequences = []
                                    for i in range(len(X_train) - seq_length + 1):
                                        sequences.append(X_train.iloc[i:i+seq_length].values)
                                    X_sequences = np.array(sequences)
                                    y_sequences = y_train.iloc[seq_length-1:].values

                                    # Fine-tune the model
                                    model.fit(
                                        X_sequences, y_sequences,
                                        epochs=3,
                                        batch_size=min(32, len(X_sequences)),
                                        verbose=0
                                    )
                                    updated = True
                                    print(f"Fine-tuned LSTM-GRU model with {len(sequences)} sequences")
                            else:  # transformer
                                # Transformer can work with 2D input [samples, features]
                                model.fit(
                                    X_train.values, y_train.values,
                                    epochs=3,
                                    batch_size=min(32, len(X_train)),
                                    verbose=0
                                )
                                updated = True
                                print(f"Fine-tuned Transformer model with {len(X_train)} samples")
                    elif model_name == 'xgboost':
                        # For XGBoost, use partial_fit or a small update
                        import xgboost as xgb
                        # Create DMatrix
                        dtrain = xgb.DMatrix(X_train, label=y_train)
                        # Update model with a few iterations
                        for _ in range(3):
                            model.update(dtrain, model.num_boosted_rounds())
                        updated = True
                        print(f"Updated XGBoost model with {len(X_train)} samples")

                    # Make predictions after updating
                    accuracy_after = 0
                    if updated:
                        try:
                            if model_name == 'lstm_gru':
                                correct = 0
                                for i in range(len(X_val)):
                                    pred_result = predict_with_lstm_gru(model, X_val.iloc[i:i+1])
                                    if pred_result.get('prediction') == y_val.iloc[i]:
                                        correct += 1
                                accuracy_after = correct / len(X_val)
                            elif model_name == 'transformer':
                                correct = 0
                                for i in range(len(X_val)):
                                    pred_result = predict_with_transformer(model_data, X_val.iloc[i:i+1])
                                    if pred_result.get('prediction') == y_val.iloc[i]:
                                        correct += 1
                                accuracy_after = correct / len(X_val)
                            elif model_name == 'xgboost':
                                correct = 0
                                for i in range(len(X_val)):
                                    pred_result = predict_with_xgboost(model, X_val.iloc[i:i+1])
                                    if pred_result.get('prediction') == y_val.iloc[i]:
                                        correct += 1
                                accuracy_after = correct / len(X_val)

                            # Check if accuracy improved
                            if accuracy_after > accuracy_before:
                                print(f"✅ {model_name} accuracy IMPROVED: {accuracy_before:.4f} → {accuracy_after:.4f}")
                            elif accuracy_after < accuracy_before:
                                print(f"❌ {model_name} accuracy DECREASED: {accuracy_before:.4f} → {accuracy_after:.4f}")
                            else:
                                print(f"⚠️ {model_name} accuracy unchanged: {accuracy_before:.4f}")

                            # Record the learning results
                            for i in range(len(X_val)):
                                if model_name == 'lstm_gru':
                                    pred_result = predict_with_lstm_gru(model, X_val.iloc[i:i+1])
                                elif model_name == 'transformer':
                                    pred_result = predict_with_transformer(model_data, X_val.iloc[i:i+1])
                                elif model_name == 'xgboost':
                                    pred_result = predict_with_xgboost(model, X_val.iloc[i:i+1])

                                self.record_prediction_feedback(
                                    prediction=pred_result.get('prediction'),
                                    actual_outcome=y_val.iloc[i],
                                    confidence=pred_result.get('confidence', 0.5),
                                    timestamp=datetime.now(),
                                    prediction_details={'model': model_name, 'batch_learning': True}
                                )

                            # Add to updated models list
                            updated_models.append(model_name)

                        except Exception as e:
                            print(f"Error making predictions with {model_name} after update: {e}")

                except Exception as e:
                    print(f"Error applying batch learning to {model_name}: {e}")
                    import traceback
                    traceback.print_exc()

            # Update ensemble if models were updated
            if updated_models and self.ensemble:
                print(f"Updating ensemble weights based on {len(updated_models)} updated models...")
                # Adjust weights based on validation performance
                for model_name in updated_models:
                    # Calculate accuracy on validation set
                    correct = 0
                    total = 0
                    try:
                        for i in range(len(X_val)):
                            if model_name == 'lstm_gru':
                                from Models.LSTM_GRU_Deep_Learning_Model import predict_with_lstm_gru
                                pred_result = predict_with_lstm_gru(self.models[model_name]['model'], X_val.iloc[i:i+1])
                            elif model_name == 'transformer':
                                from Models.Transformer_Model import predict_with_transformer
                                pred_result = predict_with_transformer(self.models[model_name], X_val.iloc[i:i+1])
                            elif model_name == 'xgboost':
                                from Models.XGBoost_Model import predict_with_xgboost
                                pred_result = predict_with_xgboost(self.models[model_name]['model'], X_val.iloc[i:i+1])
                            else:
                                continue

                            if pred_result.get('prediction') == y_val.iloc[i]:
                                correct += 1
                            total += 1

                        if total > 0:
                            accuracy = correct / total
                            # Adjust weight based on accuracy
                            adjustment = (accuracy - 0.5) * 0.1  # Scale adjustment: max ±0.05
                            self.ensemble.apply_immediate_adjustment(model_name, adjustment)
                            print(f"Adjusted {model_name} weight by {adjustment:.4f} based on accuracy {accuracy:.4f}")
                    except Exception as e:
                        print(f"Error adjusting ensemble weights for {model_name}: {e}")

                print("Ensemble weights updated")

            # Increment learning iterations
            self.learning_iterations += 1

            # Save learning state
            self.save_learning_state()

            # Save model snapshots periodically
            if self.learning_iterations % 5 == 0:
                self.save_model_weights_snapshot(batch=True)

            return len(updated_models) > 0

        except Exception as e:
            print(f"Error in learn_from_candles_batch: {e}")
            import traceback
            traceback.print_exc()
            return False
