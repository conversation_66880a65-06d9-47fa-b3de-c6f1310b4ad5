#!/usr/bin/env python
"""
Test script for XGBoost prediction

This script tests the XGBoost prediction functionality with sample data
to verify that the fix for handling non-numeric columns works correctly.
"""

import os
import pandas as pd
import numpy as np
import joblib
from Models.XGBoost_Model import predict_with_xgboost
from Models.Feature_Engineering import engineer_features

def main():
    """Main function to test XGBoost prediction"""
    print("Testing XGBoost prediction...")

    # Find the latest XGBoost model
    model_dir = 'models'
    model_files = [f for f in os.listdir(model_dir) if f.endswith('.pkl')]

    if not model_files:
        print("No model files found")
        return

    # Get the latest model
    latest_model = max(model_files, key=lambda x: os.path.getmtime(os.path.join(model_dir, x)))
    model_path = os.path.join(model_dir, latest_model)

    print(f"Using model: {model_path}")

    # Load the model
    model = joblib.load(model_path)

    # Create sample data with more rows (needed for technical indicators)
    # Generate 100 rows of sample data
    np.random.seed(42)  # For reproducibility
    n_samples = 100

    # Generate random price data with a trend
    base_price = 100.0
    trend = np.cumsum(np.random.normal(0.01, 0.1, n_samples))  # Cumulative sum creates a random walk

    # Create open, high, low, close prices
    opens = base_price + trend
    closes = opens + np.random.normal(0, 0.5, n_samples)
    highs = np.maximum(opens, closes) + np.abs(np.random.normal(0, 0.3, n_samples))
    lows = np.minimum(opens, closes) - np.abs(np.random.normal(0, 0.3, n_samples))

    # Create colors based on price movement
    colors = ['green' if closes[i] >= opens[i] else 'red' for i in range(n_samples)]

    # Create market regime
    regimes = []
    for i in range(n_samples):
        if i < 2:
            regimes.append('neutral')
        else:
            if trend[i] > trend[i-1] and trend[i-1] > trend[i-2]:
                regimes.append('bullish')
            elif trend[i] < trend[i-1] and trend[i-1] < trend[i-2]:
                regimes.append('bearish')
            else:
                regimes.append('neutral')

    # Create DataFrame
    sample_data = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': np.random.randint(1000, 2000, n_samples),
        'color': colors,
        'market_regime': regimes,
        'time': pd.date_range(start='2023-01-01', periods=n_samples, freq='1min')
    })

    # Apply feature engineering
    print("Applying feature engineering...")
    try:
        featured_data = engineer_features(sample_data)
        print("Feature engineering completed successfully")
    except Exception as e:
        print(f"Error in feature engineering: {e}")
        print("Using sample data with basic features instead")

        # Create basic features manually
        featured_data = sample_data.copy()
        featured_data['returns'] = featured_data['close'].pct_change()
        featured_data['log_returns'] = np.log(featured_data['close'] / featured_data['close'].shift(1))
        featured_data['sma_5'] = featured_data['close'].rolling(window=5).mean()
        featured_data['sma_10'] = featured_data['close'].rolling(window=10).mean()
        featured_data['sma_20'] = featured_data['close'].rolling(window=20).mean()
        featured_data['volatility'] = featured_data['returns'].rolling(window=10).std()

        # Drop NaN values
        featured_data = featured_data.dropna()

    print(f"Data shape after feature engineering: {featured_data.shape}")
    print(f"Sample columns: {list(featured_data.columns)[:10]}")

    # Make prediction
    print("\nMaking prediction...")
    prediction = predict_with_xgboost(model, featured_data)

    print("\nPrediction result:")
    for key, value in prediction.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
