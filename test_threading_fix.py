#!/usr/bin/env python3
"""
Test script to verify threading fixes in trading_ui.py
This script tests the thread-safe UI update mechanisms
"""

import sys
import time
import threading
from PyQt5 import QtWidgets, Qt<PERSON><PERSON>

def test_threading_fixes():
    """Test the threading fixes"""
    print("🔧 Testing Threading Fixes...")
    
    try:
        # Create QApplication
        app = QtWidgets.QApplication(sys.argv)
        
        # Import the fixed trading UI
        from trading_ui import TradingUI
        
        print("✅ Successfully imported TradingUI with threading fixes")
        
        # Create UI instance
        ui = TradingUI()
        
        # Test 1: Check if signals are properly defined
        print("\n📡 Testing Signals...")
        signals_to_test = [
            'data_updated',
            'chart_update_requested', 
            'status_update_requested'
        ]
        
        for signal_name in signals_to_test:
            if hasattr(ui, signal_name):
                print(f"  ✅ {signal_name}: Found")
            else:
                print(f"  ❌ {signal_name}: Missing")
        
        # Test 2: Check thread-safe methods
        print("\n🔒 Testing Thread-Safe Methods...")
        methods_to_test = [
            'queue_ui_update',
            'execute_ui_update',
            'update_status_safe',
            'start_chart_update_timer'
        ]
        
        for method_name in methods_to_test:
            if hasattr(ui, method_name):
                print(f"  ✅ {method_name}: Found")
            else:
                print(f"  ❌ {method_name}: Missing")
        
        # Test 3: Test signal connections
        print("\n🔗 Testing Signal Connections...")
        try:
            # Test status update signal
            ui.status_update_requested.emit("Test message", "color: blue;")
            print("  ✅ status_update_requested signal works")
            
            # Test chart update signal
            ui.chart_update_requested.emit()
            print("  ✅ chart_update_requested signal works")
            
        except Exception as e:
            print(f"  ❌ Signal test failed: {e}")
        
        # Test 4: Test thread-safe UI updates
        print("\n🧵 Testing Thread-Safe UI Updates...")
        
        def background_task():
            """Simulate background task that needs to update UI"""
            time.sleep(0.1)  # Simulate work
            
            # Test thread-safe status update
            ui.status_update_requested.emit(
                "Background task completed",
                "color: green; font-weight: bold;"
            )
            
            print("  ✅ Background thread UI update completed")
        
        # Run background task
        thread = threading.Thread(target=background_task, daemon=True)
        thread.start()
        
        # Wait for background task
        thread.join(timeout=1.0)
        
        # Test 5: Test queue system
        print("\n📋 Testing Queue System...")
        test_executed = False
        
        def test_queue_function():
            nonlocal test_executed
            test_executed = True
            print("  ✅ Queue function executed successfully")
        
        # Queue the test function
        ui.queue_ui_update(test_queue_function)
        
        # Process events to execute queued updates
        app.processEvents()
        
        if test_executed:
            print("  ✅ Queue system working correctly")
        else:
            print("  ❌ Queue system not working")
        
        print("\n🎉 Threading fix tests completed!")
        print("✅ All threading issues should now be resolved")
        
        # Clean up
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Error during threading tests: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Threading Fix Tests...")
    success = test_threading_fixes()
    
    if success:
        print("\n✅ All tests passed! Threading fixes are working correctly.")
        print("🔧 The QObject threading error should now be resolved.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    sys.exit(0 if success else 1)
