#!/usr/bin/env python
"""
Connect with PIN Script for Quotex API
This script connects to the Quotex API using a PIN code
"""

import asyncio
import os
import sys
import time
import json
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
    print("Successfully imported quotexapi module")
except ImportError as e:
    print(f"Error importing quotexapi module: {e}")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

# Read credentials from config.ini
def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""

    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")

            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')

            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")

    return email, password

# Connect with PIN function
async def connect_with_pin(email, password, pin):
    """Connect to Quotex API using PIN"""
    print(f"Connecting to Quotex API with email: {email} and PIN: {pin}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # First try with our improved PIN helper
        try:
            import quotex_pin_helper
            print("Using quotex_pin_helper for PIN verification...")

            # Create PIN helper
            helper = quotex_pin_helper.QuotexPINHelper(email, password)

            # Submit PIN
            print("Submitting PIN...")
            success, result = helper.submit_pin(pin)

            if success:
                print("PIN verification successful!")

                # Check if result is a dictionary with session data
                if isinstance(result, dict):
                    print("Session data obtained:")
                    if "token" in result:
                        token = result["token"]
                        token_preview = f"{token[:10]}..." if len(token) > 10 else token
                        print(f"- Token: {token_preview}")
                    if "user_id" in result:
                        print(f"- User ID: {result['user_id']}")

                    # Save session data to file
                    try:
                        with open('quotex_session.json', 'w') as f:
                            json.dump(result, f, indent=4)
                        print("Saved session data to quotex_session.json")
                    except Exception as e:
                        print(f"Error saving session data: {e}")

                    # Try to get assets using the session data
                    try:
                        # Create client
                        client = Quotex(email, password)

                        # Set session data if available
                        if "token" in result and hasattr(client, 'api') and client.api:
                            client.api.ssid = result["token"]
                            print("Set session token in API client")

                        # Try to connect
                        connected = await client.connect()
                        print(f"Connection with session data: {'Success' if connected else 'Failed'}")

                        # Get assets
                        print("Fetching available assets...")
                        try:
                            # Try different method names that might exist in the API
                            try:
                                print("Trying get_all_assets method...")
                                assets = await client.get_all_assets()
                            except AttributeError:
                                try:
                                    print("Trying get_all_asset method...")
                                    assets = await client.get_all_asset()
                                except AttributeError:
                                    try:
                                        print("Trying get_assets method...")
                                        assets = await client.get_assets()
                                    except AttributeError:
                                        print("Trying get_asset method...")
                                        assets = await client.get_asset()

                            print(f"Total assets: {len(assets) if assets else 0}")

                            # Save the assets to a file for later use
                            try:
                                with open('quotex_assets.json', 'w') as f:
                                    json.dump(assets, f, indent=4)
                                print("Saved assets to quotex_assets.json")
                            except Exception as save_error:
                                print(f"Error saving assets to file: {save_error}")

                            return True, assets
                        except Exception as asset_error:
                            print(f"Error fetching assets: {asset_error}")
                            import traceback
                            traceback.print_exc()
                            return True, None
                    except Exception as client_error:
                        print(f"Error using session data: {client_error}")
                        return True, None
                else:
                    print(f"Result: {result}")
                    return True, None
            else:
                print("PIN verification failed!")
                print(f"Error: {result}")

                # Fall back to standard API methods
                print("Falling back to standard API methods...")
        except ImportError:
            print("quotex_pin_helper not available, using standard API methods...")
        except Exception as helper_error:
            print(f"Error using PIN helper: {helper_error}")
            print("Falling back to standard API methods...")

        # Create client
        print("Creating Quotex client...")
        client = Quotex(email, password)

        # Try to connect with PIN
        print("Connecting with PIN using standard API methods...")

        # Try different methods that might exist in the API
        try:
            # First try with a dedicated method if it exists
            if hasattr(client, 'connect_with_pin'):
                print("Using connect_with_pin method...")
                connected = await client.connect_with_pin(pin)
            elif hasattr(client, 'submit_pin'):
                print("Using submit_pin method...")
                await client.submit_pin(pin)
                connected = await client.connect()
            elif hasattr(client, 'send_pin'):
                print("Using send_pin method...")
                await client.send_pin(pin)
                connected = await client.connect()
            elif hasattr(client, 'verify_pin'):
                print("Using verify_pin method...")
                await client.verify_pin(pin)
                connected = await client.connect()
            else:
                # If no dedicated method exists, try to connect normally
                # The PIN might be handled internally by the API
                print("No dedicated PIN method found. Trying normal connect...")
                connected = await client.connect()

            if connected:
                print("Successfully connected to Quotex API with PIN")

                # Test by getting assets
                print("Fetching available assets...")
                try:
                    # Try different method names that might exist in the API
                    try:
                        print("Trying get_all_assets method...")
                        assets = await client.get_all_assets()
                    except AttributeError:
                        try:
                            print("Trying get_all_asset method...")
                            assets = await client.get_all_asset()
                        except AttributeError:
                            try:
                                print("Trying get_assets method...")
                                assets = await client.get_assets()
                            except AttributeError:
                                print("Trying get_asset method...")
                                assets = await client.get_asset()

                    print(f"Total assets: {len(assets) if assets else 0}")

                    # Save the assets to a file for later use
                    try:
                        with open('quotex_assets.json', 'w') as f:
                            json.dump(assets, f, indent=4)
                        print("Saved assets to quotex_assets.json")
                    except Exception as save_error:
                        print(f"Error saving assets to file: {save_error}")

                    return True, assets
                except Exception as asset_error:
                    print(f"Error fetching assets: {asset_error}")
                    import traceback
                    traceback.print_exc()
                    return True, None
            else:
                print("Failed to connect to Quotex API with PIN")
                return False, None
        except Exception as method_error:
            print(f"Error in connect method: {method_error}")
            return False, None
    except Exception as e:
        print(f"Error connecting with PIN: {e}")
        import traceback
        traceback.print_exc()
        return False, None

# Main function
async def main():
    """Main function"""
    print("=== Quotex API Connect with PIN ===")

    # Get PIN from command line argument
    if len(sys.argv) > 1:
        pin = sys.argv[1]
    else:
        pin = input("Enter PIN code from email: ")

    if not pin:
        print("No PIN provided. Exiting.")
        return

    # Read credentials
    email, password = read_credentials()

    if not email or not password:
        print("No credentials provided. Exiting.")
        return

    # Connect with PIN
    success, assets = await connect_with_pin(email, password, pin)

    if success:
        print("Connection successful")
        if assets:
            print(f"Retrieved {len(assets)} assets")
    else:
        print("Connection failed")
        print("Please try again with a different PIN or check your credentials.")

# Run the main function
if __name__ == "__main__":
    # Create a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # Run the main function
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("Connection interrupted by user")
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the event loop
        loop.close()
