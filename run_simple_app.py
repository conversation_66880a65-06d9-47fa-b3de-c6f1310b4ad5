#!/usr/bin/env python
"""
Simple Trading App Launcher
Clean, thread-safe trading application
"""

import sys
import os

def main():
    """Launch the simple trading application"""
    print("=" * 50)
    print("🚀 SIMPLE TRADING SYSTEM")
    print("=" * 50)
    print()
    
    # Check required files
    required_files = ['simple_trading_app.py', 'quotex_assets.json']
    missing = [f for f in required_files if not os.path.exists(f)]
    
    if missing:
        print("❌ Missing files:")
        for f in missing:
            print(f"   - {f}")
        return False
    
    print("✅ All files found")
    print()
    
    try:
        print("📱 Starting simple trading application...")
        from simple_trading_app import main as run_app
        
        print("🎯 Features:")
        print("   • Clean, simple interface")
        print("   • Manual asset selection")
        print("   • Thread-safe operation")
        print("   • No complex initialization")
        print("   • Fast startup")
        print()
        
        run_app()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
