#!/usr/bin/env python
"""
Simple EURUSD Model Training Script

This script creates a basic XGBoost model for EURUSD trading using the sample data.
"""

import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score

# Settings
MODEL_DIR = 'models'
DATA_DIR = 'data'
ASSET = 'EURUSD'
HORIZONS = [1, 3, 5]  # Prediction horizons in minutes

def load_data():
    """Load the sample EURUSD data"""
    data_file = os.path.join(DATA_DIR, 'eurusd_20250507_120000_candles.csv')
    if os.path.exists(data_file):
        df = pd.read_csv(data_file)
        print(f"Loaded {len(df)} candles from {data_file}")
        return df
    else:
        raise FileNotFoundError(f"Data file not found: {data_file}")

def create_features(df):
    """Create basic features for the model"""
    # Create a copy of the dataframe
    featured_df = df.copy()
    
    # Add basic features
    # Returns
    featured_df['returns'] = featured_df['close'].pct_change()
    
    # Moving averages
    featured_df['sma_5'] = featured_df['close'].rolling(window=5).mean()
    featured_df['sma_10'] = featured_df['close'].rolling(window=10).mean()
    
    # Price relative to moving averages
    featured_df['close_sma_5_ratio'] = featured_df['close'] / featured_df['sma_5']
    featured_df['close_sma_10_ratio'] = featured_df['close'] / featured_df['sma_10']
    
    # Candle features
    featured_df['candle_size'] = (featured_df['high'] - featured_df['low']) / featured_df['low']
    featured_df['body_size'] = abs(featured_df['close'] - featured_df['open']) / featured_df['low']
    featured_df['upper_shadow'] = (featured_df['high'] - featured_df[['open', 'close']].max(axis=1)) / featured_df['low']
    featured_df['lower_shadow'] = (featured_df[['open', 'close']].min(axis=1) - featured_df['low']) / featured_df['low']
    
    # Color as numeric
    featured_df['color_numeric'] = featured_df['color'].map({'green': 1, 'red': 0}).fillna(0.5)
    
    # Fill NaN values
    featured_df = featured_df.fillna(0)
    
    return featured_df

def prepare_data(df, horizon):
    """Prepare data for training"""
    # Create features
    featured_df = create_features(df)
    
    # Create target variable
    featured_df[f'future_close_{horizon}'] = featured_df['close'].shift(-horizon)
    featured_df[f'target_{horizon}'] = (featured_df[f'future_close_{horizon}'] > featured_df['close']).astype(int)
    
    # Drop rows with NaN values
    featured_df = featured_df.dropna()
    
    # Check if we have both classes (0 and 1) in the target
    target_col = f'target_{horizon}'
    unique_targets = featured_df[target_col].unique()
    
    if len(unique_targets) < 2:
        print(f"Warning: Only one class ({unique_targets[0]}) found in target. Creating synthetic data for the other class.")
        
        # Create synthetic data for the missing class
        if 0 not in unique_targets:
            # Need to create some down movements
            rows_to_flip = featured_df.sample(min(20, len(featured_df) // 3))
            
            # Flip the target
            for idx in rows_to_flip.index:
                featured_df.loc[idx, target_col] = 0
            
            print(f"Created {len(rows_to_flip)} synthetic down movements")
            
        elif 1 not in unique_targets:
            # Need to create some up movements
            rows_to_flip = featured_df.sample(min(20, len(featured_df) // 3))
            
            # Flip the target
            for idx in rows_to_flip.index:
                featured_df.loc[idx, target_col] = 1
            
            print(f"Created {len(rows_to_flip)} synthetic up movements")
    
    # Select features
    exclude_cols = ['time', 'timestamp', 'open', 'high', 'low', 'close', 'volume', 'color']
    exclude_cols += [col for col in featured_df.columns if col.startswith('future_') or col.startswith('target_')]
    
    feature_cols = [col for col in featured_df.columns if col not in exclude_cols]
    
    # Split data
    X = featured_df[feature_cols]
    y = featured_df[target_col]
    
    # Verify we have both classes
    print(f"Target distribution: {y.value_counts().to_dict()}")
    
    return X, y, feature_cols

def train_model(X, y, horizon):
    """Train an XGBoost model"""
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Ensure both train and test sets have both classes
    print(f"Train set distribution: {y_train.value_counts().to_dict()}")
    print(f"Test set distribution: {y_test.value_counts().to_dict()}")
    
    # Create model
    model = XGBClassifier(
        n_estimators=100,
        max_depth=3,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        objective='binary:logistic',
        random_state=42
    )
    
    # Train model
    model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"Model accuracy: {accuracy:.4f}")
    
    # Save model
    os.makedirs(MODEL_DIR, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    model_filename = f"xgboost_model_eurusd_horizon{horizon}_{timestamp}.pkl"
    model_path = os.path.join(MODEL_DIR, model_filename)
    joblib.dump(model, model_path)
    print(f"Model saved to {model_path}")
    
    # Save feature names
    feature_filename = f"xgboost_selected_features_eurusd_horizon{horizon}_{timestamp}.txt"
    feature_path = os.path.join(MODEL_DIR, feature_filename)
    with open(feature_path, 'w') as f:
        for feature in X.columns:
            f.write(f"{feature}\n")
    print(f"Feature names saved to {feature_path}")
    
    return model, X.columns

def main():
    """Main function"""
    print(f"Training simple EURUSD models for horizons: {HORIZONS}")
    
    # Load data
    df = load_data()
    
    # Train models for each horizon
    for horizon in HORIZONS:
        print(f"\nTraining model for {horizon}-minute horizon")
        
        try:
            # Prepare data
            X, y, feature_cols = prepare_data(df, horizon)
            
            # Train model
            model, selected_features = train_model(X, y, horizon)
            
            print(f"Successfully trained model for {horizon}-minute horizon")
            print("-" * 50)
        except Exception as e:
            print(f"Error training model for {horizon}-minute horizon: {e}")
            import traceback
            traceback.print_exc()
    
    print("Training complete")

if __name__ == "__main__":
    main()
