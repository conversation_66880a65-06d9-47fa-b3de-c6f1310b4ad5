import tensorflow as tf
from tensorflow.keras.models import Sequential, Model, load_model
from tensorflow.keras.layers import Dense, Input, Concatenate, LSTM, GRU, Dropout, BatchNormalization
from tensorflow.keras.layers import Conv1D, Flatten, Bidirectional
from tensorflow.keras.optimizers import <PERSON>, RMSprop
from tensorflow.keras.regularizers import l1_l2
import numpy as np
import pandas as pd
import random
import os
import datetime
import joblib
import matplotlib.pyplot as plt
from collections import deque

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)
random.seed(42)

class TradingEnvironment:
    """
    Trading environment for binary options on USDARS OTC

    This environment simulates binary options trading with realistic payouts and risk management.
    """
    def __init__(self, data, initial_balance=1000, trade_fee=0.01, window_size=30,
                 payout_ratio=0.8, risk_per_trade=0.01, feature_columns=None):
        """
        Initialize the trading environment

        Parameters:
        - data: DataFrame containing market data
        - initial_balance: Initial account balance
        - trade_fee: Fee per trade (percentage)
        - window_size: Size of the observation window
        - payout_ratio: Payout ratio for correct predictions (e.g., 0.8 = 80% profit)
        - risk_per_trade: Percentage of balance to risk per trade
        - feature_columns: List of feature columns to use for state representation
        """
        self.data = data
        self.initial_balance = initial_balance
        self.trade_fee = trade_fee
        self.window_size = window_size
        self.payout_ratio = payout_ratio
        self.risk_per_trade = risk_per_trade

        # Set default feature columns if not provided
        if feature_columns is None:
            self.feature_columns = [
                'close', 'returns', 'sma_5', 'sma_20', 'rsi_14', 'macd',
                'stoch_k_14', 'adx_14', 'cci_20', 'atr_14', 'bollinger_pct_20'
            ]
        else:
            self.feature_columns = feature_columns

        # Ensure all feature columns exist in the data
        for col in self.feature_columns:
            if col not in self.data.columns:
                raise ValueError(f"Feature column '{col}' not found in data")

        # Initialize environment
        self.reset()

    def reset(self):
        """
        Reset the environment to initial state

        Returns:
        - initial_state: Initial state representation
        """
        self.balance = self.initial_balance
        self.current_step = self.window_size  # Start after window_size to have enough history
        self.trades = []
        self.portfolio_values = [self.initial_balance]
        self.consecutive_losses = 0
        self.max_drawdown = 0
        self.peak_balance = self.initial_balance

        # State: current market features
        initial_state = self._get_state()
        return initial_state

    def _get_state(self):
        """
        Get the current state representation

        Returns:
        - state: Dictionary containing market features and portfolio state
        """
        if self.current_step >= len(self.data):
            return None

        # Use a window of data as state
        window_start = max(0, self.current_step - self.window_size)
        window_data = self.data.iloc[window_start:self.current_step]

        # Extract relevant features for the state
        if len(window_data) < self.window_size:
            # Pad with zeros if not enough history
            padding = self.window_size - len(window_data)
            features = np.zeros((self.window_size, len(self.feature_columns)))
            features[padding:] = window_data[self.feature_columns].values
        else:
            features = window_data[self.feature_columns].values

        # Add portfolio state information
        portfolio_state = np.array([
            self.balance / self.initial_balance,  # Normalized balance
            self.consecutive_losses / 10,  # Normalized consecutive losses (capped at 10)
            self.max_drawdown  # Max drawdown as a percentage
        ])

        return {
            'market_features': features,
            'portfolio_state': portfolio_state
        }

    def step(self, action):
        """
        Execute action and return new state, reward, and done flag

        Parameters:
        - action: 0 = predict down, 1 = predict up

        Returns:
        - next_state: Next state representation
        - reward: Reward for the action
        - done: Whether the episode is done
        - info: Additional information
        """
        if self.current_step >= len(self.data) - 1:
            return None, 0, True, {'reason': 'end_of_data'}

        # Get current price and next price
        current_price = self.data.iloc[self.current_step]['close']
        next_price = self.data.iloc[self.current_step + 1]['close']

        # Determine if price went up or down
        price_went_up = next_price > current_price

        # Calculate trade amount based on risk management
        trade_amount = self.balance * self.risk_per_trade

        # Calculate reward
        if (action == 1 and price_went_up) or (action == 0 and not price_went_up):
            # Correct prediction, gain reward
            reward = self.payout_ratio * trade_amount
            self.consecutive_losses = 0
            trade_result = 'win'
        else:
            # Incorrect prediction, lose investment
            reward = -trade_amount
            self.consecutive_losses += 1
            trade_result = 'loss'

        # Apply trade fee
        fee = trade_amount * self.trade_fee
        reward -= fee

        # Update balance
        old_balance = self.balance
        self.balance += reward

        # Update peak balance and max drawdown
        if self.balance > self.peak_balance:
            self.peak_balance = self.balance

        current_drawdown = (self.peak_balance - self.balance) / self.peak_balance
        self.max_drawdown = max(self.max_drawdown, current_drawdown)

        # Record portfolio value
        self.portfolio_values.append(self.balance)

        # Record trade
        self.trades.append({
            'step': self.current_step,
            'timestamp': self.data.index[self.current_step],
            'action': 'UP' if action == 1 else 'DOWN',
            'price': current_price,
            'next_price': next_price,
            'amount': trade_amount,
            'fee': fee,
            'reward': reward,
            'balance': self.balance,
            'result': trade_result
        })

        # Move to next step
        self.current_step += 1
        next_state = self._get_state()

        # Check if done
        done = self.current_step >= len(self.data) - 1 or self.balance <= 0

        # Additional info
        info = {
            'trade_result': trade_result,
            'balance_change': self.balance - old_balance,
            'balance_change_pct': (self.balance - old_balance) / old_balance,
            'current_balance': self.balance,
            'max_drawdown': self.max_drawdown,
            'consecutive_losses': self.consecutive_losses
        }

        return next_state, reward, done, info

class DQNAgent:
    """
    Deep Q-Network agent for binary options trading

    This agent uses a deep neural network to approximate the Q-function for reinforcement learning.
    It implements several advanced techniques including:
    - Double DQN
    - Dueling DQN architecture
    - Prioritized Experience Replay
    - Convolutional layers for time series processing
    """
    def __init__(self, state_shape, action_size=2, memory_size=10000, gamma=0.95,
                 epsilon_start=1.0, epsilon_min=0.01, epsilon_decay=0.995,
                 learning_rate=0.001, batch_size=64, update_target_freq=5,
                 model_type='advanced'):
        """
        Initialize the DQN agent

        Parameters:
        - state_shape: Dictionary containing shapes of state components
        - action_size: Number of possible actions
        - memory_size: Size of experience replay memory
        - gamma: Discount factor for future rewards
        - epsilon_start: Initial exploration rate
        - epsilon_min: Minimum exploration rate
        - epsilon_decay: Decay rate for exploration
        - learning_rate: Learning rate for optimizer
        - batch_size: Batch size for training
        - update_target_freq: Frequency of target network updates
        - model_type: Type of model architecture ('simple', 'cnn', 'advanced', 'dueling')
        """
        self.state_shape = state_shape
        self.action_size = action_size
        self.memory = deque(maxlen=memory_size)
        self.gamma = gamma  # discount factor
        self.epsilon = epsilon_start  # exploration rate
        self.epsilon_min = epsilon_min
        self.epsilon_decay = epsilon_decay
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.update_target_freq = update_target_freq
        self.model_type = model_type
        self.train_step_counter = 0

        # Create main and target networks
        self.model = self._build_model()
        self.target_model = self._build_model()
        self.update_target_model()

        # Training metrics
        self.loss_history = []
        self.reward_history = []
        self.q_value_history = []

    def _build_model(self):
        """
        Build a neural network model for DQN

        Returns:
        - model: Compiled Keras model
        """
        # Get shapes from state_shape dictionary
        market_feature_shape = self.state_shape['market_features']
        portfolio_state_shape = self.state_shape['portfolio_state']

        if self.model_type == 'simple':
            # Simple feedforward network
            # Market features input
            market_input = Input(shape=market_feature_shape)
            market_flat = Flatten()(market_input)
            market_x = Dense(64, activation='relu')(market_flat)
            market_x = Dense(32, activation='relu')(market_x)

            # Portfolio state input
            portfolio_input = Input(shape=(portfolio_state_shape,))
            portfolio_x = Dense(8, activation='relu')(portfolio_input)

            # Combine inputs
            combined = Concatenate()([market_x, portfolio_x])

            # Additional processing
            x = Dense(32, activation='relu')(combined)
            x = Dense(16, activation='relu')(x)

            # Output layer (Q-values for each action)
            outputs = Dense(self.action_size, activation='linear')(x)

        elif self.model_type == 'cnn':
            # CNN-based architecture for time series
            # Market features input - reshape for CNN
            market_input = Input(shape=market_feature_shape)

            # Add channel dimension for Conv1D
            market_reshaped = tf.expand_dims(market_input, axis=-1)

            # CNN layers
            conv1 = Conv1D(filters=32, kernel_size=3, padding='same', activation='relu')(market_reshaped)
            conv2 = Conv1D(filters=64, kernel_size=5, padding='same', activation='relu')(conv1)
            conv3 = Conv1D(filters=128, kernel_size=7, padding='same', activation='relu')(conv2)

            # Flatten CNN output
            market_flat = Flatten()(conv3)
            market_x = Dense(64, activation='relu')(market_flat)

            # Portfolio state input
            portfolio_input = Input(shape=(portfolio_state_shape,))
            portfolio_x = Dense(16, activation='relu')(portfolio_input)

            # Combine inputs
            combined = Concatenate()([market_x, portfolio_x])

            # Additional processing
            x = Dense(64, activation='relu')(combined)
            x = Dropout(0.2)(x)
            x = Dense(32, activation='relu')(x)

            # Output layer (Q-values for each action)
            outputs = Dense(self.action_size, activation='linear')(x)

        elif self.model_type == 'advanced':
            # Advanced architecture with LSTM/GRU layers
            # Market features input
            market_input = Input(shape=market_feature_shape)

            # CNN layer to extract local patterns
            conv1 = Conv1D(filters=32, kernel_size=3, padding='same', activation='relu')(market_input)
            conv1 = BatchNormalization()(conv1)

            # LSTM layer to capture temporal dependencies
            lstm = LSTM(64, return_sequences=True)(conv1)
            lstm = BatchNormalization()(lstm)
            lstm = Dropout(0.3)(lstm)

            # GRU layer
            gru = GRU(32, return_sequences=False)(lstm)
            gru = BatchNormalization()(gru)
            gru = Dropout(0.3)(gru)

            # Portfolio state input
            portfolio_input = Input(shape=(portfolio_state_shape,))
            portfolio_x = Dense(16, activation='relu')(portfolio_input)
            portfolio_x = BatchNormalization()(portfolio_x)

            # Combine inputs
            combined = Concatenate()([gru, portfolio_x])

            # Dense layers
            x = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(combined)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)

            x = Dense(32, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
            x = BatchNormalization()(x)
            x = Dropout(0.3)(x)

            # Output layer (Q-values for each action)
            outputs = Dense(self.action_size, activation='linear')(x)

        elif self.model_type == 'dueling':
            # Dueling DQN architecture
            # Market features input
            market_input = Input(shape=market_feature_shape)

            # CNN layer to extract local patterns
            conv1 = Conv1D(filters=32, kernel_size=3, padding='same', activation='relu')(market_input)
            conv1 = BatchNormalization()(conv1)

            # LSTM layer
            lstm = LSTM(64, return_sequences=False)(conv1)
            lstm = BatchNormalization()(lstm)

            # Portfolio state input
            portfolio_input = Input(shape=(portfolio_state_shape,))
            portfolio_x = Dense(16, activation='relu')(portfolio_input)
            portfolio_x = BatchNormalization()(portfolio_x)

            # Combine inputs
            combined = Concatenate()([lstm, portfolio_x])

            # Value stream - estimates state value
            value_stream = Dense(32, activation='relu')(combined)
            value_stream = Dense(1)(value_stream)

            # Advantage stream - estimates advantage of each action
            advantage_stream = Dense(32, activation='relu')(combined)
            advantage_stream = Dense(self.action_size)(advantage_stream)

            # Combine value and advantage streams
            # Q(s,a) = V(s) + (A(s,a) - mean(A(s,a')))
            outputs = value_stream + (advantage_stream - tf.reduce_mean(advantage_stream, axis=1, keepdims=True))

        else:
            raise ValueError(f"Unknown model type: {self.model_type}")

        # Create and compile model
        model = Model(inputs=[market_input, portfolio_input], outputs=outputs)
        model.compile(
            loss='mse',
            optimizer=Adam(learning_rate=self.learning_rate),
            metrics=['mae']
        )

        return model

    def update_target_model(self):
        """
        Update target model to match main model
        """
        self.target_model.set_weights(self.model.get_weights())
        print("Target network updated")

    def remember(self, state, action, reward, next_state, done, info=None):
        """
        Store experience in memory

        Parameters:
        - state: Current state
        - action: Action taken
        - reward: Reward received
        - next_state: Next state
        - done: Whether the episode is done
        - info: Additional information
        """
        self.memory.append((state, action, reward, next_state, done, info))

        # Track rewards for monitoring
        self.reward_history.append(reward)

    def act(self, state, eval_mode=False):
        """
        Choose action based on epsilon-greedy policy

        Parameters:
        - state: Current state
        - eval_mode: Whether to use exploration (False) or exploitation only (True)

        Returns:
        - action: Selected action
        """
        # Exploration
        if not eval_mode and np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)

        # Exploitation - use model to predict best action
        # Process market and portfolio features
        market_features = np.expand_dims(state['market_features'], axis=0)
        portfolio_state = np.expand_dims(state['portfolio_state'], axis=0)

        # Get Q-values
        q_values = self.model.predict([market_features, portfolio_state], verbose=0)[0]

        # Track Q-values for monitoring
        self.q_value_history.append(np.max(q_values))

        # Return action with highest Q-value
        return np.argmax(q_values)

    def replay(self, batch_size=None):
        """
        Train model using experience replay

        Parameters:
        - batch_size: Size of batch to sample from memory (defaults to self.batch_size)

        Returns:
        - loss: Training loss
        """
        if batch_size is None:
            batch_size = self.batch_size

        if len(self.memory) < batch_size:
            return 0

        # Sample batch from memory
        minibatch = random.sample(self.memory, batch_size)

        # Prepare batch data
        states_market = []
        states_portfolio = []
        targets = []

        for state, action, reward, next_state, done, _ in minibatch:
            # Initialize target with current Q-values
            market_features = np.expand_dims(state['market_features'], axis=0)
            portfolio_state = np.expand_dims(state['portfolio_state'], axis=0)
            target = self.model.predict([market_features, portfolio_state], verbose=0)[0]

            if done:
                # If done, target is just the reward
                target[action] = reward
            else:
                # Process next state
                next_market_features = np.expand_dims(next_state['market_features'], axis=0)
                next_portfolio_state = np.expand_dims(next_state['portfolio_state'], axis=0)

                # Double DQN: Use main model to select action, target model to evaluate it
                next_action = np.argmax(
                    self.model.predict([next_market_features, next_portfolio_state], verbose=0)[0]
                )

                # Calculate target Q-value using target network
                next_q = self.target_model.predict([next_market_features, next_portfolio_state], verbose=0)[0][next_action]

                # Update target for the chosen action
                target[action] = reward + self.gamma * next_q

            # Add to batch
            states_market.append(state['market_features'])
            states_portfolio.append(state['portfolio_state'])
            targets.append(target)

        # Convert to numpy arrays
        states_market = np.array(states_market)
        states_portfolio = np.array(states_portfolio)
        targets = np.array(targets)

        # Train the model
        history = self.model.fit(
            [states_market, states_portfolio],
            targets,
            epochs=1,
            batch_size=batch_size,
            verbose=0
        )

        # Track loss
        loss = history.history['loss'][0]
        self.loss_history.append(loss)

        # Update target network periodically
        self.train_step_counter += 1
        if self.train_step_counter % self.update_target_freq == 0:
            self.update_target_model()

        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        return loss

    def load(self, filepath):
        """
        Load model weights from file

        Parameters:
        - filepath: Path to model weights file
        """
        try:
            self.model.load_weights(filepath)
            self.target_model.load_weights(filepath)
            print(f"Model loaded from {filepath}")
        except Exception as e:
            print(f"Error loading model: {e}")

    def save(self, filepath):
        """
        Save model weights to file

        Parameters:
        - filepath: Path to save model weights
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            self.model.save_weights(filepath)
            print(f"Model saved to {filepath}")
        except Exception as e:
            print(f"Error saving model: {e}")

    def get_metrics(self):
        """
        Get training metrics

        Returns:
        - metrics: Dictionary of training metrics
        """
        return {
            'loss_history': self.loss_history,
            'reward_history': self.reward_history,
            'q_value_history': self.q_value_history,
            'epsilon': self.epsilon
        }

def train_dqn_agent(data, feature_columns=None, episodes=200, batch_size=64,
                model_type='advanced', initial_balance=10000, window_size=30,
                payout_ratio=0.8, risk_per_trade=0.01, eval_interval=10,
                model_dir='models', plot_results=True):
    """
    Train a DQN agent for binary options trading

    Parameters:
    - data: DataFrame containing market data
    - feature_columns: List of feature columns to use (if None, default features will be used)
    - episodes: Number of episodes to train
    - batch_size: Batch size for training
    - model_type: Type of model architecture ('simple', 'cnn', 'advanced', 'dueling')
    - initial_balance: Initial account balance
    - window_size: Size of the observation window
    - payout_ratio: Payout ratio for correct predictions
    - risk_per_trade: Percentage of balance to risk per trade
    - eval_interval: Interval for evaluation during training
    - model_dir: Directory to save model files
    - plot_results: Whether to plot training results

    Returns:
    - agent: Trained DQN agent
    - env: Trading environment
    - results: Dictionary of training results
    """
    # Create model directory if it doesn't exist
    os.makedirs(model_dir, exist_ok=True)

    # Create environment
    env = TradingEnvironment(
        data=data,
        initial_balance=initial_balance,
        window_size=window_size,
        payout_ratio=payout_ratio,
        risk_per_trade=risk_per_trade,
        feature_columns=feature_columns
    )

    # Determine state shape
    state = env.reset()
    state_shape = {
        'market_features': state['market_features'].shape,
        'portfolio_state': state['portfolio_state'].shape[0]
    }

    print(f"State shape - Market features: {state_shape['market_features']}, Portfolio state: {state_shape['portfolio_state']}")

    # Create agent
    agent = DQNAgent(
        state_shape=state_shape,
        action_size=2,  # Binary options: UP or DOWN
        memory_size=10000,
        gamma=0.95,
        epsilon_start=1.0,
        epsilon_min=0.01,
        epsilon_decay=0.995,
        learning_rate=0.001,
        batch_size=batch_size,
        update_target_freq=10,
        model_type=model_type
    )

    # Print model summary
    print("\nModel Summary:")
    agent.model.summary()

    # Training metrics
    episode_rewards = []
    episode_balances = []
    episode_win_rates = []
    evaluation_results = []

    # Training loop
    print(f"\nStarting training for {episodes} episodes...")
    for e in range(episodes):
        state = env.reset()
        total_reward = 0
        step_count = 0
        wins = 0
        losses = 0

        while True:
            # Choose and perform action
            action = agent.act(state)
            next_state, reward, done, info = env.step(action)
            total_reward += reward
            step_count += 1

            # Track wins/losses
            if info['trade_result'] == 'win':
                wins += 1
            elif info['trade_result'] == 'loss':
                losses += 1

            # Remember experience
            if next_state is not None:
                agent.remember(state, action, reward, next_state, done, info)

            # Update state
            state = next_state

            # Train model using experience replay
            if len(agent.memory) > batch_size:
                loss = agent.replay(batch_size)

            # If done, print episode stats
            if done or state is None:
                win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

                # Record metrics
                episode_rewards.append(total_reward)
                episode_balances.append(env.balance)
                episode_win_rates.append(win_rate)

                print(f"Episode: {e+1}/{episodes}, Steps: {step_count}, Total Reward: {total_reward:.2f}, "
                      f"Final Balance: {env.balance:.2f}, Win Rate: {win_rate:.2f}, Epsilon: {agent.epsilon:.4f}")

                # Evaluate agent periodically
                if (e + 1) % eval_interval == 0 or e == episodes - 1:
                    eval_result = evaluate_dqn_agent(agent, data, feature_columns, num_episodes=5)
                    evaluation_results.append(eval_result)

                    # Save model if it's the best so far
                    if len(evaluation_results) == 1 or eval_result['mean_balance'] > max(r['mean_balance'] for r in evaluation_results[:-1]):
                        model_path = os.path.join(model_dir, f"dqn_trading_model_{model_type}_best.h5")
                        agent.save(model_path)
                        print(f"New best model saved with mean balance: {eval_result['mean_balance']:.2f}")

                break

    # Save the final trained model
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    model_path = os.path.join(model_dir, f"dqn_trading_model_{model_type}_{timestamp}.h5")
    agent.save(model_path)

    # Compile results
    results = {
        'episode_rewards': episode_rewards,
        'episode_balances': episode_balances,
        'episode_win_rates': episode_win_rates,
        'evaluation_results': evaluation_results,
        'agent_metrics': agent.get_metrics()
    }

    # Plot training results if requested
    if plot_results:
        plot_training_results(results, model_type)

    return agent, env, results

def evaluate_dqn_agent(agent, data, feature_columns=None, num_episodes=5, initial_balance=10000):
    """
    Evaluate a trained DQN agent

    Parameters:
    - agent: Trained DQN agent
    - data: DataFrame containing market data
    - feature_columns: List of feature columns to use
    - num_episodes: Number of episodes to evaluate
    - initial_balance: Initial account balance

    Returns:
    - results: Dictionary of evaluation results
    """
    print(f"\nEvaluating agent for {num_episodes} episodes...")

    # Create evaluation environment
    env = TradingEnvironment(
        data=data,
        initial_balance=initial_balance,
        feature_columns=feature_columns
    )

    # Evaluation metrics
    episode_rewards = []
    episode_balances = []
    episode_win_rates = []
    episode_trades = []

    # Evaluation loop
    for e in range(num_episodes):
        state = env.reset()
        total_reward = 0
        step_count = 0
        wins = 0
        losses = 0

        while True:
            # Choose action (no exploration)
            action = agent.act(state, eval_mode=True)
            next_state, reward, done, info = env.step(action)
            total_reward += reward
            step_count += 1

            # Track wins/losses
            if info['trade_result'] == 'win':
                wins += 1
            elif info['trade_result'] == 'loss':
                losses += 1

            # Update state
            state = next_state

            # If done, record episode stats
            if done or state is None:
                win_rate = wins / (wins + losses) if (wins + losses) > 0 else 0

                # Record metrics
                episode_rewards.append(total_reward)
                episode_balances.append(env.balance)
                episode_win_rates.append(win_rate)
                episode_trades.append(env.trades)

                print(f"Eval Episode {e+1}/{num_episodes}, Steps: {step_count}, "
                      f"Total Reward: {total_reward:.2f}, Final Balance: {env.balance:.2f}, "
                      f"Win Rate: {win_rate:.2f}, Trades: {len(env.trades)}")
                break

    # Calculate aggregate metrics
    mean_reward = np.mean(episode_rewards)
    mean_balance = np.mean(episode_balances)
    mean_win_rate = np.mean(episode_win_rates)

    print(f"\nEvaluation Results:")
    print(f"Mean Reward: {mean_reward:.2f}")
    print(f"Mean Final Balance: {mean_balance:.2f}")
    print(f"Mean Win Rate: {mean_win_rate:.2f}")

    # Compile results
    results = {
        'episode_rewards': episode_rewards,
        'episode_balances': episode_balances,
        'episode_win_rates': episode_win_rates,
        'episode_trades': episode_trades,
        'mean_reward': mean_reward,
        'mean_balance': mean_balance,
        'mean_win_rate': mean_win_rate
    }

    return results

def plot_training_results(results, model_type):
    """
    Plot training results

    Parameters:
    - results: Dictionary of training results
    - model_type: Type of model architecture
    """
    # Create figure with subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))

    # Plot episode rewards
    axs[0, 0].plot(results['episode_rewards'])
    axs[0, 0].set_title('Episode Rewards')
    axs[0, 0].set_xlabel('Episode')
    axs[0, 0].set_ylabel('Total Reward')
    axs[0, 0].grid(True)

    # Plot episode balances
    axs[0, 1].plot(results['episode_balances'])
    axs[0, 1].set_title('Final Balance')
    axs[0, 1].set_xlabel('Episode')
    axs[0, 1].set_ylabel('Balance')
    axs[0, 1].grid(True)

    # Plot win rates
    axs[1, 0].plot(results['episode_win_rates'])
    axs[1, 0].set_title('Win Rate')
    axs[1, 0].set_xlabel('Episode')
    axs[1, 0].set_ylabel('Win Rate')
    axs[1, 0].set_ylim([0, 1])
    axs[1, 0].grid(True)

    # Plot agent metrics
    if 'loss_history' in results['agent_metrics']:
        # Downsample loss history if too large
        loss_history = results['agent_metrics']['loss_history']
        if len(loss_history) > 1000:
            loss_history = loss_history[::len(loss_history)//1000]

        axs[1, 1].plot(loss_history)
        axs[1, 1].set_title('Training Loss')
        axs[1, 1].set_xlabel('Training Step')
        axs[1, 1].set_ylabel('Loss')
        axs[1, 1].set_yscale('log')
        axs[1, 1].grid(True)

    # Add overall title
    plt.suptitle(f'DQN Training Results - {model_type.upper()} Model', fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.96])

    # Save figure
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    plt.savefig(f"dqn_training_results_{model_type}_{timestamp}.png")
    plt.show()

def predict_with_dqn(agent, data, feature_columns=None, window_size=30):
    """
    Make predictions using a trained DQN agent

    Parameters:
    - agent: Trained DQN agent
    - data: DataFrame containing market data
    - feature_columns: List of feature columns to use
    - window_size: Size of the observation window

    Returns:
    - prediction: Dictionary containing prediction results
    """
    # Create environment for prediction
    env = TradingEnvironment(
        data=data,
        window_size=window_size,
        feature_columns=feature_columns
    )

    # Get state
    state = env.reset()

    # Make prediction
    action = agent.act(state, eval_mode=True)

    # Get Q-values
    market_features = np.expand_dims(state['market_features'], axis=0)
    portfolio_state = np.expand_dims(state['portfolio_state'], axis=0)
    q_values = agent.model.predict([market_features, portfolio_state], verbose=0)[0]

    # Calculate confidence
    confidence = abs(q_values[0] - q_values[1]) / (abs(q_values[0]) + abs(q_values[1]) + 1e-6)

    return {
        'action': action,
        'direction': 'UP' if action == 1 else 'DOWN',
        'confidence': float(confidence),
        'q_values': q_values.tolist()
    }