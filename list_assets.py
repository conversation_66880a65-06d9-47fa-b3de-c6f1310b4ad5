import asyncio
from quotexapi.stable_api import Quotex

# Load credentials from config.ini
client = Quotex(
    lang="en",  # Use English language
)

async def list_all_assets():
    """List all available assets"""
    print("Connecting to Quotex API...")
    check_connect, message = await client.connect()
    
    if check_connect:
        print("Connected successfully. Retrieving assets...")
        
        # Get all assets
        all_data = client.get_payment()
        
        # Print assets with BRL in the name
        print("\nAssets containing 'BRL':")
        for asset_name in all_data:
            if "BRL" in asset_name.upper():
                asset_data = all_data[asset_name]
                profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
                status = " ==> Opened" if asset_data["open"] else " ==> Closed"
                print(asset_name, status, profit)
        
        # Print all USD assets
        print("\nAll USD assets:")
        for asset_name in all_data:
            if asset_name.startswith("USD/"):
                asset_data = all_data[asset_name]
                profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
                status = " ==> Opened" if asset_data["open"] else " ==> Closed"
                print(asset_name, status, profit)
        
        # Print all assets
        print("\nAll available assets:")
        for asset_name in all_data:
            asset_data = all_data[asset_name]
            profit = f'Profit 1+ : {asset_data["profit"]["1M"]} | Profit 5+ : {asset_data["profit"]["5M"]}'
            status = " ==> Opened" if asset_data["open"] else " ==> Closed"
            print(asset_name, status, profit)
    else:
        print(f"Connection failed: {message}")
    
    print("Exiting...")
    await client.close()

async def main():
    await list_all_assets()

if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("\nInterrupted by user.")
    finally:
        loop.close()
