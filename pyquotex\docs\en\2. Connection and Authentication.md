# Connection and Authentication Documentation - PyQuotex

## Client Initialization

To start using PyQuotex, you first need to initialize the client with your credentials:

```python
from quotexapi.stable_api import Quotex

client = Quotex(
    email="<EMAIL>",
    password="your_password",
    lang="en",  # Default language (es=Spanish, en=English, pt=Portuguese)
)

# Optional: Enable debugging logs
client.debug_ws_enable = True
```

### Optional Initialization Parameters

- `user_agent`: Customize the User-Agent (uses a predefined one by default)
- `root_path`: Root directory for storing files
- `user_data_dir`: Directory for user data
- `asset_default`: Default currency pair (e.g., "EURUSD")
- `period_default`: Default period in seconds (e.g., 60)

## Connection Process

The connection is performed asynchronously. Here are two ways to establish the connection:

### 1. Simple Connection

```python
import asyncio

async def connect():
    check_connect, message = await client.connect()
    if check_connect:
        print("Connection successful!")
        # Your code here
    else:
        print(f"Connection error: {message}")

# Execute
asyncio.run(connect())
```

### 2. Connection with Retries

```python
async def connect_with_retries(attempts=5):
    check_connect, message = await client.connect()
    
    if not check_connect:
        attempt = 0
        while attempt <= attempts:
            if not await client.check_connect():
                check_connect, message = await client.connect()
                if check_connect:
                    print("Reconnection successful!")
                    break
                else:
                    attempt += 1
                    print(f"Retrying connection {attempt} of {attempts}")
            await asyncio.sleep(5)
    
    return check_connect, message
```

## Session Management

PyQuotex automatically handles sessions and saves data in a `session.json` file. You can manually configure session data:

```python
client.set_session(
    user_agent="Mozilla/5.0...", 
    cookies="your_cookies",  # Optional
    ssid="your_ssid"        # Optional
)
```

### Set account type when starting

```python
# Set demo account
cliente.set_account_mode("PRACTICE")

# Set real account
cliente.set_account_mode("REAL")
```

### Account Mode Change

```python
# Switch to practice account
cliente.change_account("PRACTICE")

# Switch to real account
cliente.change_account("REAL")
```

## Automatic Reconnection

The system implements automatic reconnection when it detects disconnections. However, you can also handle it manually:

```python
async def maintain_connection():
    while True:
        try:
            if not await client.check_connect():
                print("Disconnection detected, attempting to reconnect...")
                check_connect, message = await client.connect()
                if check_connect:
                    print("Reconnection successful")
                else:
                    print(f"Reconnection error: {message}")
                    await asyncio.sleep(5)
            await asyncio.sleep(1)
        except Exception as e:
            print(f"Error: {e}")
            await asyncio.sleep(5)
```

### Closing Connection

It's important to close the connection when you're done:

```python
await cliente.close()
```

## Complete Example

```python
import asyncio
from quotexapi.stable_api import Quotex

async def complete_example():
    # Initialization
    client = Quotex(
        email="<EMAIL>",
        password="your_password",
        lang="en"
    )
    
    try:
        # Connection with retries
        check_connect, message = await connect_with_retries()
        
        if check_connect:
            # Check balance
            balance = await client.get_balance()
            print(f"Current balance: {balance}")
            
            # Perform operations...
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await cliente.close()

# Execute
asyncio.run(complete_example())
```

## Important Notes

1. The library uses WebSocket to maintain a real-time connection.
2. Sessions are stored locally to avoid unnecessary logins.
3. All methods that interact with the API are asynchronous and must be handled with `async/await`.
4. It's recommended to implement proper error handling for reconnections.
5. Two-factor authentication (2FA) is handled automatically if configured in the account.