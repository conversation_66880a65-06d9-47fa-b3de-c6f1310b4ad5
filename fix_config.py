#!/usr/bin/env python
"""
Fix config.ini file format
This script fixes the config.ini file format to ensure it's compatible with ConfigParser
"""

import os
import sys
import configparser
import shutil

def fix_config_file():
    """Fix config.ini file format"""
    print("Checking config.ini file format...")

    # Check if config.ini exists
    if not os.path.exists('config.ini'):
        print("No config.ini file found.")
        return

    # Try to read config file with ConfigParser (no interpolation to handle special characters)
    config = configparser.ConfigParser(interpolation=None)
    try:
        config.read('config.ini')

        # Check if credentials section exists
        if 'credentials' in config:
            email = config['credentials'].get('email', '')
            password = config['credentials'].get('password', '')

            if email and password:
                print(f"Config file is already in correct format with credentials for: {email}")
                return
            else:
                print("Config file is in correct format but missing credentials.")
                return
        else:
            print("Config file is in correct format but missing credentials section.")
            return
    except configparser.MissingSectionHeaderError:
        # Handle old format config.ini without section headers
        print("Config file has incorrect format. Fixing...")

        # Backup the old file
        backup_file = 'config.ini.bak'
        try:
            shutil.copy2('config.ini', backup_file)
            print(f"Backed up old config file to {backup_file}")
        except Exception as backup_error:
            print(f"Warning: Could not backup old config file: {backup_error}")

        # Read the old file
        email = ""
        password = ""
        try:
            with open('config.ini', 'r') as f:
                for line in f:
                    if line.startswith('email='):
                        email = line.strip().split('=', 1)[1]
                    elif line.startswith('password='):
                        password = line.strip().split('=', 1)[1]

            if email and password:
                print(f"Found credentials in old format for: {email}")

                # Create new config file (no interpolation to handle special characters)
                config = configparser.ConfigParser(interpolation=None)
                config['credentials'] = {
                    'email': email,
                    'password': password
                }

                # Write new config file
                with open('config.ini', 'w') as f:
                    config.write(f)

                print("Config file fixed and saved.")
                return
            else:
                print("Could not find valid credentials in old format config file.")
                return
        except Exception as e:
            print(f"Error reading old format config file: {e}")
            return
    except Exception as e:
        print(f"Error reading config file: {e}")
        return

if __name__ == "__main__":
    fix_config_file()
