#!/usr/bin/env python
"""
Quotex Chart Tester
A simple application to test fetching and displaying real-time chart data from Quotex API
"""

import os
import sys
import time
import asyncio
import configparser
import numpy as np
from datetime import datetime
from PyQt5 import QtCore, QtGui, QtWidgets
import pyqtgraph as pg

# Set up asyncio event loop for Windows
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

class CallbackHelper(QtCore.QObject):
    """Helper class to execute callbacks on the main thread"""
    callback_signal = QtCore.pyqtSignal(object)
    error_signal = QtCore.pyqtSignal(object)

    def __init__(self, callback=None, error_callback=None):
        super().__init__()
        if callback:
            self.callback_signal.connect(callback)
        if error_callback:
            self.error_signal.connect(error_callback)

class AsyncHelper(QtCore.QObject):
    """Helper class to run async coroutines from Qt"""

    def __init__(self):
        super().__init__()
        self.loop = asyncio.new_event_loop()
        self.thread = QtCore.QThread()
        self.moveToThread(self.thread)
        self.thread.started.connect(self._start_loop)
        self.thread.start()

    def _start_loop(self):
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def run_coroutine(self, coroutine, callback=None, error_callback=None):
        """Run a coroutine in the event loop"""
        # Create a helper object that will live on the main thread
        helper = CallbackHelper(callback, error_callback)

        future = asyncio.run_coroutine_threadsafe(coroutine, self.loop)

        if callback or error_callback:
            future.add_done_callback(
                lambda f: self._handle_future_result(f, helper)
            )

        return future

    def _handle_future_result(self, future, helper):
        """Handle the result of a future"""
        try:
            result = future.result()
            # Emit signal to call the callback on the main thread
            helper.callback_signal.emit(result)
        except Exception as e:
            # Emit signal to call the error callback on the main thread
            helper.error_signal.emit(e)
            print(f"Unhandled exception in coroutine: {e}")

class CandlestickItem(pg.GraphicsObject):
    """Custom graphics item for displaying a candlestick chart"""

    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data  # data must have fields: time, open, high, low, close, color
        self.picture = QtGui.QPicture()
        self.generatePicture()

    def generatePicture(self):
        """Optimized pre-render the candlestick chart as a QPicture for performance"""
        # Clear previous picture
        self.picture = QtGui.QPicture()
        painter = QtGui.QPainter(self.picture)

        # Disable antialiasing for better performance on large datasets
        if len(self.data) > 100:
            painter.setRenderHint(QtGui.QPainter.Antialiasing, False)
        else:
            painter.setRenderHint(QtGui.QPainter.Antialiasing, True)

        # Width of candlestick body (adjust for better appearance)
        w = 0.35  # slightly narrower for cleaner look

        # Pre-calculate colors and pens for better performance
        color_cache = {}
        pen_cache = {}

        # Use simple index-based positioning for all candles
        for i, candle in enumerate(self.data):
            # Always use index for positioning to ensure candles are visible
            t = i

            # Ensure we have all required values
            if not all(k in candle for k in ['open', 'high', 'low', 'close']):
                continue

            open_val = candle['open']
            high = candle['high']
            low = candle['low']
            close_val = candle['close']

            # Determine candle color with caching
            if 'color' in candle:
                color = candle['color']
            else:
                color = 'green' if close_val > open_val else 'red' if close_val < open_val else 'gray'

            # Set colors based on candle direction - using more professional colors
            if color == 'green':
                # Professional green for bullish candles
                body_color = QtGui.QColor(0, 180, 100)  # Softer green
                wick_color = QtGui.QColor(0, 200, 120)  # Slightly brighter for wicks
            elif color == 'red':
                # Professional red for bearish candles
                body_color = QtGui.QColor(220, 60, 60)  # Softer red
                wick_color = QtGui.QColor(240, 80, 80)  # Slightly brighter for wicks
            else:
                # Gray for neutral candles
                body_color = QtGui.QColor(150, 150, 150)
                wick_color = QtGui.QColor(170, 170, 170)

            # Set pen for wicks
            wick_pen = pg.mkPen(wick_color, width=1)
            painter.setPen(wick_pen)

            # Draw candle wicks first (so they appear behind the body)
            painter.drawLine(QtCore.QPointF(t, low), QtCore.QPointF(t, high))

            # Set pen and brush for body
            body_pen = pg.mkPen(body_color, width=1)
            painter.setPen(body_pen)
            painter.setBrush(pg.mkBrush(body_color))

            # Draw candle body
            if close_val > open_val:
                # Bullish candle (filled)
                painter.drawRect(QtCore.QRectF(t-w, open_val, w*2, close_val-open_val))
            elif close_val < open_val:
                # Bearish candle (filled)
                painter.drawRect(QtCore.QRectF(t-w, close_val, w*2, open_val-close_val))
            else:
                # Doji candle (open == close)
                painter.drawLine(
                    QtCore.QPointF(t-w, open_val),
                    QtCore.QPointF(t+w, close_val)
                )

        painter.end()

    def paint(self, painter, *_):
        """Paint the pre-rendered picture"""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        """Return the bounding rectangle of the picture"""
        return QtCore.QRectF(self.picture.boundingRect())

    def update_data(self, data):
        """Update the candlestick data and redraw"""
        self.data = data
        self.generatePicture()
        self.update()

class TimeAxisItem(pg.AxisItem):
    """Custom axis item for displaying time values"""

    def __init__(self, *args, **kwargs):
        super(TimeAxisItem, self).__init__(*args, **kwargs)
        self.time_values = {}  # Maps x-values to timestamps

    def set_time_values(self, time_values):
        """Set the time values for the axis"""
        self.time_values = time_values
        self.update()

    def tickStrings(self, values, *_):
        """Return the strings that should be placed next to ticks"""
        result = []
        for v in values:
            # Find the closest time value
            if int(v) in self.time_values:
                # Get the timestamp for this position
                timestamp = self.time_values[int(v)]
                # Format the timestamp as HH:MM
                time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M")
                result.append(time_str)
            else:
                result.append("")
        return result

class CandlestickChart(pg.PlotWidget):
    """Widget for displaying candlestick charts with time axis"""

    def __init__(self, parent=None, background='#1E222D', title="Candlestick Chart"):
        # Create custom time axis
        time_axis = TimeAxisItem(orientation='bottom')

        # Initialize with custom axis
        super(CandlestickChart, self).__init__(parent=parent, background=background, axisItems={'bottom': time_axis})

        # Store reference to time axis
        self.time_axis = time_axis

        # Set up the plot
        self.setTitle(title, color='#ffffff', size='12pt')

        # Get the plot item
        plot_item = self.getPlotItem()

        # Apply grid with improved appearance
        plot_item.showGrid(x=False, y=True, alpha=0.1)  # Only show horizontal grid lines

        # Set axis pens
        axis_pen = pg.mkPen('#444455')
        plot_item.getAxis('left').setPen(axis_pen)
        plot_item.getAxis('bottom').setPen(axis_pen)

        # Set tick fonts
        plot_item.getAxis('left').setTickFont(QtGui.QFont('Arial', 8))
        plot_item.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 8))

        # Set labels
        self.setLabel('left', '', color='#777788')
        self.setLabel('bottom', '', color='#777788')

        # Set view box background
        self.getPlotItem().getViewBox().setBackgroundColor('#1E222D')

        # Remove border around the plot
        self.getPlotItem().getViewBox().setBorder(None)

        # Initialize empty candlestick item
        self.candle_item = None

        # Add price line (horizontal line at current price)
        self.price_line = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen(color='#ffffff', width=1, style=QtCore.Qt.DashLine)
        )
        self.addItem(self.price_line)
        self.price_line.setVisible(False)  # Hide until we have data

        # Add price label
        self.price_label = pg.TextItem(text="", color='#ffffff', anchor=(0, 0.5), fill='#2A2F3A')
        self.addItem(self.price_label)
        self.price_label.setVisible(False)  # Hide until we have data

        # Add current time line (vertical line at current time)
        self.time_line = pg.InfiniteLine(
            angle=90,
            movable=False,
            pen=pg.mkPen(color='#ffffff', width=1, style=QtCore.Qt.DotLine)
        )
        self.addItem(self.time_line)
        self.time_line.setVisible(False)  # Hide until we have data

        # Flag for live mode
        self.live_mode_active = False

        # Flag for predicted candles
        self.predicted_candles_enabled = False
        self.predicted_candles = []

        # Initialize future predictions
        self.future_predictions = {}
        self.prediction_items = []

    def enable_predicted_candles(self, enabled=True):
        """Enable or disable predicted candles display"""
        self.predicted_candles_enabled = enabled

    def add_future_predictions(self, predictions):
        """Add future price predictions to the chart

        Args:
            predictions: Dictionary with time horizons as keys and prediction details as values
                Each prediction should have 'direction' and 'confidence' keys
        """
        # Clear previous prediction items
        for item in self.prediction_items:
            self.removeItem(item)
        self.prediction_items = []

        # Store predictions
        self.future_predictions = predictions

        if not predictions:
            return

        # Get the last candle position
        if not hasattr(self, 'candle_item') or not self.candle_item or not self.candle_item.data:
            return

        last_candle_idx = len(self.candle_item.data) - 1
        last_candle = self.candle_item.data[-1]
        last_price = last_candle['close']

        # Add prediction arrows and labels
        for horizon, pred in predictions.items():
            # Skip invalid predictions
            if 'direction' not in pred or 'confidence' not in pred:
                continue

            # Calculate position (horizon candles into the future)
            x_pos = last_candle_idx + horizon

            # Determine arrow direction and color
            if pred['direction'] == 'call':
                # Up arrow for call
                arrow_color = '#00B050'  # Green
                arrow_angle = 90  # Pointing up
                y_offset = 0.01  # Offset from price
            else:
                # Down arrow for put
                arrow_color = '#FF3030'  # Red
                arrow_angle = -90  # Pointing down
                y_offset = -0.01  # Offset from price

            # Calculate confidence-based size
            confidence = pred['confidence']
            arrow_size = 10 + (confidence * 10)  # Size between 10-20 based on confidence

            # Create arrow
            arrow = pg.ArrowItem(
                angle=arrow_angle,
                tipAngle=30,
                baseAngle=20,
                headLen=arrow_size,
                tailLen=0,
                tailWidth=3,
                pen=None,
                brush=pg.mkBrush(arrow_color)
            )

            # Position arrow
            if pred['direction'] == 'call':
                # Position above the price for call
                arrow.setPos(x_pos, last_price * (1 + y_offset))
            else:
                # Position below the price for put
                arrow.setPos(x_pos, last_price * (1 + y_offset))

            # Add to chart
            self.addItem(arrow)
            self.prediction_items.append(arrow)

            # Add confidence label
            label_text = f"{confidence:.2f}"
            label_color = arrow_color

            label = pg.TextItem(
                text=label_text,
                color=label_color,
                anchor=(0.5, 0.5)
            )

            # Position label near the arrow
            if pred['direction'] == 'call':
                label.setPos(x_pos, last_price * (1 + y_offset * 3))
            else:
                label.setPos(x_pos, last_price * (1 + y_offset * 3))

            # Add to chart
            self.addItem(label)
            self.prediction_items.append(label)

    def set_candle_data(self, candles):
        """Optimized set candlestick data and update the chart"""
        if not candles or len(candles) == 0:
            return

        # Reduce debug output for performance
        if len(candles) > 0 and hasattr(self, '_last_candle_count'):
            # Only print debug info if candle count changed significantly
            if abs(len(candles) - self._last_candle_count) > 5:
                print(f"Chart updated with {len(candles)} candles")
                self._last_candle_count = len(candles)
        else:
            self._last_candle_count = len(candles)

        # Validate and process candle data
        validated_candles = []
        for candle in candles:
            # Ensure all required fields are present
            if not all(k in candle for k in ['open', 'high', 'low', 'close']):
                print(f"Warning: Skipping candle missing required OHLC data: {candle}")
                continue

            # Create a copy to avoid modifying the original
            processed_candle = candle.copy()

            # Ensure numeric values for OHLC
            for key in ['open', 'high', 'low', 'close']:
                try:
                    processed_candle[key] = float(processed_candle[key])
                except (ValueError, TypeError):
                    print(f"Warning: Invalid {key} value in candle: {processed_candle[key]}")
                    processed_candle[key] = 0.0

            # Add to validated list
            validated_candles.append(processed_candle)

        if not validated_candles:
            print("No valid candles to display after validation")
            return

        # Use validated candles from now on
        candles = validated_candles

        # Remove existing candle item if it exists
        if self.candle_item is not None:
            self.removeItem(self.candle_item)

        # Create new candle item
        self.candle_item = CandlestickItem(candles)
        self.addItem(self.candle_item)

        # Update time axis with timestamps
        time_values = {}
        for i, candle in enumerate(candles):
            if 'time' in candle:
                time_values[i] = candle['time']
        self.time_axis.set_time_values(time_values)

        # Update price line and label
        if candles:
            last_candle = candles[-1]
            current_price = last_candle['close']
            self.price_line.setPos(current_price)
            self.price_line.setVisible(True)

            self.price_label.setText(f"{current_price:.5f}")
            self.price_label.setPos(len(candles) - 1, current_price)
            self.price_label.setVisible(True)

            # Update time line to show current time
            current_time = datetime.now()
            # Find the closest candle to current time
            closest_candle_idx = len(candles) - 1
            for i, candle in enumerate(candles):
                if 'time' in candle and candle['time'] > current_time.timestamp():
                    closest_candle_idx = max(0, i - 1)
                    break

            self.time_line.setPos(closest_candle_idx)
            self.time_line.setVisible(True)

        # Auto-range to show all data
        self.autoRange()

        # Set X range to show all candles with better spacing
        if len(candles) > 1:
            x_min = -0.5
            x_max = len(candles) - 1

            # Adjust padding based on number of candles
            if len(candles) <= 20:
                x_padding = (x_max - x_min) * 0.2  # More padding for fewer candles
            elif len(candles) <= 50:
                x_padding = (x_max - x_min) * 0.15
            else:
                x_padding = (x_max - x_min) * 0.1  # Less padding for many candles

            self.setXRange(x_min, x_max + x_padding)

        # Set Y range to show prices with some padding
        if candles:
            # Find min and max prices
            min_price = min(candle['low'] for candle in candles)
            max_price = max(candle['high'] for candle in candles)

            # Add padding
            price_range = max_price - min_price

            # Use more padding for smaller price ranges to avoid zooming in too much
            if price_range < 0.001:
                padding = 0.001  # Minimum padding for very small ranges
            else:
                padding = price_range * 0.15  # Slightly more padding (15% instead of 10%)

            self.setYRange(min_price - padding, max_price + padding)

            # Add some visual enhancements

            # Add a horizontal line at the middle price point for reference
            mid_price = (min_price + max_price) / 2
            if not hasattr(self, 'mid_line'):
                self.mid_line = pg.InfiniteLine(
                    angle=0,
                    movable=False,
                    pen=pg.mkPen(color='#555555', width=1, style=QtCore.Qt.DotLine)
                )
                self.addItem(self.mid_line)
            self.mid_line.setPos(mid_price)
            self.mid_line.setVisible(True)

            # Add live mode indicator if needed
            if 'source' in candles[-1] and candles[-1]['source'] == 'live_generated':
                if not hasattr(self, 'live_indicator'):
                    self.live_indicator = pg.TextItem(
                        text="LIVE",
                        color='#4299E1',
                        anchor=(1, 0),
                        fill='#2A2F3A'
                    )
                    self.addItem(self.live_indicator)

                # Position in top-right corner
                self.live_indicator.setPos(len(candles) - 1, max_price + padding * 0.8)
                self.live_indicator.setVisible(True)
            elif hasattr(self, 'live_indicator'):
                self.live_indicator.setVisible(False)

class QuotexChartTester(QtWidgets.QMainWindow):
    """Main application window for testing Quotex charts"""

    # Signal for logging from any thread
    log_signal = QtCore.pyqtSignal(str)

    def __init__(self):
        super(QuotexChartTester, self).__init__()

        # Connect log signal to append method
        self.log_signal.connect(self._append_to_log)

        # Initialize market payouts dictionary
        self.market_payouts = {}

        # Set up the UI
        self.setWindowTitle("Quotex Professional Chart Tester")
        self.setGeometry(100, 100, 1200, 800)

        # Try to set window icon if file exists
        if os.path.exists("icon.png"):
            self.setWindowIcon(QtGui.QIcon("icon.png"))

        # Set professional dark theme
        self.setStyleSheet("""
            QMainWindow, QWidget {
                background-color: #1E222D;
                color: #E0E0E0;
            }
            QPushButton {
                background-color: #2D3748;
                color: #E0E0E0;
                border: 1px solid #4A5568;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3A4A64;
                border: 1px solid #5A6A88;
            }
            QPushButton:pressed {
                background-color: #2A3548;
            }
            QComboBox {
                background-color: #2D3748;
                color: #E0E0E0;
                border: 1px solid #4A5568;
                padding: 5px;
                border-radius: 4px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #4A5568;
            }
            QComboBox QAbstractItemView {
                background-color: #2D3748;
                color: #E0E0E0;
                selection-background-color: #4A5568;
            }
            QLineEdit, QSpinBox {
                background-color: #2D3748;
                color: #E0E0E0;
                border: 1px solid #4A5568;
                padding: 5px;
                border-radius: 4px;
            }
            QCheckBox {
                color: #E0E0E0;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #4A5568;
                background-color: #2D3748;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #4A5568;
                background-color: #4299E1;
            }
            QLabel {
                color: #E0E0E0;
            }
            QTextEdit {
                background-color: #252A37;
                color: #CBD5E0;
                border: 1px solid #4A5568;
                border-radius: 4px;
                font-family: Consolas, Monaco, monospace;
                font-size: 9pt;
            }
            QSplitter::handle {
                background-color: #4A5568;
            }
            QScrollBar:vertical {
                border: none;
                background-color: #2D3748;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #4A5568;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # Create central widget and layout
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QtWidgets.QVBoxLayout(central_widget)

        # Create controls layout
        controls_layout = QtWidgets.QHBoxLayout()

        # Add asset selector
        asset_label = QtWidgets.QLabel("Asset:")
        self.asset_combo = QtWidgets.QComboBox()
        self.asset_combo.currentIndexChanged.connect(self.on_asset_changed)

        # Regular markets
        regular_markets = [
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD",
            "EURJPY", "GBPJPY", "EURGBP", "AUDCAD", "NZDUSD", "CADJPY",
            "GBPCAD", "AUDJPY", "AUDCHF", "EURAUD", "EURCAD", "EURCHF"
        ]

        # OTC markets from the provided list with payout percentages
        # Format: (market, payout percentage)
        otc_markets_with_payout = [
            ("USDINR", 93), ("USDPKR", 93), ("USDTRY", 93), ("EURNZD", 90),
            ("USDDZD", 90), ("GBPNZD", 89), ("USDARS", 89), ("AUDNZD", 88),
            ("NZDUSD", 88), ("USDIDR", 87), ("NZDCHF", 86), ("EURAUD", 85),
            ("USDBDT", 85), ("USDBRL", 84), ("USDCOP", 84), ("GBPCHF", 83),
            ("USDNGN", 83), ("USDPHP", 83), ("EURGBP", 82), ("AUDCHF", 81),
            ("CADCHF", 80), ("CHFJPY", 80), ("EURCAD", 80), ("NZDJPY", 80),
            ("EURCHF", 78), ("NZDCAD", 78), ("USDEGP", 78), ("USDMXN", 78),
            ("USDJPY", 72), ("EURJPY", 70), ("USDZAR", 70), ("EURSGD", 66),
            ("CADJPY", 60), ("EURUSD", 60), ("GBPUSD", 50), ("AUDJPY", 40),
            ("GBPCAD", 40), ("USDCAD", 40), ("AUDCAD", 30), ("AUDUSD", 30),
            ("GBPAUD", 30), ("GBPJPY", 30), ("USDCHF", 30)
        ]

        # Sort OTC markets by payout percentage (highest first)
        otc_markets_with_payout.sort(key=lambda x: x[1], reverse=True)

        # Group OTC markets by payout percentage
        payout_groups = {}
        for market, payout in otc_markets_with_payout:
            if payout not in payout_groups:
                payout_groups[payout] = []
            payout_groups[payout].append(market)

        # Clear the combo box
        self.asset_combo.clear()

        # Create a custom model to make headers non-selectable
        class ComboBoxModel(QtCore.QAbstractListModel):
            def __init__(self, items, parent=None):
                super(ComboBoxModel, self).__init__(parent)
                self.items = items

            def rowCount(self, _=None):
                return len(self.items)

            def data(self, index, role=QtCore.Qt.DisplayRole):
                if not index.isValid() or index.row() >= len(self.items):
                    return None

                if role == QtCore.Qt.DisplayRole:
                    return self.items[index.row()]['text']

                if role == QtCore.Qt.ForegroundRole and self.items[index.row()]['is_header']:
                    return QtGui.QColor('#888888')

                return None

            def flags(self, index):
                if not index.isValid() or index.row() >= len(self.items):
                    return QtCore.Qt.NoItemFlags

                if self.items[index.row()]['is_header'] or self.items[index.row()]['is_separator']:
                    return QtCore.Qt.NoItemFlags

                return QtCore.Qt.ItemIsEnabled | QtCore.Qt.ItemIsSelectable

        # Prepare items for the model
        combo_items = []

        # Add regular markets header
        combo_items.append({'text': "--- Regular Markets ---", 'is_header': True, 'is_separator': False})
        combo_items.append({'text': "", 'is_header': False, 'is_separator': True})

        # Add regular markets
        for market in regular_markets:
            combo_items.append({'text': market, 'is_header': False, 'is_separator': False})

        # Add OTC markets header
        combo_items.append({'text': "--- OTC Markets (Grouped by Payout %) ---", 'is_header': True, 'is_separator': False})
        combo_items.append({'text': "", 'is_header': False, 'is_separator': True})

        # Add each payout group
        for payout in sorted(payout_groups.keys(), reverse=True):
            # Add group header
            combo_items.append({'text': f"--- {payout}% Payout ---", 'is_header': True, 'is_separator': False})
            combo_items.append({'text': "", 'is_header': False, 'is_separator': True})

            # Add markets in this group
            markets_in_group = sorted(payout_groups[payout])
            for market in markets_in_group:
                combo_items.append({'text': market + "_otc", 'is_header': False, 'is_separator': False})

        # Set the model
        model = ComboBoxModel(combo_items)
        self.asset_combo.setModel(model)

        # Select first non-header item
        for i in range(len(combo_items)):
            if not combo_items[i]['is_header'] and not combo_items[i]['is_separator']:
                self.asset_combo.setCurrentIndex(i)
                break

        # Create a dictionary of payout percentages for reference
        self.market_payouts = {market + "_otc": payout for market, payout in otc_markets_with_payout}
        controls_layout.addWidget(asset_label)
        controls_layout.addWidget(self.asset_combo)

        # Add timeframe selector
        timeframe_label = QtWidgets.QLabel("Timeframe:")
        self.timeframe_combo = QtWidgets.QComboBox()
        self.timeframe_combo.addItems(["1 min", "5 min", "15 min", "30 min", "1 hour"])
        self.timeframe_values = [60, 300, 900, 1800, 3600]  # in seconds
        controls_layout.addWidget(timeframe_label)
        controls_layout.addWidget(self.timeframe_combo)

        # Add candle count selector
        count_label = QtWidgets.QLabel("Candles:")
        self.count_spin = QtWidgets.QSpinBox()
        self.count_spin.setRange(10, 500)
        self.count_spin.setValue(100)
        controls_layout.addWidget(count_label)
        controls_layout.addWidget(self.count_spin)

        # Add fetch button
        self.fetch_button = QtWidgets.QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        controls_layout.addWidget(self.fetch_button)

        # Add auto-update checkbox
        self.auto_update_check = QtWidgets.QCheckBox("Auto Update")
        self.auto_update_check.setChecked(True)
        self.auto_update_check.stateChanged.connect(self.toggle_auto_update)
        controls_layout.addWidget(self.auto_update_check)

        # Add live mode checkbox (always checked and disabled)
        self.live_mode_check = QtWidgets.QCheckBox("Live Mode")
        self.live_mode_check.setChecked(True)
        self.live_mode_check.setEnabled(False)  # Disable to prevent toggling
        self.live_mode_check.setStyleSheet("QCheckBox { color: #4299E1; font-weight: bold; }")
        controls_layout.addWidget(self.live_mode_check)

        # Add status label
        self.status_label = QtWidgets.QLabel("Ready")
        controls_layout.addWidget(self.status_label)

        # Add controls to main layout
        main_layout.addLayout(controls_layout)

        # Create splitter for chart and log
        splitter = QtWidgets.QSplitter(QtCore.Qt.Vertical)
        main_layout.addWidget(splitter, 1)

        # Create chart
        self.chart = CandlestickChart(title="Quotex Chart")
        splitter.addWidget(self.chart)

        # Create log text area
        log_widget = QtWidgets.QWidget()
        log_layout = QtWidgets.QVBoxLayout(log_widget)
        log_label = QtWidgets.QLabel("Debug Log:")
        self.log_text = QtWidgets.QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(log_label)
        log_layout.addWidget(self.log_text)
        splitter.addWidget(log_widget)

        # Set splitter sizes
        splitter.setSizes([600, 200])

        # Add credentials button
        self.credentials_button = QtWidgets.QPushButton("Set API Credentials")
        self.credentials_button.clicked.connect(self.set_credentials)
        controls_layout.addWidget(self.credentials_button)

        # Initialize variables
        self.api_client = None
        self.api_connected = False
        self.candles_data = []
        self.async_helper = AsyncHelper()
        self.update_timer = QtCore.QTimer()
        self.update_timer.timeout.connect(self.update_data)

        # Initialize live mode variables
        self.live_mode_active = False
        self.last_live_price = None

        # Connect timer signals
        self.start_timer_signal.connect(self.update_timer.start)
        self.stop_timer_signal.connect(self.update_timer.stop)

        # Create live candle timer
        self.live_timer = QtCore.QTimer()
        self.live_timer.timeout.connect(self.update_live_candle)

        # Try to read credentials from config.ini file
        self.email = ''
        self.password = ''
        self.read_credentials_from_config()

        # Initialize API
        self.initialize_api()

    def read_credentials_from_config(self):
        """Read credentials from config.ini file"""
        try:
            # Try to read directly from the file first (to avoid ConfigParser % issues)
            if os.path.exists('config.ini'):
                with open('config.ini', 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if line.startswith('email='):
                            self.email = line.strip().replace('email=', '')
                        elif line.startswith('password='):
                            self.password = line.strip().replace('password=', '')

                    if self.email and self.password:
                        self.log(f"Loaded credentials from config.ini for: {self.email}")
                        return True

            # Try ConfigParser as a backup
            config = configparser.ConfigParser()

            # Check if settings.ini exists
            if os.path.exists('settings.ini'):
                config.read('settings.ini')
                if 'settings' in config and 'email' in config['settings'] and 'password' in config['settings']:
                    self.email = config['settings']['email']
                    self.password = config['settings']['password']
                    self.log(f"Loaded credentials from settings.ini for: {self.email}")
                    return True

            # If no config file found, try environment variables
            if not self.email or not self.password:
                self.email = os.environ.get('QUOTEX_EMAIL', '')
                self.password = os.environ.get('QUOTEX_PASSWORD', '')
                if self.email and self.password:
                    self.log(f"Loaded credentials from environment variables for: {self.email}")
                    return True

            return False
        except Exception as e:
            self.log(f"Error reading credentials from config: {e}")
            import traceback
            self.log(f"Traceback: {traceback.format_exc()}")
            return False

    def set_credentials(self):
        """Show dialog to set API credentials"""
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Set Quotex API Credentials")
        dialog.setMinimumWidth(400)

        layout = QtWidgets.QVBoxLayout(dialog)

        # Add instructions
        instructions = QtWidgets.QLabel(
            "Enter your Quotex API credentials below. "
            "These will be used to connect to the Quotex API."
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # Add form layout
        form_layout = QtWidgets.QFormLayout()

        # Add email field
        email_label = QtWidgets.QLabel("Email:")
        self.email_input = QtWidgets.QLineEdit()
        self.email_input.setText(self.email)
        form_layout.addRow(email_label, self.email_input)

        # Add password field
        password_label = QtWidgets.QLabel("Password:")
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setEchoMode(QtWidgets.QLineEdit.Password)
        self.password_input.setText(self.password)
        form_layout.addRow(password_label, self.password_input)

        layout.addLayout(form_layout)

        # Add buttons
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            # Save credentials
            self.email = self.email_input.text()
            self.password = self.password_input.text()

            # Update environment variables
            os.environ['QUOTEX_EMAIL'] = self.email
            os.environ['QUOTEX_PASSWORD'] = self.password

            self.log("API credentials updated")

            # Reconnect to API
            self.initialize_api()

    def log(self, message):
        """Add a message to the log in a thread-safe way"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        formatted_message = f"[{timestamp}] {message}"

        # Emit signal to update log from any thread
        self.log_signal.emit(formatted_message)

    def _append_to_log(self, message):
        """Append message to log text (must be called from main thread)"""
        self.log_text.append(message)
        # Scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def initialize_api(self):
        """Initialize the Quotex API client"""
        self.log("Initializing Quotex API client...")

        try:
            # Try to import Quotex API
            try:
                from quotexapi.stable_api import Quotex
                self.log("Successfully imported quotexapi module")
            except ImportError as import_error:
                self.log(f"ERROR: Could not import quotexapi module: {import_error}")
                self.log("Please install it with: pip install quotexapi")
                self.log("You can find it at: https://github.com/Lu-Yi-Hsun/quotexapi")
                self.status_label.setText("Quotex API not available")
                self.fetch_button.setEnabled(False)
                return

            # Use stored credentials
            email = self.email
            password = self.password

            # Check if credentials are set
            if not email or not password:
                self.log("WARNING: No API credentials set. Please use the 'Set API Credentials' button to enter your Quotex login details.")

                # Show credentials dialog automatically
                reply = QtWidgets.QMessageBox.question(
                    self,
                    "No Credentials",
                    "No Quotex API credentials are set. Would you like to enter them now?",
                    QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                    QtWidgets.QMessageBox.Yes
                )

                if reply == QtWidgets.QMessageBox.Yes:
                    self.set_credentials()
                    # If credentials were set, use them
                    email = self.email
                    password = self.password

                    if not email or not password:
                        self.log("Still no credentials set. Cannot connect to API.")
                        self.status_label.setText("No credentials")
                        self.status_label.setStyleSheet("color: red;")
                        self.fetch_button.setEnabled(False)
                        return

            # Define async function to create and connect client
            async def create_client():
                try:
                    self.log(f"Creating Quotex client with email: {email}")

                    # Create the client
                    client = Quotex(email, password)

                    self.log("Attempting to connect to Quotex API...")
                    self.log("If a PIN verification is required, you'll need to enter it in the console.")
                    self.log("Please check your email for the PIN code and enter it in the console window when prompted.")

                    connected = await client.connect()

                    if connected:
                        self.log("Successfully connected to Quotex API")
                        # Test connection by fetching available assets
                        try:
                            self.log("Fetching available assets...")
                            assets = await client.get_all_asset()
                            if assets:
                                self.log(f"Available assets: {assets}")
                                # Create a helper to update the asset combo box
                                class AssetUpdater(QtCore.QObject):
                                    update_signal = QtCore.pyqtSignal(list)

                                    def __init__(self, combo_box):
                                        super().__init__()
                                        self.combo_box = combo_box
                                        self.update_signal.connect(self.update_combo)

                                    def update_combo(self, items):
                                        self.combo_box.clear()
                                        self.combo_box.addItems(items)

                                # Create updater and emit signal
                                updater = AssetUpdater(self.asset_combo)
                                updater.update_signal.emit(assets)
                            else:
                                self.log("No assets returned from API")
                        except Exception as asset_error:
                            self.log(f"Error fetching assets: {asset_error}")
                            import traceback
                            self.log(f"Traceback: {traceback.format_exc()}")
                    else:
                        self.log("Failed to connect to Quotex API - check your credentials")
                        self.log("Make sure you're using the correct email and password for your Quotex account")
                        self.log("If you're seeing PIN verification messages in the console, enter the PIN in the console window.")

                    return client if connected else None
                except Exception as e:
                    self.log(f"Error creating client: {e}")
                    import traceback
                    self.log(f"Traceback: {traceback.format_exc()}")
                    return None

            # Define callbacks
            def on_client_created(client):
                self.api_client = client
                self.api_connected = (client is not None)

                if self.api_connected:
                    self.status_label.setText("Connected to Quotex API")
                    self.status_label.setStyleSheet("color: green;")
                    self.fetch_button.setEnabled(True)

                    # Test the connection with a simple fetch
                    self.log("Testing connection with a simple fetch...")
                    self.fetch_data()

                    if self.auto_update_check.isChecked():
                        self.start_auto_update()
                else:
                    self.status_label.setText("Connection failed")
                    self.status_label.setStyleSheet("color: red;")
                    self.fetch_button.setEnabled(False)
                    self.log("Could not connect to Quotex API. Please check your credentials and internet connection.")

            def on_error(error):
                self.log(f"Error connecting to API: {error}")
                import traceback
                self.log(f"Traceback: {traceback.format_exc()}")
                self.api_client = None
                self.api_connected = False
                self.status_label.setText("Connection failed")
                self.status_label.setStyleSheet("color: red;")
                self.fetch_button.setEnabled(False)
                self.log("Could not connect to Quotex API. Please check your credentials and internet connection.")

            # Run the async function
            self.log("Starting async connection process...")
            self.async_helper.run_coroutine(create_client(), on_client_created, on_error)

        except Exception as e:
            self.log(f"Error initializing API: {e}")
            import traceback
            self.log(f"Traceback: {traceback.format_exc()}")
            self.status_label.setText("Initialization failed")
            self.status_label.setStyleSheet("color: red;")
            self.fetch_button.setEnabled(False)

    def fetch_data(self):
        """Fetch candle data from Quotex API"""
        # Stop live mode if active (will restart after fetching)
        if self.live_mode_active:
            self.stop_live_mode()

        if not self.api_client or not self.api_connected:
            self.log("ERROR: API client not available or not connected")
            self.log("Please check that:")
            self.log("1. You have installed the quotexapi package (pip install quotexapi)")
            self.log("2. You have set the correct credentials (QUOTEX_EMAIL and QUOTEX_PASSWORD environment variables)")
            self.log("3. Your internet connection is working")
            self.log("4. The Quotex API server is available")

            # Show a more detailed error message
            self.status_label.setText("API connection error")
            self.status_label.setStyleSheet("color: red;")
            return

        # Get selected asset and timeframe
        asset = self.asset_combo.currentText()
        timeframe_index = self.timeframe_combo.currentIndex()
        period = self.timeframe_values[timeframe_index]
        count = self.count_spin.value()

        self.log(f"Fetching {count} candles for {asset} with period {period}s...")
        self.status_label.setText(f"Fetching data for {asset}...")
        self.status_label.setStyleSheet("color: orange;")
        self.fetch_button.setEnabled(False)

        # Define async function to fetch candles
        async def fetch_candles():
            try:
                # Get current time
                end_from_time = time.time()

                # Log the exact parameters we're using
                self.log(f"API call parameters:")
                self.log(f"  asset: {asset}")
                self.log(f"  end_from_time: {end_from_time} ({datetime.fromtimestamp(end_from_time).strftime('%Y-%m-%d %H:%M:%S')})")
                self.log(f"  count: {count}")
                self.log(f"  period: {period}")

                # Fetch candles
                self.log("Calling get_candles API method...")
                candles = await self.api_client.get_candles(asset, end_from_time, count, period)

                if candles:
                    self.log(f"Successfully received {len(candles)} candles from API")
                else:
                    self.log("API returned empty candles list")

                return candles
            except Exception as e:
                self.log(f"Error fetching candles: {e}")
                import traceback
                self.log(f"Traceback: {traceback.format_exc()}")
                return None

        # Define callbacks
        def on_candles_fetched(candles):
            self.fetch_button.setEnabled(True)

            if not candles:
                self.log("No candles returned from API")
                self.status_label.setText("No data received")
                return

            self.log(f"Received {len(candles)} candles from API")

            # Process candles
            processed_candles = []
            for candle in candles:
                # Only print first and last candle for debugging to avoid log spam
                if candle == candles[0] or candle == candles[-1]:
                    self.log(f"Raw candle: {candle}")

                # Check if we have all required fields
                required_fields = ['time', 'open', 'high', 'low', 'close']
                if not all(field in candle for field in required_fields):
                    self.log(f"Warning: Missing required fields in candle: {candle}")
                    continue

                # Verify the price range is correct for EURUSD (should be around 1.12)
                if 'open' in candle:
                    open_price = float(candle['open'])
                    if open_price < 0.5 or open_price > 2.0:
                        self.log(f"WARNING: Suspicious price value: {open_price:.5f}. EURUSD should be around 1.12.")

                # Convert timestamp to datetime for display
                candle_time = datetime.fromtimestamp(candle['time'])
                timestamp = candle_time.strftime("%Y-%m-%d %H:%M:%S")

                # Create processed candle
                processed_candle = {
                    'timestamp': timestamp,
                    'time': int(candle['time']),
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'color': 'green' if float(candle['close']) >= float(candle['open']) else 'red',
                    'source': 'quotex_api',
                    'raw': candle  # Store the raw data for reference
                }

                processed_candles.append(processed_candle)

            # Update candles data
            self.candles_data = processed_candles

            # Update chart
            self.chart.set_candle_data(self.candles_data)

            # Automatically start live mode after fetching data
            self.start_live_mode()

            # Update status
            if self.candles_data:
                last_candle = self.candles_data[-1]
                self.status_label.setText(f"{asset}: {last_candle['close']:.5f}")

                # Display detailed information about the last candle
                self.log(f"Latest candle details:")
                self.log(f"  Time: {last_candle['timestamp']}")
                self.log(f"  OHLC: O={last_candle['open']:.5f}, H={last_candle['high']:.5f}, L={last_candle['low']:.5f}, C={last_candle['close']:.5f}")
                self.log(f"  Color: {last_candle['color']}")

                # Calculate some statistics
                if len(self.candles_data) > 1:
                    # Calculate price change
                    first_candle = self.candles_data[0]
                    price_change = last_candle['close'] - first_candle['open']
                    price_change_pct = (price_change / first_candle['open']) * 100
                    self.log(f"  Price change: {price_change:.5f} ({price_change_pct:.2f}%)")

                    # Calculate average candle size
                    candle_sizes = [abs(c['close'] - c['open']) for c in self.candles_data]
                    avg_candle_size = sum(candle_sizes) / len(candle_sizes)
                    self.log(f"  Average candle size: {avg_candle_size:.5f}")

                    # Count green and red candles
                    green_candles = sum(1 for c in self.candles_data if c['color'] == 'green')
                    red_candles = sum(1 for c in self.candles_data if c['color'] == 'red')
                    self.log(f"  Green candles: {green_candles}, Red candles: {red_candles}")
            else:
                self.status_label.setText(f"No valid data for {asset}")

        def on_error(error):
            self.log(f"Error fetching candles: {error}")
            self.status_label.setText("Error fetching data")
            self.status_label.setStyleSheet("color: red;")
            self.fetch_button.setEnabled(True)

            # Ask if user wants to use simulated data
            reply = QtWidgets.QMessageBox.question(
                self,
                "API Error",
                "Failed to fetch data from Quotex API. Would you like to use simulated data instead?",
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                QtWidgets.QMessageBox.Yes
            )

            if reply == QtWidgets.QMessageBox.Yes:
                self.log("Using simulated data instead of real API data")
                simulated_candles = self.generate_simulated_candles(asset, count, period)
                on_candles_fetched(simulated_candles)
                # Live mode will be started automatically in on_candles_fetched

        # Run the async function
        self.async_helper.run_coroutine(fetch_candles(), on_candles_fetched, on_error)

    def generate_simulated_candles(self, asset, count, period):
        """Generate simulated candles for testing when API is not available"""
        self.log(f"Generating {count} simulated candles for {asset} with period {period}s")

        # Set base price based on asset
        if asset.startswith("EURUSD"):
            base_price = 1.12000
        elif asset.startswith("GBPUSD"):
            base_price = 1.28000
        elif asset.startswith("USDJPY"):
            base_price = 150.000
        elif asset.startswith("USDCHF"):
            base_price = 0.90000
        elif asset.startswith("AUDUSD"):
            base_price = 0.66000
        else:
            base_price = 1.00000

        # Generate candles
        candles = []
        current_time = int(time.time())

        # Start from the oldest candle
        start_time = current_time - (count * period)

        # Set initial price
        current_price = base_price

        for i in range(count):
            # Calculate candle time
            candle_time = start_time + (i * period)

            # Generate price movement
            volatility = 0.0002  # Base volatility
            trend = 0.00005 * (1 if np.random.random() > 0.5 else -1)  # Small trend

            # Calculate price movement
            price_change = np.random.normal(trend, volatility) * current_price
            new_price = current_price + price_change

            # Generate OHLC data
            open_price = current_price
            close_price = new_price

            # Add some randomness to high and low
            price_range = abs(close_price - open_price)
            extra_range = max(price_range * 0.5, volatility * open_price)

            if close_price > open_price:
                high_price = close_price + np.random.uniform(0, extra_range)
                low_price = open_price - np.random.uniform(0, extra_range)
            else:
                high_price = open_price + np.random.uniform(0, extra_range)
                low_price = close_price - np.random.uniform(0, extra_range)

            # Ensure high >= open/close >= low
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)

            # Create candle
            candle = {
                'time': candle_time,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.randint(50, 200),  # Random volume
                'simulated': True  # Mark as simulated
            }

            candles.append(candle)

            # Update current price for next candle
            current_price = close_price

        self.log(f"Generated {len(candles)} simulated candles")
        return candles

    def update_data(self):
        """Update data automatically"""
        if self.api_connected:
            self.fetch_data()

    def toggle_auto_update(self, state):
        """Toggle automatic updates"""
        if state == QtCore.Qt.Checked:
            self.start_auto_update()
        else:
            self.stop_auto_update()

    # Signals for timer control
    start_timer_signal = QtCore.pyqtSignal(int)
    stop_timer_signal = QtCore.pyqtSignal()

    def start_auto_update(self):
        """Start automatic updates"""
        # Get selected timeframe
        timeframe_index = self.timeframe_combo.currentIndex()
        period = self.timeframe_values[timeframe_index]

        # Set update interval to 1/4 of the period (minimum 1 second)
        update_interval = max(1000, period * 250)  # in milliseconds

        self.log(f"Starting auto-update with interval {update_interval}ms")

        # Emit signal to start timer on main thread
        self.start_timer_signal.emit(update_interval)

    def stop_auto_update(self):
        """Stop automatic updates"""
        self.log("Stopping auto-update")

        # Emit signal to stop timer on main thread
        self.stop_timer_signal.emit()

    def toggle_live_mode(self, _):
        """Toggle live candle generation mode (now always active)"""
        # This method is kept for compatibility but no longer used
        # since live mode is always active
        if not self.live_mode_active:
            self.start_live_mode()

    def start_live_mode(self):
        """Start live candle generation"""
        if not self.candles_data:
            self.log("Cannot start live mode without initial data. Please fetch data first.")
            return

        self.log("Starting live candle generation mode")
        self.live_mode_active = True

        # Get the last candle's close price as starting point
        self.last_live_price = self.candles_data[-1]['close']

        # Start the live timer with 1-second updates
        self.live_timer.start(1000)  # Update every second

        # Update UI
        self.status_label.setText(f"● LIVE MODE: {self.asset_combo.currentText()}")
        self.status_label.setStyleSheet("color: #4299E1; font-weight: bold;")

        # Live mode checkbox is already styled in __init__

    def stop_live_mode(self):
        """Stop live candle generation"""
        if self.live_mode_active:
            self.log("Stopping live candle generation mode")
            self.live_mode_active = False
            self.live_timer.stop()

            # Reset UI
            selected_asset = self.asset_combo.currentText()
            if selected_asset in self.market_payouts:
                payout = self.market_payouts[selected_asset]
                self.status_label.setText(f"{selected_asset}: Payout {payout}%")
            else:
                self.status_label.setText(f"{selected_asset}")

            # Checkbox style is maintained since live mode is always enabled

    def update_live_candle(self):
        """Update the live candle in real-time"""
        if not self.candles_data or not self.live_mode_active:
            return

        # Get the last candle
        last_candle = self.candles_data[-1]

        # Generate a new price with realistic movement
        volatility = 0.0001  # Smaller volatility for more realistic 1-second movements
        trend = 0.00001 * (1 if np.random.random() > 0.5 else -1)  # Very small trend

        # Calculate price movement
        price_change = np.random.normal(trend, volatility) * self.last_live_price
        new_price = self.last_live_price + price_change

        # Store the current price for direction comparison
        old_price = self.last_live_price

        # Store the new price for next update
        self.last_live_price = new_price

        # Update the last candle with the new price
        if new_price > last_candle['high']:
            last_candle['high'] = new_price
        if new_price < last_candle['low']:
            last_candle['low'] = new_price

        # Update close price
        last_candle['close'] = new_price

        # Update color
        last_candle['color'] = 'green' if last_candle['close'] > last_candle['open'] else 'red' if last_candle['close'] < last_candle['open'] else 'gray'

        # Update the chart
        self.chart.set_candle_data(self.candles_data)

        # Update status with live price and direction indicator
        price_direction = "▲" if new_price > old_price else "▼" if new_price < old_price else "■"
        direction_color = "#00cc00" if new_price > old_price else "#ff4444" if new_price < old_price else "#cccccc"

        # Create a dynamic status with color
        self.status_label.setText(f"● LIVE: {self.asset_combo.currentText()} - {new_price:.5f} {price_direction}")

        # Set color based on price direction
        self.status_label.setStyleSheet(f"color: {direction_color}; font-weight: bold;")

        # Every 60 seconds, create a new candle
        current_time = time.time()
        last_candle_time = last_candle.get('time', 0)

        if current_time - last_candle_time >= 60:
            self.create_new_candle()

    def create_new_candle(self):
        """Create a new candle for the next time period"""
        if not self.candles_data:
            return

        # Get the last candle
        last_candle = self.candles_data[-1]

        # Create a new candle starting from the last close price
        current_time = time.time()

        # Round time to the nearest minute for better display
        rounded_time = int(current_time - (current_time % 60))

        # Create new candle with the last close price as the open
        new_candle = {
            'time': rounded_time,
            'timestamp': datetime.fromtimestamp(rounded_time).strftime("%Y-%m-%d %H:%M:%S"),
            'open': last_candle['close'],
            'high': last_candle['close'],
            'low': last_candle['close'],
            'close': last_candle['close'],
            'color': 'gray',
            'volume': np.random.randint(50, 200),
            'source': 'live_generated'
        }

        # Add the new candle to the data
        self.candles_data.append(new_candle)

        # If we have too many candles, remove the oldest one
        max_candles = self.count_spin.value()
        if len(self.candles_data) > max_candles:
            self.candles_data.pop(0)

        # Update the chart
        self.chart.set_candle_data(self.candles_data)

        # Log the new candle creation with more details
        self.log(f"Created new candle at {new_candle['timestamp']} - Open: {new_candle['open']:.5f}")

        # Flash the live indicator by changing its color briefly
        if hasattr(self, 'live_indicator'):
            original_color = self.live_indicator.color
            self.live_indicator.setColor('#ffffff')  # Flash white

            # Reset color after a short delay
            QtCore.QTimer.singleShot(300, lambda: self.live_indicator.setColor(original_color))

    def on_asset_changed(self, _):
        """Handle asset selection change"""
        selected_asset = self.asset_combo.currentText()

        # Stop live mode if active (will restart after fetching new data)
        if self.live_mode_active:
            self.stop_live_mode()  # Will be restarted automatically after fetching

        # Skip headers and separators
        if selected_asset.startswith("---"):
            return

        # Update status with payout percentage if available
        if selected_asset in self.market_payouts:
            payout = self.market_payouts[selected_asset]
            self.status_label.setText(f"{selected_asset}: Payout {payout}%")

            # Set color based on payout percentage
            if payout >= 90:
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            elif payout >= 80:
                self.status_label.setStyleSheet("color: lightgreen; font-weight: bold;")
            elif payout >= 70:
                self.status_label.setStyleSheet("color: yellow;")
            elif payout >= 50:
                self.status_label.setStyleSheet("color: orange;")
            else:
                self.status_label.setStyleSheet("color: red;")
        else:
            self.status_label.setText(f"{selected_asset}")
            self.status_label.setStyleSheet("color: white;")

        # If we're connected, fetch data for the new asset
        if self.api_connected and self.auto_update_check.isChecked():
            self.fetch_data()

class FuturePredictionChart(pg.PlotWidget):
    """Widget for displaying future price predictions"""

    def __init__(self, parent=None, background='#1E222D', title="Future Prediction"):
        # Initialize with default axis
        super(FuturePredictionChart, self).__init__(parent=parent, background=background)

        # Set up the plot
        self.setTitle(title, color='#ffffff', size='12pt')

        # Get the plot item
        plot_item = self.getPlotItem()

        # Apply grid with improved appearance
        plot_item.showGrid(x=True, y=True, alpha=0.1)

        # Set axis pens
        axis_pen = pg.mkPen('#444455')
        plot_item.getAxis('left').setPen(axis_pen)
        plot_item.getAxis('bottom').setPen(axis_pen)

        # Set tick fonts
        plot_item.getAxis('left').setTickFont(QtGui.QFont('Arial', 8))
        plot_item.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 8))

        # Set labels
        self.setLabel('left', 'Confidence', color='#777788')
        self.setLabel('bottom', 'Time Horizon (minutes)', color='#777788')

        # Set view box background
        self.getPlotItem().getViewBox().setBackgroundColor('#1E222D')

        # Remove border around the plot
        self.getPlotItem().getViewBox().setBorder(None)

        # Create bar chart for prediction confidence
        self.call_bars = pg.BarGraphItem(x=[], height=[], width=0.4, brush='#00B050')
        self.put_bars = pg.BarGraphItem(x=[], height=[], width=0.4, brush='#FF3030')

        # Add items to plot
        self.addItem(self.call_bars)
        self.addItem(self.put_bars)

        # Add legend
        self.legend = plot_item.addLegend()
        self.legend.addItem(pg.PlotDataItem(pen=pg.mkPen('#00B050')), 'Call')
        self.legend.addItem(pg.PlotDataItem(pen=pg.mkPen('#FF3030')), 'Put')

        # Initialize with empty data
        self.predictions = {}

    def set_prediction_data(self, predictions):
        """Set prediction data and update the chart

        Args:
            predictions: Dictionary with time horizons as keys and prediction details as values
                Each prediction should have 'direction' and 'confidence' keys
        """
        if not predictions:
            return

        self.predictions = predictions

        # Extract data for plotting
        horizons = sorted(predictions.keys())
        call_values = []
        put_values = []

        for horizon in horizons:
            pred = predictions[horizon]
            if pred['direction'] == 'call':
                call_values.append(pred['confidence'])
                put_values.append(0)
            else:
                call_values.append(0)
                put_values.append(pred['confidence'])

        # Update bar positions
        self.call_bars.setOpts(x=horizons, height=call_values, width=0.4)
        self.put_bars.setOpts(x=[h + 0.4 for h in horizons], height=put_values, width=0.4)

        # Set axis range
        self.setXRange(-0.5, max(horizons) + 1)
        self.setYRange(0, 1.05)

        # Add confidence threshold line
        if not hasattr(self, 'threshold_line'):
            self.threshold_line = pg.InfiniteLine(
                angle=0,
                movable=False,
                pen=pg.mkPen(color='#AAAAAA', width=1, style=QtCore.Qt.DashLine)
            )
            self.addItem(self.threshold_line)

        self.threshold_line.setPos(0.65)  # Default confidence threshold

        # Add labels for each bar
        if hasattr(self, 'confidence_labels'):
            for label in self.confidence_labels:
                self.removeItem(label)

        self.confidence_labels = []

        for i, horizon in enumerate(horizons):
            pred = predictions[horizon]
            confidence = pred['confidence']
            direction = pred['direction']

            # Position based on direction
            x_pos = horizon if direction == 'call' else horizon + 0.4

            # Create label
            label = pg.TextItem(
                text=f"{confidence:.2f}",
                color='white',
                anchor=(0.5, 0)
            )

            # Position above the bar
            label.setPos(x_pos, confidence + 0.02)

            # Add to plot and store reference
            self.addItem(label)
            self.confidence_labels.append(label)


class LearningProgressChart(pg.PlotWidget):
    """Widget for displaying learning progress over time"""

    def __init__(self, parent=None, background='#1E222D', title="Learning Progress"):
        # Initialize with default axis
        super(LearningProgressChart, self).__init__(parent=parent, background=background)

        # Set up the plot
        self.setTitle(title, color='#ffffff', size='12pt')

        # Get the plot item
        plot_item = self.getPlotItem()

        # Apply grid with improved appearance
        plot_item.showGrid(x=True, y=True, alpha=0.1)

        # Set axis pens
        axis_pen = pg.mkPen('#444455')
        plot_item.getAxis('left').setPen(axis_pen)
        plot_item.getAxis('bottom').setPen(axis_pen)

        # Set tick fonts
        plot_item.getAxis('left').setTickFont(QtGui.QFont('Arial', 8))
        plot_item.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 8))

        # Set labels
        self.setLabel('left', 'Accuracy / Loss', color='#777788')
        self.setLabel('bottom', 'Training Iterations', color='#777788')

        # Set view box background
        self.getPlotItem().getViewBox().setBackgroundColor('#1E222D')

        # Remove border around the plot
        self.getPlotItem().getViewBox().setBorder(None)

        # Create line plots for accuracy and loss
        self.accuracy_plot = self.plot([], [], pen=pg.mkPen('#4299E1', width=2), name='Accuracy')
        self.loss_plot = self.plot([], [], pen=pg.mkPen('#F56565', width=2), name='Loss')

        # Add legend
        self.legend = plot_item.addLegend()

        # Initialize with empty data
        self.accuracy_data = []
        self.loss_data = []
        self.iterations = []

    def set_learning_data(self, iterations, accuracy_data, loss_data):
        """Set learning data and update the chart"""
        self.iterations = iterations
        self.accuracy_data = accuracy_data
        self.loss_data = loss_data

        # Update plots
        self.accuracy_plot.setData(iterations, accuracy_data)
        self.loss_plot.setData(iterations, loss_data)

        # Set axis range with some padding
        if iterations:
            self.setXRange(min(iterations) - 1, max(iterations) + 1)

            # Set Y range to accommodate both accuracy and loss
            y_min = min(min(accuracy_data) if accuracy_data else 1, min(loss_data) if loss_data else 1)
            y_max = max(max(accuracy_data) if accuracy_data else 0, max(loss_data) if loss_data else 0)

            # Add padding
            y_padding = (y_max - y_min) * 0.1
            self.setYRange(max(0, y_min - y_padding), y_max + y_padding)


class AccuracyChart(pg.PlotWidget):
    """Widget for displaying prediction accuracy by time horizon"""

    def __init__(self, parent=None, background='#1E222D', title="Prediction Accuracy"):
        # Initialize with default axis
        super(AccuracyChart, self).__init__(parent=parent, background=background)

        # Set up the plot
        self.setTitle(title, color='#ffffff', size='12pt')

        # Get the plot item
        plot_item = self.getPlotItem()

        # Apply grid with improved appearance
        plot_item.showGrid(x=True, y=True, alpha=0.1)

        # Set axis pens
        axis_pen = pg.mkPen('#444455')
        plot_item.getAxis('left').setPen(axis_pen)
        plot_item.getAxis('bottom').setPen(axis_pen)

        # Set tick fonts
        plot_item.getAxis('left').setTickFont(QtGui.QFont('Arial', 8))
        plot_item.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 8))

        # Set labels
        self.setLabel('left', 'Accuracy', color='#777788')
        self.setLabel('bottom', 'Time Horizon (minutes)', color='#777788')

        # Set view box background
        self.getPlotItem().getViewBox().setBackgroundColor('#1E222D')

        # Remove border around the plot
        self.getPlotItem().getViewBox().setBorder(None)

        # Create bar chart for accuracy by horizon
        self.accuracy_bars = pg.BarGraphItem(x=[], height=[], width=0.6, brush='#4299E1')
        self.addItem(self.accuracy_bars)

        # Add threshold line at 50% (random chance)
        self.threshold_line = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen(color='#AAAAAA', width=1, style=QtCore.Qt.DashLine)
        )
        self.addItem(self.threshold_line)
        self.threshold_line.setPos(0.5)

        # Add threshold label
        self.threshold_label = pg.TextItem(
            text="Random (50%)",
            color='#AAAAAA',
            anchor=(0, 0.5)
        )
        self.addItem(self.threshold_label)

        # Initialize with empty data
        self.accuracy_data = {}

    def set_accuracy_data(self, accuracy_data):
        """Set accuracy data and update the chart

        Args:
            accuracy_data: Dictionary with time horizons as keys and accuracy values as values,
                          or a list of accuracy values (in which case indices will be used as horizons),
                          or a list of dictionaries with 'horizon' and 'accuracy' keys
        """
        if not accuracy_data:
            return

        self.accuracy_data = accuracy_data

        # Process the data based on its format
        horizons = []
        accuracies = []

        # Handle different input formats
        if isinstance(accuracy_data, dict):
            # Dictionary format: {horizon: accuracy}
            horizons = sorted(accuracy_data.keys())
            accuracies = [accuracy_data[h] for h in horizons]
        elif isinstance(accuracy_data, list):
            if accuracy_data and isinstance(accuracy_data[0], dict):
                # List of dictionaries format
                # Extract horizon and accuracy from each entry
                for entry in accuracy_data:
                    if 'horizon' in entry and 'accuracy' in entry:
                        horizons.append(entry['horizon'])
                        accuracies.append(entry['accuracy'])
                    elif 'timestamp' in entry and 'metrics' in entry and 'accuracy' in entry['metrics']:
                        # Handle the format from real_model_manager.accuracy_history
                        horizons.append(entry.get('horizon', 1))  # Default to 1 if horizon not specified
                        accuracies.append(entry['metrics']['accuracy'])
            else:
                # Simple list format: [accuracy1, accuracy2, ...]
                accuracies = accuracy_data
                horizons = list(range(1, len(accuracies) + 1))

        # Ensure we have valid data
        if not horizons or not accuracies:
            print("Warning: No valid accuracy data to display")
            return

        # Update bar positions
        self.accuracy_bars.setOpts(x=horizons, height=accuracies, width=0.6)

        # Position the threshold label
        if horizons:
            self.threshold_label.setPos(max(horizons) + 0.5, 0.5)

        # Set axis range
        if horizons:
            self.setXRange(-0.5, max(horizons) + 1)

            # Set Y range with padding
            y_max = max(accuracies) if accuracies else 0.5
            self.setYRange(0, max(1.0, y_max * 1.1))

        # Add labels for each bar
        if hasattr(self, 'accuracy_labels'):
            for label in self.accuracy_labels:
                self.removeItem(label)

        self.accuracy_labels = []

        for i, (horizon, accuracy) in enumerate(zip(horizons, accuracies)):
            # Create label
            label = pg.TextItem(
                text=f"{accuracy:.2f}",
                color='white',
                anchor=(0.5, 0)
            )

            # Position above the bar
            label.setPos(horizon, accuracy + 0.02)

            # Add to plot and store reference
            self.addItem(label)
            self.accuracy_labels.append(label)


def main():
    """Main function"""
    app = QtWidgets.QApplication(sys.argv)
    window = QuotexChartTester()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
