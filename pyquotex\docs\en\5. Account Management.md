# Account Management in PyQuotex

This documentation details the account management functionalities in the PyQuotex API.

## Initial Setup

```python
from quotexapi.stable_api import Quotex

# Initialize client
client = Quotex(
    email="<EMAIL>",
    password="yourpassword",
    lang="en"  # Default language: Portuguese(pt)
)

# Connect to API
await client.connect()
```

## Profile Information

To get user profile information:

```python
async def get_profile():
    profile = await client.get_profile()
    
    # Available information
    print(f"User: {profile.nick_name}")
    print(f"Demo Balance: {profile.demo_balance}")
    print(f"Real Balance: {profile.live_balance}")
    print(f"ID: {profile.profile_id}")
    print(f"Avatar: {profile.avatar}")
    print(f"Country: {profile.country_name}")
    print(f"Timezone: {profile.offset}")
```

## Balance Query

To check the current account balance:

```python
async def check_balance():
    balance = await client.get_balance()
    print(f"Current Balance: {balance}")
```

The displayed balance corresponds to the active account (demo or real).

## Demo Balance Reload

To reload the balance in the demo account:

```python
async def reload_demo_balance():
    # Reload 5000 in demo account
    result = await client.edit_practice_balance(5000)
    print(result)
```

## Operation History

To get the operation history:

```python
async def get_history():
    # Gets history of active account
    history = await client.get_history()
    
    for operation in history:
        print(f"ID: {operation.get('ticket')}")
        print(f"Profit: {operation.get('profitAmount')}")
        # Other available data in history
```

You can also check the result of a specific operation:

```python
async def check_operation(operation_id):
    status, details = await client.get_result(operation_id)
    # status can be "win" or "loss"
    print(f"Result: {status}")
    print(f"Details: {details}")
```

## Switching Between Demo/Real Accounts

To switch between demo and real accounts:

```python
# Switch to real account
client.set_account_mode("REAL")

# Switch to demo account
client.set_account_mode("PRACTICE")

# You can also use the alternative method
client.change_account("REAL")  # or "PRACTICE"
```

### Important Notes:

1. Demo account is the default mode when initializing the client
2. Make sure you have an established connection before performing operations
3. Demo balance can be reloaded, but real balance cannot
4. Real account operations involve real money, use them with caution
5. Always verify the active account mode before performing operations

## Error Handling

It's recommended to implement error handling in operations:

```python
async def safe_operation():
    try:
        check_connect, message = await client.connect()
        if check_connect:
            # Perform operations
            pass
        else:
            print(f"Connection error: {message}")
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        await cliente.close()
```