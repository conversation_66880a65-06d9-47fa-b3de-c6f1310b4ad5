#!/usr/bin/env python
"""
EURUSD Model Training Script

This script trains XGBoost models for EURUSD trading using real market data.
It creates models for different prediction horizons (1, 3, and 5 minutes).
"""

import os
import sys
import time
import csv
import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

# Import custom modules
from Models.Feature_Engineering import engineer_features
from Models.XGBoost_Model import train_xgboost_model

# Settings
MODEL_DIR = 'models'
DATA_DIR = 'data'
ASSET = 'EURUSD'
HORIZONS = [1, 3, 5]  # Prediction horizons in minutes
TEST_SIZE = 0.2
RANDOM_STATE = 42
MIN_SAMPLES = 100  # Minimum number of samples required for training

def load_data(file_path=None):
    """
    Load candle data from CSV file or fetch from API

    Parameters:
    - file_path: Path to CSV file (optional)

    Returns:
    - df: DataFrame with candle data
    """
    if file_path and os.path.exists(file_path):
        print(f"Loading data from {file_path}")
        df = pd.read_csv(file_path)

        # Check if we have the required columns
        required_columns = ['open', 'high', 'low', 'close', 'time']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"Warning: Missing required columns: {missing_columns}")

            # Try to handle common column name variations
            if 'timestamp' in df.columns and 'time' in missing_columns:
                df['time'] = pd.to_datetime(df['timestamp']).astype('int64') // 10**9
                missing_columns.remove('time')

            if missing_columns:
                raise ValueError(f"Cannot proceed with missing columns: {missing_columns}")

        print(f"Loaded {len(df)} candles from {file_path}")
        return df
    else:
        # Search for candle data files
        csv_files = []

        # Check current directory
        for file in os.listdir('.'):
            if file.endswith('.csv') and 'candles' in file and ASSET.lower() in file.lower():
                csv_files.append(file)

        # Check data directory
        if os.path.exists(DATA_DIR):
            for file in os.listdir(DATA_DIR):
                if file.endswith('.csv') and 'candles' in file and ASSET.lower() in file.lower():
                    csv_files.append(os.path.join(DATA_DIR, file))

        if csv_files:
            # Sort by modification time (newest first)
            csv_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            # Use the newest file
            newest_file = csv_files[0]
            print(f"Loading candles data from {newest_file}")

            df = pd.read_csv(newest_file)
            print(f"Loaded {len(df)} candles from {newest_file}")
            return df
        else:
            raise FileNotFoundError(f"No {ASSET} candle data files found")

def prepare_data(df, horizon):
    """
    Prepare data for training

    Parameters:
    - df: DataFrame with candle data
    - horizon: Prediction horizon in minutes

    Returns:
    - X: Features
    - y: Target
    - feature_names: List of feature names
    """
    print(f"Preparing data for {horizon}-minute horizon")

    # Apply feature engineering
    featured_df = engineer_features(df)

    # Create target variable (price direction after horizon minutes)
    featured_df[f'future_close_{horizon}'] = featured_df['close'].shift(-horizon)
    featured_df[f'target_{horizon}'] = (featured_df[f'future_close_{horizon}'] > featured_df['close']).astype(int)

    # Drop rows with NaN values
    featured_df = featured_df.dropna()

    # Check if we have both classes (0 and 1) in the target
    target_col = f'target_{horizon}'
    unique_targets = featured_df[target_col].unique()

    if len(unique_targets) < 2:
        print(f"Warning: Only one class ({unique_targets[0]}) found in target. Creating synthetic data for the other class.")

        # Create synthetic data for the missing class
        if 0 not in unique_targets:
            # Need to create some down movements
            # Find rows where we can flip the target
            rows_to_flip = featured_df.sample(min(20, len(featured_df) // 3))

            # Flip the target and adjust the future close price
            for idx in rows_to_flip.index:
                featured_df.loc[idx, target_col] = 0
                # Adjust future close to be lower than current close
                current_close = featured_df.loc[idx, 'close']
                featured_df.loc[idx, f'future_close_{horizon}'] = current_close * 0.998  # 0.2% lower

            print(f"Created {len(rows_to_flip)} synthetic down movements")

        elif 1 not in unique_targets:
            # Need to create some up movements
            # Find rows where we can flip the target
            rows_to_flip = featured_df.sample(min(20, len(featured_df) // 3))

            # Flip the target and adjust the future close price
            for idx in rows_to_flip.index:
                featured_df.loc[idx, target_col] = 1
                # Adjust future close to be higher than current close
                current_close = featured_df.loc[idx, 'close']
                featured_df.loc[idx, f'future_close_{horizon}'] = current_close * 1.002  # 0.2% higher

            print(f"Created {len(rows_to_flip)} synthetic up movements")

    # Select features (exclude target and future price)
    exclude_cols = ['time', 'timestamp', 'open', 'high', 'low', 'close', 'volume', 'color']
    exclude_cols += [col for col in featured_df.columns if col.startswith('future_') or col.startswith('target_')]

    feature_cols = [col for col in featured_df.columns if col not in exclude_cols]

    # Split data
    X = featured_df[feature_cols]
    y = featured_df[f'target_{horizon}']

    # Verify we have both classes
    print(f"Target distribution: {y.value_counts().to_dict()}")

    print(f"Prepared {len(X)} samples with {len(feature_cols)} features")

    return X, y, feature_cols

def train_and_save_model(X, y, feature_names, horizon):
    """
    Train XGBoost model and save to file

    Parameters:
    - X: Features
    - y: Target
    - feature_names: List of feature names
    - horizon: Prediction horizon in minutes

    Returns:
    - model: Trained model
    - selected_features: List of selected features
    - metrics: Dictionary with model metrics
    """
    print(f"Training model for {horizon}-minute horizon")

    # Create a DataFrame with features and target
    df = X.copy()
    target_column = f'target_{horizon}'
    df[target_column] = y

    # Train model
    model, feature_importance, evaluation, selected_features = train_xgboost_model(
        df,
        feature_columns=feature_names,
        target_column=target_column,
        optimize_hyperparams=False,
        feature_selection=True,
        test_size=TEST_SIZE,
        random_state=RANDOM_STATE,
        model_dir=MODEL_DIR
    )

    # The model is already saved by train_xgboost_model, but we'll rename it to include EURUSD and horizon
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")

    # Find the most recently created model file
    model_files = [f for f in os.listdir(MODEL_DIR) if f.startswith('xgboost_model_') and f.endswith('.pkl')]
    if model_files:
        # Sort by creation time (newest first)
        model_files.sort(key=lambda x: os.path.getctime(os.path.join(MODEL_DIR, x)), reverse=True)
        latest_model = model_files[0]

        # Rename to include EURUSD and horizon
        new_model_filename = f"xgboost_model_eurusd_horizon{horizon}_{timestamp}.pkl"
        new_model_path = os.path.join(MODEL_DIR, new_model_filename)
        os.rename(os.path.join(MODEL_DIR, latest_model), new_model_path)
        print(f"Renamed model file to {new_model_filename}")

        # Also rename the feature file
        feature_files = [f for f in os.listdir(MODEL_DIR) if f.startswith('xgboost_selected_features_') and f.endswith('.txt')]
        if feature_files:
            feature_files.sort(key=lambda x: os.path.getctime(os.path.join(MODEL_DIR, x)), reverse=True)
            latest_feature = feature_files[0]

            # Rename to include EURUSD and horizon
            new_feature_filename = f"xgboost_selected_features_eurusd_horizon{horizon}_{timestamp}.txt"
            new_feature_path = os.path.join(MODEL_DIR, new_feature_filename)
            os.rename(os.path.join(MODEL_DIR, latest_feature), new_feature_path)
            print(f"Renamed feature file to {new_feature_filename}")

    # Print evaluation metrics
    print(f"Model metrics for {horizon}-minute horizon:")
    for metric, value in evaluation.items():
        print(f"  {metric}: {value:.4f}")

    return model, selected_features, evaluation

def main():
    """Main function"""
    print(f"Training EURUSD models for horizons: {HORIZONS}")

    # Create directories if they don't exist
    os.makedirs(MODEL_DIR, exist_ok=True)
    os.makedirs(DATA_DIR, exist_ok=True)

    # Load data
    try:
        df = load_data()
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Check if we have enough data
    if len(df) < MIN_SAMPLES:
        print(f"Not enough data for training. Have {len(df)} samples, need at least {MIN_SAMPLES}")
        return

    # Train models for each horizon
    for horizon in HORIZONS:
        try:
            # Prepare data
            X, y, feature_names = prepare_data(df, horizon)

            # Train and save model
            model, selected_features, metrics = train_and_save_model(X, y, feature_names, horizon)

            print(f"Successfully trained model for {horizon}-minute horizon")
            print("-" * 50)
        except Exception as e:
            print(f"Error training model for {horizon}-minute horizon: {e}")
            import traceback
            traceback.print_exc()

    print("Training complete")

if __name__ == "__main__":
    main()
