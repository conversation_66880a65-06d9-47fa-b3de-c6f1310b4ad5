import os
import sys
import tensorflow as tf
import joblib

print("Checking ModelManager initialization...")

# Import model manager
try:
    from Models.model_manager import Model<PERSON>anager, TENSORFLOW_AVAILABLE
    
    print(f"TENSORFLOW_AVAILABLE = {TENSORFLOW_AVAILABLE}")
    
    # Create model manager
    model_manager = ModelManager(model_dir='models')
    
    # Check if models exist
    models_exist = model_manager.check_models_exist()
    print(f"Models exist: {models_exist}")
    
    # Load models
    print("\nLoading models...")
    loaded_models = model_manager.load_models()
    
    # Print loaded models
    print("\nLoaded models:")
    for model_name, model_info in loaded_models.items():
        print(f"- {model_name}")
    
    # Check if ensemble was created
    if hasattr(model_manager, 'ensemble') and model_manager.ensemble is not None:
        print("\nEnsemble model was created")
    else:
        print("\nEnsemble model was NOT created")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("\nModelManager check complete.")
