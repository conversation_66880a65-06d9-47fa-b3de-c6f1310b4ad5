import os
import tensorflow as tf
from datetime import datetime
import numpy as np

print("Creating a compatible DQN model for TensorFlow 2.19.0...")

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

def create_compatible_dqn_model():
    """
    Create a DQN model that's compatible with TensorFlow 2.19.0
    """
    # Market features input
    market_input = tf.keras.layers.Input(shape=(30, 10), name='market_features')
    
    # Portfolio state input
    portfolio_input = tf.keras.layers.Input(shape=(3,), name='portfolio_state')
    
    # Process market features
    x = tf.keras.layers.Conv1D(32, 3, padding='same', activation='relu')(market_input)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.MaxPooling1D(2)(x)
    x = tf.keras.layers.LSTM(64, return_sequences=True)(x)
    x = tf.keras.layers.GlobalAveragePooling1D()(x)
    
    # Process portfolio state
    y = tf.keras.layers.Dense(16, activation='relu')(portfolio_input)
    
    # Combine features
    combined = tf.keras.layers.Concatenate()([x, y])
    
    # Output layers
    dense = tf.keras.layers.Dense(64, activation='relu')(combined)
    output = tf.keras.layers.Dense(2, activation='linear')(dense)  # Q-values for actions
    
    # Create model
    model = tf.keras.Model(inputs=[market_input, portfolio_input], outputs=output)
    
    # Use a string loss function name instead of the function object
    model.compile(optimizer='adam', loss='mean_squared_error')
    
    return model

# Create model directory if it doesn't exist
os.makedirs('models', exist_ok=True)

# Create and save the model
model = create_compatible_dqn_model()
timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
model_path = os.path.join('models', f'dqn_model_{timestamp}.h5')

# Save the model in the newer .keras format
keras_path = model_path.replace('.h5', '.keras')
model.save(keras_path)
print(f"DQN model saved to {keras_path}")

# Also save in h5 format for backward compatibility
model.save(model_path)
print(f"DQN model also saved to {model_path}")

# Create a features file
features = [
    'close', 'returns', 'sma_5', 'sma_20', 'rsi_14', 'macd',
    'stoch_k_14', 'adx_14', 'cci_20', 'atr_14'
]

features_path = os.path.join('models', f'dqn_model_{timestamp}_features.txt')
with open(features_path, 'w') as f:
    for feature in features:
        f.write(f"{feature}\n")

print(f"DQN features saved to {features_path}")
print("Done!")
