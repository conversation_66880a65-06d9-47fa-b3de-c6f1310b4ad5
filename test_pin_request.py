#!/usr/bin/env python
"""
Test script for Quotex PIN request
This script tests the PIN request functionality with the given credentials
"""

import sys
import os
from datetime import datetime

def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""
    
    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")
            
            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')
            
            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")
    
    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')
    
    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password
    
    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")
    
    return email, password

def test_pin_request():
    """Test the PIN request functionality"""
    print("=== Testing Quotex PIN Request ===")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Read credentials
    email, password = read_credentials()
    
    if not email or not password:
        print("No credentials provided. Exiting.")
        return
    
    # Print password details for debugging
    print(f"Password length: {len(password)}")
    print(f"Password contains special chars: {'+' in password or '%' in password or '-' in password}")
    if len(password) > 0:
        print(f"First character: {password[0]}")
        print(f"Last character: {password[-1]}")
    
    # Import the PIN helper
    try:
        import quotex_pin_helper
        print("Successfully imported quotex_pin_helper module")
    except ImportError as e:
        print(f"Error importing quotex_pin_helper module: {e}")
        print("Make sure quotex_pin_helper.py is in the same directory")
        return
    
    # Create PIN helper
    print("Creating QuotexPINHelper...")
    helper = quotex_pin_helper.QuotexPINHelper(email, password)
    
    # Request PIN
    print("Requesting new PIN using QuotexPINHelper...")
    success, message = helper.request_pin()
    
    print(f"PIN request result: {success}")
    print(f"Message: {message}")
    
    if success:
        print("PIN request successful!")
        print("Please check your email for the PIN code.")
        
        # Ask if user wants to submit a PIN
        submit_pin = input("Do you want to submit a PIN? (y/n): ")
        if submit_pin.lower() == 'y':
            pin = input("Enter the PIN from your email: ")
            if pin:
                print(f"Submitting PIN: {pin}")
                success, result = helper.submit_pin(pin)
                print(f"PIN submission result: {success}")
                print(f"Result: {result}")
                
                if success:
                    print("PIN submission successful!")
                    print("You are now logged in to Quotex API.")
                else:
                    print("PIN submission failed.")
                    print("Please check the PIN and try again.")
    else:
        print("PIN request failed.")
        print("Please check your credentials and try again.")

if __name__ == "__main__":
    try:
        test_pin_request()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
