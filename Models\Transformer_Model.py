import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input, LayerNormalization
from tensorflow.keras.layers import Conv1D, MaxPooling1D, Flatten, Concatenate, Add, Multiply
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, TensorBoard
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l1_l2
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import os
import datetime
import matplotlib.pyplot as plt

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

class TransformerBlock(tf.keras.layers.Layer):
    """
    Transformer block with multi-head self-attention and feed-forward network
    """
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1):
        super(TransformerBlock, self).__init__()
        self.att = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )
        self.ffn = tf.keras.Sequential([
            Dense(ff_dim, activation="relu"),
            Dense(embed_dim),
        ])
        self.layernorm1 = LayerNormalization(epsilon=1e-6)
        self.layernorm2 = LayerNormalization(epsilon=1e-6)
        self.dropout1 = Dropout(rate)
        self.dropout2 = Dropout(rate)

    def call(self, inputs, training=False):
        # Multi-head self-attention with residual connection and layer normalization
        attn_output = self.att(inputs, inputs)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)

        # Feed-forward network with residual connection and layer normalization
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)

def create_transformer_model(input_shape, model_type='standard',
                            embed_dim=64, num_heads=4, ff_dim=128,
                            num_transformer_blocks=2, mlp_units=[128, 64],
                            dropout_rate=0.2, mlp_dropout_rate=0.3):
    """
    Create a Transformer-based model for time series prediction

    Parameters:
    - input_shape: Shape of input data (sequence_length, features)
    - model_type: 'standard', 'informer', or 'tft' (Temporal Fusion Transformer inspired)
    - embed_dim: Embedding dimension for transformer
    - num_heads: Number of attention heads
    - ff_dim: Feed-forward network dimension
    - num_transformer_blocks: Number of transformer blocks
    - mlp_units: Units in final MLP layers
    - dropout_rate: Dropout rate for transformer blocks
    - mlp_dropout_rate: Dropout rate for MLP layers

    Returns:
    - Compiled Keras model
    """
    inputs = Input(shape=input_shape)

    # Initial feature extraction with Conv1D
    x = Conv1D(filters=32, kernel_size=3, padding='same', activation='relu')(inputs)
    x = BatchNormalization()(x)
    x = Conv1D(filters=embed_dim, kernel_size=3, padding='same', activation='relu')(x)
    x = BatchNormalization()(x)

    if model_type == 'standard':
        # Standard Transformer architecture
        for _ in range(num_transformer_blocks):
            x = TransformerBlock(embed_dim, num_heads, ff_dim, dropout_rate)(x)

        # Global feature extraction
        x = Flatten()(x)

    elif model_type == 'informer':
        # Informer-inspired architecture with distilling attention
        # (Simplified version of the Informer paper)
        for i in range(num_transformer_blocks):
            x = TransformerBlock(embed_dim, num_heads, ff_dim, dropout_rate)(x)

            # Distilling: reduce sequence length by max pooling after each block except the last
            if i < num_transformer_blocks - 1:
                x = MaxPooling1D(pool_size=2)(x)

        # Global feature extraction
        x = Flatten()(x)

    elif model_type == 'tft':
        # Temporal Fusion Transformer inspired architecture
        # Extract context using transformer blocks
        context_features = TransformerBlock(embed_dim, num_heads, ff_dim, dropout_rate)(inputs)

        # Process with additional transformer blocks
        for _ in range(num_transformer_blocks - 1):
            context_features = TransformerBlock(embed_dim, num_heads, ff_dim, dropout_rate)(context_features)

        # Process current features with CNN
        current_features = Conv1D(filters=embed_dim, kernel_size=1, padding='same')(inputs)
        current_features = BatchNormalization()(current_features)

        # Combine context and current features with attention
        combined_features = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=embed_dim // num_heads
        )(current_features, context_features)

        # Add residual connection
        combined_features = Add()([current_features, combined_features])
        combined_features = LayerNormalization(epsilon=1e-6)(combined_features)

        # Global feature extraction
        x = Flatten()(combined_features)

    # Final MLP layers
    for dim in mlp_units:
        x = Dense(dim, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(mlp_dropout_rate)(x)

    # Output layer - binary classification
    outputs = Dense(1, activation='sigmoid')(x)

    model = Model(inputs=inputs, outputs=outputs)

    # Compile with binary crossentropy for binary classification
    model.compile(
        loss='binary_crossentropy',
        optimizer=Adam(learning_rate=0.001),
        metrics=['accuracy', tf.keras.metrics.AUC(), tf.keras.metrics.Precision(), tf.keras.metrics.Recall()]
    )

    return model

def prepare_transformer_data(df, feature_columns, target_column='target_1min', seq_length=30,
                           test_size=0.2, val_size=0.1, scaler_type='robust', random_state=42):
    """
    Prepare data for Transformer model training

    Parameters:
    - df: DataFrame containing features and target
    - feature_columns: List of feature column names
    - target_column: Target column name
    - seq_length: Length of sequences to create
    - test_size: Proportion of data to use for testing
    - val_size: Proportion of training data to use for validation
    - scaler_type: Type of scaler to use ('standard', 'robust', or 'minmax')
    - random_state: Random seed for reproducibility

    Returns:
    - X_train, X_val, X_test: Training, validation, and test sequences
    - y_train, y_val, y_test: Training, validation, and test targets
    - scaler: Fitted scaler for feature normalization
    - feature_importance: Dictionary of feature importance scores (None for Transformer)
    """
    # Extract features and target
    X = df[feature_columns].values
    y = df[target_column].values

    # Scale features
    if scaler_type == 'standard':
        scaler = StandardScaler()
    elif scaler_type == 'robust':
        scaler = RobustScaler()
    elif scaler_type == 'minmax':
        scaler = MinMaxScaler()
    else:
        raise ValueError(f"Unknown scaler type: {scaler_type}")

    X_scaled = scaler.fit_transform(X)

    # Create sequences
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - seq_length):
        X_sequences.append(X_scaled[i:i+seq_length])
        y_sequences.append(y[i+seq_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    # Split into train, validation, and test sets
    # First split into train+val and test
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X_sequences, y_sequences, test_size=test_size, shuffle=False
    )

    # Then split train+val into train and val
    val_size_adjusted = val_size / (1 - test_size)
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_val, y_train_val, test_size=val_size_adjusted, shuffle=False
    )

    # For Transformer models, feature importance is not directly available
    feature_importance = None

    return X_train, X_val, X_test, y_train, y_val, y_test, scaler, feature_importance

def train_transformer_model(df, feature_columns, target_column='target_1min', seq_length=30,
                          model_type='standard', embed_dim=64, num_heads=4, ff_dim=128,
                          num_transformer_blocks=2, mlp_units=[128, 64],
                          dropout_rate=0.2, mlp_dropout_rate=0.3,
                          scaler_type='robust', batch_size=32, epochs=100,
                          patience=15, learning_rate=0.001, model_dir='models'):
    """
    Train the Transformer model with enhanced options

    Parameters:
    - df: DataFrame containing features and target
    - feature_columns: List of feature column names
    - target_column: Target column name
    - seq_length: Length of sequences to create
    - model_type: Type of transformer architecture ('standard', 'informer', or 'tft')
    - embed_dim: Embedding dimension for transformer
    - num_heads: Number of attention heads
    - ff_dim: Feed-forward network dimension
    - num_transformer_blocks: Number of transformer blocks
    - mlp_units: Units in final MLP layers
    - dropout_rate: Dropout rate for transformer blocks
    - mlp_dropout_rate: Dropout rate for MLP layers
    - scaler_type: Type of scaler to use ('standard', 'robust', or 'minmax')
    - batch_size: Batch size for training
    - epochs: Maximum number of epochs to train
    - patience: Patience for early stopping
    - learning_rate: Initial learning rate
    - model_dir: Directory to save model files

    Returns:
    - Dictionary containing trained model, scaler, feature columns, and performance metrics
    """
    print(f"\nPreparing data for Transformer model training...")
    X_train, X_val, X_test, y_train, y_val, y_test, scaler, _ = prepare_transformer_data(
        df, feature_columns, target_column, seq_length,
        scaler_type=scaler_type, random_state=42
    )

    print(f"Training data shape: {X_train.shape}")
    print(f"Validation data shape: {X_val.shape}")
    print(f"Test data shape: {X_test.shape}")

    # Create model
    print(f"\nCreating {model_type} Transformer model...")
    model = create_transformer_model(
        input_shape=(seq_length, len(feature_columns)),
        model_type=model_type,
        embed_dim=embed_dim,
        num_heads=num_heads,
        ff_dim=ff_dim,
        num_transformer_blocks=num_transformer_blocks,
        mlp_units=mlp_units,
        dropout_rate=dropout_rate,
        mlp_dropout_rate=mlp_dropout_rate
    )

    # Print model summary
    model.summary()

    # Create model directory if it doesn't exist
    os.makedirs(model_dir, exist_ok=True)

    # Create timestamp for model files
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")

    # Define callbacks
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=patience,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=patience // 3,
            min_lr=1e-6,
            verbose=1
        ),
        ModelCheckpoint(
            filepath=os.path.join(model_dir, f"transformer_{model_type}_{timestamp}.h5"),
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),
        TensorBoard(
            log_dir=os.path.join(model_dir, f"logs/transformer_{model_type}_{timestamp}"),
            histogram_freq=1
        )
    ]

    # Train model
    print(f"\nTraining {model_type} Transformer model...")
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1
    )

    # Evaluate model on test set
    print("\nEvaluating model on test set...")
    test_loss, test_accuracy, test_auc, test_precision, test_recall = model.evaluate(X_test, y_test, verbose=0)
    print(f"Test loss: {test_loss:.4f}")
    print(f"Test accuracy: {test_accuracy:.4f}")
    print(f"Test AUC: {test_auc:.4f}")
    print(f"Test precision: {test_precision:.4f}")
    print(f"Test recall: {test_recall:.4f}")

    # Calculate F1 score
    test_f1 = 2 * (test_precision * test_recall) / (test_precision + test_recall + 1e-10)
    print(f"Test F1 score: {test_f1:.4f}")

    # Plot training history
    plt.figure(figsize=(12, 4))

    plt.subplot(1, 2, 1)
    plt.plot(history.history['loss'], label='Train Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    plt.subplot(1, 2, 2)
    plt.plot(history.history['accuracy'], label='Train Accuracy')
    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
    plt.title('Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()

    plt.tight_layout()

    # Save plot
    plot_path = os.path.join(model_dir, f"transformer_{model_type}_history_{timestamp}.png")
    plt.savefig(plot_path)
    print(f"\nTraining history plot saved to {plot_path}")

    # Save model
    model_path = os.path.join(model_dir, f"transformer_{model_type}_final_{timestamp}.h5")
    model.save(model_path)
    print(f"Model saved to {model_path}")

    # Return model and related information
    return {
        'model': model,
        'scaler': scaler,
        'feature_columns': feature_columns,
        'seq_length': seq_length,
        'model_type': model_type,
        'performance': {
            'accuracy': test_accuracy,
            'auc': test_auc,
            'precision': test_precision,
            'recall': test_recall,
            'f1': test_f1
        },
        'timestamp': timestamp
    }

def predict_with_transformer(model_info, data):
    """
    Make predictions using a trained Transformer model

    Parameters:
    - model_info: Dictionary containing model, scaler, feature columns, and sequence length
    - data: DataFrame containing features

    Returns:
    - predictions: Dictionary containing prediction results
    """
    try:
        # Extract model information
        model = model_info['model']
        scaler = model_info.get('scaler')

        # Check for feature_columns key
        if 'feature_columns' not in model_info or not model_info['feature_columns']:
            print("No feature columns specified for Transformer model, using numeric columns")
            feature_columns = data.select_dtypes(include=['number']).columns.tolist()
        else:
            feature_columns = model_info['feature_columns']

        # Check for seq_length key
        if 'seq_length' not in model_info:
            print("No sequence length specified for Transformer model, using default of 30")
            seq_length = 30
        else:
            seq_length = model_info['seq_length']

        # Extract features
        missing_features = [f for f in feature_columns if f not in data.columns]
        if missing_features:
            print(f"Missing features for Transformer prediction: {missing_features}")
            # Add missing features with default values
            for feature in missing_features:
                data[feature] = 0

        features = data[feature_columns].copy()

        # Handle missing values
        features = features.fillna(0)

        # Check if the model expects a specific number of features
        try:
            # Try to get the expected number of features from the model
            expected_features = 0

            # First check if we have feature information from the model info
            if 'features' in model_info and model_info['features'] is not None:
                expected_features = len(model_info['features'])
                print(f"Using feature count from model_info: {expected_features}")

            # If not, try to extract from model architecture
            if expected_features == 0:
                for layer in model.layers:
                    if hasattr(layer, 'input_shape') and layer.input_shape is not None:
                        if isinstance(layer.input_shape, tuple) and len(layer.input_shape) > 1:
                            expected_features = layer.input_shape[1]
                            print(f"Extracted expected feature count from model architecture: {expected_features}")
                            break

            if expected_features > 0 and expected_features != features.shape[1]:
                print(f"Feature dimension mismatch: Model expects {expected_features} features, but got {features.shape[1]}")

                if features.shape[1] > expected_features:
                    print(f"Limiting features from {features.shape[1]} to {expected_features}")
                    # Keep only the first expected_features columns
                    features = features.iloc[:, :expected_features]
                else:
                    print(f"Padding features from {features.shape[1]} to {expected_features}")
                    # Pad with zeros
                    for i in range(features.shape[1], expected_features):
                        features[f'padding_{i}'] = 0

                print(f"After adjustment, feature shape: {features.shape}")
            else:
                print(f"Feature dimensions match: {features.shape[1]}")

        except Exception as e:
            print(f"Error checking model input shape: {e}")
            import traceback
            traceback.print_exc()

        # Scale features
        if scaler is not None:
            try:
                features_scaled = scaler.transform(features.values)
            except ValueError as e:
                print(f"Error with scaler: {e}")
                print("Using standard scaling instead")
                from sklearn.preprocessing import StandardScaler
                temp_scaler = StandardScaler()
                features_scaled = temp_scaler.fit_transform(features.values)
        else:
            # If no scaler is provided, standardize the data
            print("No scaler provided for Transformer model, using standard scaling")
            from sklearn.preprocessing import StandardScaler
            temp_scaler = StandardScaler()
            features_scaled = temp_scaler.fit_transform(features.values)

        # Create sequence for prediction
        if len(features_scaled) >= seq_length:
            # Get the last seq_length rows
            sequence = features_scaled[-seq_length:]

            # Reshape to match model's expected input shape
            sequence = np.array([sequence])

            # Double-check dimensions before prediction
            print(f"Sequence shape before prediction: {sequence.shape}")

            # Verify input shape matches model's expected input shape
            expected_input_shape = None
            for layer in model.layers:
                if hasattr(layer, 'input_shape') and layer.input_shape is not None:
                    expected_input_shape = layer.input_shape
                    break

            if expected_input_shape:
                print(f"Model's expected input shape: {expected_input_shape}")

                # Check if we need to reshape
                if len(expected_input_shape) > 2:
                    expected_seq_len = expected_input_shape[1]
                    expected_features = expected_input_shape[2]

                    if sequence.shape[1] != expected_seq_len or sequence.shape[2] != expected_features:
                        print(f"Reshaping sequence from {sequence.shape} to match expected shape ({expected_seq_len}, {expected_features})")

                        # Adjust sequence length if needed
                        if sequence.shape[1] > expected_seq_len:
                            sequence = sequence[:, -expected_seq_len:, :]
                        elif sequence.shape[1] < expected_seq_len:
                            # Pad with zeros if sequence is too short (should be rare)
                            padding = np.zeros((1, expected_seq_len - sequence.shape[1], sequence.shape[2]))
                            sequence = np.concatenate([padding, sequence], axis=1)

                        # Adjust feature count if needed
                        if sequence.shape[2] > expected_features:
                            sequence = sequence[:, :, :expected_features]
                        elif sequence.shape[2] < expected_features:
                            # Pad with zeros if features are too few
                            padding = np.zeros((1, sequence.shape[1], expected_features - sequence.shape[2]))
                            sequence = np.concatenate([sequence, padding], axis=2)

                        print(f"After reshaping, sequence shape: {sequence.shape}")

            try:
                # Make prediction with error handling
                prediction_prob = model.predict(sequence, verbose=0)[0][0]
                prediction = 1 if prediction_prob > 0.5 else 0
                confidence = max(prediction_prob, 1 - prediction_prob)

                return {
                    'prediction': prediction,
                    'probability': float(prediction_prob),
                    'confidence': float(confidence),
                    'direction': "UP" if prediction == 1 else "DOWN"
                }
            except Exception as e:
                print(f"Error in predict_with_transformer: Exception during model.predict(): {e}")
                import traceback
                traceback.print_exc()

                # Return error information
                return {
                    'prediction': None,
                    'probability': None,
                    'confidence': None,
                    'direction': "UNKNOWN",
                    'error': f"Prediction failed: {str(e)}"
                }
        else:
            # Not enough data for prediction
            print(f"Not enough data for Transformer prediction. Need {seq_length} samples, got {len(features_scaled)}")
            return {
                'prediction': None,
                'probability': None,
                'confidence': None,
                'direction': "UNKNOWN",
                'error': f"Not enough data for prediction. Need {seq_length} samples, got {len(features_scaled)}"
            }
    except Exception as e:
        print(f"Error in predict_with_transformer: {e}")
        import traceback
        traceback.print_exc()

        # Check if this is a dimension mismatch error
        error_str = str(e)
        if "Dimensions must be equal" in error_str or "shape" in error_str:
            print("Detected dimension mismatch error. This is likely due to a feature count mismatch.")

            # Try to extract the expected and actual dimensions from the error message
            import re
            dims = re.findall(r'(\d+)', error_str)
            if len(dims) >= 2:
                print(f"Error indicates mismatch between dimensions {dims[0]} and {dims[1]}")
                print("Please ensure the model was trained with the same feature set being used for prediction.")
                print("You may need to retrain the model with the current feature set.")

            # Return a more informative error
            return {
                'prediction': None,
                'probability': None,
                'confidence': None,
                'direction': "UNKNOWN",
                'error': f"Feature dimension mismatch: {error_str}",
                'suggestion': "Model may need to be retrained with current feature set"
            }
        else:
            # For other errors, return a fallback prediction with low confidence
            import random
            random_pred = random.choice([0, 1])
            return {
                'prediction': random_pred,
                'probability': 0.51 if random_pred == 1 else 0.49,
                'confidence': 0.51,
                'direction': "UP" if random_pred == 1 else "DOWN",
                'note': f"Fallback prediction due to error: {str(e)}",
                'error_details': traceback.format_exc()
            }
