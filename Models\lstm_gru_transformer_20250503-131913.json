{"module": "keras.src.models.functional", "class_name": "Functional", "config": {"name": "functional_2", "trainable": true, "layers": [{"module": "keras.layers", "class_name": "InputLayer", "config": {"batch_shape": [null, 30, 229], "dtype": "float32", "sparse": false, "ragged": false, "name": "input_layer_2"}, "registered_name": null, "name": "input_layer_2", "inbound_nodes": []}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_5", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 64, "kernel_size": [3], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 229]}, "name": "conv1d_5", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 229], "dtype": "float32", "keras_history": ["input_layer_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_13", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "batch_normalization_13", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["conv1d_5", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Conv1D", "config": {"name": "conv1d_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "filters": 64, "kernel_size": [3], "strides": [1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1], "groups": 1, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "conv1d_6", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["batch_normalization_13", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_14", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "batch_normalization_14", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["conv1d_6", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "MultiHeadAttention", "config": {"name": "multi_head_attention_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "num_heads": 4, "key_dim": 16, "value_dim": 16, "dropout": 0.0, "use_bias": true, "output_shape": null, "attention_axes": [1], "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "seed": null}, "registered_name": null, "build_config": {"shapes_dict": {"query_shape": [null, 30, 64], "value_shape": [null, 30, 64]}}, "name": "multi_head_attention_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["batch_normalization_14", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["batch_normalization_14", 0, 0]}}], "kwargs": {"query_mask": null, "value_mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_11", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.1, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_11", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["multi_head_attention_1", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 64], [null, 30, 64]]}, "name": "add_3", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["batch_normalization_14", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dropout_11", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "LayerNormalization", "config": {"name": "layer_normalization_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": [-1], "epsilon": 1e-06, "center": true, "scale": true, "rms_scaling": false, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "layer_normalization_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["add_3", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_8", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "dense_8", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_1", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_9", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}, "name": "dense_9", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["dense_8", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_12", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.1, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_12", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dense_9", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_4", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 64], [null, 30, 64]]}, "name": "add_4", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_1", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dropout_12", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "LayerNormalization", "config": {"name": "layer_normalization_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": [-1], "epsilon": 1e-06, "center": true, "scale": true, "rms_scaling": false, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "layer_normalization_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["add_4", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "MultiHeadAttention", "config": {"name": "multi_head_attention_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "num_heads": 4, "key_dim": 16, "value_dim": 16, "dropout": 0.0, "use_bias": true, "output_shape": null, "attention_axes": [1], "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "seed": null}, "registered_name": null, "build_config": {"shapes_dict": {"query_shape": [null, 30, 64], "value_shape": [null, 30, 64]}}, "name": "multi_head_attention_2", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_2", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_2", 0, 0]}}], "kwargs": {"query_mask": null, "value_mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_14", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.1, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_14", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["multi_head_attention_2", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_5", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 64], [null, 30, 64]]}, "name": "add_5", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_2", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dropout_14", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "LayerNormalization", "config": {"name": "layer_normalization_3", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": [-1], "epsilon": 1e-06, "center": true, "scale": true, "rms_scaling": false, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "layer_normalization_3", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["add_5", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_10", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "dense_10", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_3", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_11", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "linear", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 128]}, "name": "dense_11", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 128], "dtype": "float32", "keras_history": ["dense_10", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_15", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.1, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_15", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dense_11", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "Add", "config": {"name": "add_6", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}}, "registered_name": null, "build_config": {"input_shape": [[null, 30, 64], [null, 30, 64]]}, "name": "add_6", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_3", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["dropout_15", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "LayerNormalization", "config": {"name": "layer_normalization_4", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": [-1], "epsilon": 1e-06, "center": true, "scale": true, "rms_scaling": false, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 30, 64]}, "name": "layer_normalization_4", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["add_6", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "GlobalAveragePooling1D", "config": {"name": "global_average_pooling1d_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_average_pooling1d_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_4", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "GlobalMaxPooling1D", "config": {"name": "global_max_pooling1d_1", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "data_format": "channels_last", "keepdims": false}, "registered_name": null, "name": "global_max_pooling1d_1", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 30, 64], "dtype": "float32", "keras_history": ["layer_normalization_4", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "Concatenate", "config": {"name": "concatenate_2", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1}, "registered_name": null, "build_config": {"input_shape": [[null, 64], [null, 64]]}, "name": "concatenate_2", "inbound_nodes": [{"args": [[{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["global_average_pooling1d_1", 0, 0]}}, {"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["global_max_pooling1d_1", 0, 0]}}]], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_12", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": {"module": "keras.regularizers", "class_name": "L1L2", "config": {"l1": 1e-05, "l2": 0.0001}, "registered_name": null}, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dense_12", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["concatenate_2", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_15", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "batch_normalization_15", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["dense_12", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_16", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.3, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_16", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["batch_normalization_15", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_13", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": {"module": "keras.regularizers", "class_name": "L1L2", "config": {"l1": 1e-05, "l2": 0.0001}, "registered_name": null}, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 128]}, "name": "dense_13", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 128], "dtype": "float32", "keras_history": ["dropout_16", 0, 0]}}], "kwargs": {}}]}, {"module": "keras.layers", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_16", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "gamma_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "moving_mean_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "moving_variance_initializer": {"module": "keras.initializers", "class_name": "Ones", "config": {}, "registered_name": null}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "synchronized": false}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "batch_normalization_16", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dense_13", 0, 0]}}], "kwargs": {"mask": null}}]}, {"module": "keras.layers", "class_name": "Dropout", "config": {"name": "dropout_17", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "rate": 0.3, "seed": null, "noise_shape": null}, "registered_name": null, "name": "dropout_17", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["batch_normalization_16", 0, 0]}}], "kwargs": {"training": false}}]}, {"module": "keras.layers", "class_name": "<PERSON><PERSON>", "config": {"name": "dense_14", "trainable": true, "dtype": {"module": "keras", "class_name": "DTypePolicy", "config": {"name": "float32"}, "registered_name": null}, "units": 1, "activation": "sigmoid", "use_bias": true, "kernel_initializer": {"module": "keras.initializers", "class_name": "GlorotUniform", "config": {"seed": null}, "registered_name": null}, "bias_initializer": {"module": "keras.initializers", "class_name": "Zeros", "config": {}, "registered_name": null}, "kernel_regularizer": null, "bias_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "registered_name": null, "build_config": {"input_shape": [null, 64]}, "name": "dense_14", "inbound_nodes": [{"args": [{"class_name": "__keras_tensor__", "config": {"shape": [null, 64], "dtype": "float32", "keras_history": ["dropout_17", 0, 0]}}], "kwargs": {}}]}], "input_layers": [["input_layer_2", 0, 0]], "output_layers": [["dense_14", 0, 0]]}, "registered_name": "Functional", "build_config": {"input_shape": null}, "compile_config": {"loss": "binary_crossentropy", "loss_weights": null, "metrics": ["accuracy", {"module": "keras.metrics", "class_name": "AUC", "config": {"name": "auc_2", "dtype": "float32", "num_thresholds": 200, "curve": "ROC", "summation_method": "interpolation", "multi_label": false, "num_labels": null, "label_weights": null, "from_logits": false}, "registered_name": null}, {"module": "keras.metrics", "class_name": "Precision", "config": {"name": "precision_2", "dtype": "float32", "thresholds": null, "top_k": null, "class_id": null}, "registered_name": null}, {"module": "keras.metrics", "class_name": "Recall", "config": {"name": "recall_2", "dtype": "float32", "thresholds": null, "top_k": null, "class_id": null}, "registered_name": null}], "weighted_metrics": null, "run_eagerly": false, "steps_per_execution": 1, "jit_compile": false}}