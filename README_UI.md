# PyQuotex Trading UI

A comprehensive PyQt-based user interface for the PyQuotex trading system with analytics, live charts, and prediction visualization.

## Features

- Live candlestick chart display
- Future candle prediction visualization
- Learning data analytics
- Model performance tracking
- Trade history display
- Market analysis information

## Requirements

- Python 3.7-3.11 (required for TensorFlow compatibility)
- PyQt5
- pyqtgraph
- pandas
- numpy
- matplotlib
- joblib
- scikit-learn

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. If you want to use TensorFlow features, install TensorFlow:

```bash
pip install tensorflow
```

## Usage

### Running the UI

To run the UI with the default settings:

```bash
python ui_controller.py
```

To specify a specific candle data CSV file:

```bash
python ui_controller.py path/to/your/candles.csv
```

### UI Components

The UI consists of several tabs:

1. **Dashboard**: Overview of trading system with live chart and current prediction
2. **Charts**: Detailed chart view with timeframe and indicator selection
3. **Predictions**: Future candle predictions with confidence visualization
4. **Learning**: Learning progress and model performance metrics
5. **Settings**: Configuration options for the trading system

## Integration with Trading System

The UI controller (`ui_controller.py`) connects the UI with the trading system components. If the trading system components are not available, the UI will run in demo mode with sample data.

## Customization

You can customize the UI by modifying the following files:

- `trading_ui.py`: Main UI implementation
- `chart_widgets.py`: Chart visualization widgets
- `analytics_widgets.py`: Analytics display widgets
- `ui_controller.py`: Controller connecting UI with trading system

## License

This project is licensed under the MIT License - see the LICENSE file for details.
