#!/usr/bin/env python
"""
Train XGBoost model from CSV data and save it for future use.
This script processes the data, trains the model, and saves it for future use.
"""

import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

# Import custom modules
from Models.Feature_Engineering import engineer_features
from Models.XGBoost_Model import train_xgboost_model

def prepare_target(df, horizon=1):
    """
    Prepare target variable for binary classification
    
    Parameters:
    - df: DataFrame with OHLC data
    - horizon: Prediction horizon in minutes
    
    Returns:
    - df: DataFrame with target variable
    """
    print(f"Preparing target variable for {horizon} minute horizon...")
    
    # Create target variable (1 if price goes up, 0 if it goes down)
    df[f'target_{horizon}min'] = (df['close'].shift(-horizon) > df['close']).astype(int)
    
    # Remove rows with NaN target
    df = df.dropna(subset=[f'target_{horizon}min'])
    
    return df

def load_and_process_data(csv_path, cache_dir='data_cache'):
    """
    Load data from CSV, process it, and cache the processed data
    
    Parameters:
    - csv_path: Path to CSV file
    - cache_dir: Directory to cache processed data
    
    Returns:
    - df: Processed DataFrame
    """
    # Create cache directory if it doesn't exist
    os.makedirs(cache_dir, exist_ok=True)
    
    # Generate cache filename based on input file
    filename = os.path.basename(csv_path)
    cache_path = os.path.join(cache_dir, f"processed_{filename}.pkl")
    
    # Check if cached data exists
    if os.path.exists(cache_path):
        print(f"Loading cached processed data from {cache_path}")
        return joblib.load(cache_path)
    
    # Load data from CSV
    print(f"Loading data from {csv_path}")
    df = pd.read_csv(csv_path)
    
    # Convert timestamp to datetime
    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Process data
    print("Engineering features...")
    df = engineer_features(df)
    
    # Prepare target variables for different horizons
    for horizon in [1, 3, 5]:
        df = prepare_target(df, horizon)
    
    # Cache processed data
    print(f"Caching processed data to {cache_path}")
    joblib.dump(df, cache_path)
    
    return df

def main():
    """Main function to train XGBoost model from CSV data"""
    # Configuration
    csv_path = 'Models/usdars_otc_20250426_234310_candles.csv'
    model_dir = 'models'
    cache_dir = 'data_cache'
    
    # Create directories
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)
    
    # Load and process data
    df = load_and_process_data(csv_path, cache_dir)
    
    # Print data info
    print(f"\nData shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    print(f"Sample data:\n{df.head()}")
    
    # Remove timestamp and other non-feature columns for training
    feature_columns = [col for col in df.columns if col not in [
        'timestamp', 'time', 'open', 'high', 'low', 'close', 'color', 'ticks',
        'target_1min', 'target_3min', 'target_5min'
    ]]
    
    # Train models for different horizons
    for horizon in [1, 3, 5]:
        target_column = f'target_{horizon}min'
        
        print(f"\n{'='*80}")
        print(f"Training XGBoost model for {horizon} minute horizon")
        print(f"{'='*80}")
        
        # Train model
        model, feature_importance, evaluation, selected_features = train_xgboost_model(
            df=df,
            feature_columns=feature_columns,
            target_column=target_column,
            optimize_hyperparams=True,
            feature_selection=True,
            test_size=0.2,
            random_state=42,
            model_dir=model_dir
        )
        
        # Print evaluation summary
        print(f"\nModel for {horizon} minute horizon:")
        print(f"Accuracy: {evaluation['accuracy']:.4f}")
        print(f"ROC AUC: {evaluation['roc_auc']:.4f}")
        print(f"Selected {len(selected_features)} features")
        
        # Save evaluation metrics
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        eval_path = os.path.join(model_dir, f"xgboost_evaluation_{horizon}min_{timestamp}.txt")
        with open(eval_path, 'w') as f:
            f.write(f"Evaluation for {horizon} minute horizon:\n")
            for metric, value in evaluation.items():
                f.write(f"{metric}: {value:.4f}\n")
            f.write(f"\nTop 10 features:\n")
            for i, (feature, importance) in enumerate(list(feature_importance.items())[:10]):
                f.write(f"{i+1}. {feature}: {importance:.4f}\n")
        
        print(f"Evaluation saved to {eval_path}")

if __name__ == "__main__":
    main()
