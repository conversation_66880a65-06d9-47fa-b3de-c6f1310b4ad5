# Quotex Chart Tester

A dedicated testing application for fetching and displaying real-time chart data from the Quotex API.

## Features

- **Direct Quotex API Integration**:
  - Connects directly to the Quotex API using your credentials
  - Fetches real candle data for both regular and OTC markets
  - Displays detailed debugging information about the API connection

- **Professional Chart Display**:
  - Renders proper candlestick charts with accurate OHLC values
  - Shows time axis with correct timestamps from Quotex
  - Displays current price line and time indicators
  - Uses appropriate color scheme matching Quotex's platform

- **Automatic Live Candle Generation**:
  - Always active after fetching initial data
  - Simulates real-time price movements with realistic volatility
  - Updates candle data every second with smooth transitions
  - Creates new candles at regular intervals
  - Visual indicators show live status with price direction (▲/▼)
  - Color-coded price updates based on movement direction

- **Flexible Configuration**:
  - Allows selection of different assets (EURUSD, GBPUSD, etc.)
  - Supports multiple timeframes (1min, 5min, 15min, etc.)
  - Adjustable candle count for history display
  - Auto-update feature to keep the chart current

- **Detailed Data Analysis**:
  - Shows raw candle data for debugging
  - Calculates statistics like price change and average candle size
  - Validates price values to ensure they're in the expected range
  - Logs detailed information about each candle

## Requirements

- Python 3.7+
- PyQt5
- PyQtGraph
- NumPy
- quotexapi (https://github.com/Lu-Yi-Hsun/quotexapi)

## Installation

1. Install the required packages:
   ```
   pip install PyQt5 pyqtgraph numpy quotexapi
   ```

## Usage

1. **Run the Application**:
   ```
   python quotex_chart_tester.py
   ```

2. **Set Up Credentials**:
   - Click the "Set API Credentials" button in the application
   - Enter your Quotex email and password
   - Click OK to save and connect

3. **Configure and Test**:
   - Select an asset (e.g., "EURUSD" or "EURUSD_otc")
   - Choose a timeframe (1min is recommended for testing)
   - Set the number of candles to fetch
   - Click "Fetch Data" to retrieve and display the chart
   - Enable "Auto Update" to keep the chart refreshing

4. **Live Mode (Always Active)**:
   - Live mode is automatically enabled after fetching data
   - Watch as the price updates in real-time with realistic movements
   - New candles are automatically created at regular intervals
   - The status bar shows the current price with direction indicators (▲ or ▼)
   - A "LIVE" indicator appears on the chart

5. **Verify the Data**:
   - Check that the price values are correct (around 1.12 for EURUSD)
   - Verify that the timestamps match the Quotex platform
   - Ensure the candle colors and shapes are accurate
   - Review the debug log for detailed information

## Troubleshooting

If you encounter connection issues:

1. **Check Credentials**:
   - Ensure your Quotex email and password are correct
   - Use the "Set API Credentials" button to update them

2. **Check Internet Connection**:
   - Make sure you have a stable internet connection
   - Check if you can access the Quotex website in your browser

3. **Check API Module**:
   - Ensure the quotexapi module is properly installed
   - Try reinstalling it: `pip install --upgrade quotexapi`

4. **Use Simulated Data**:
   - If the API connection fails, you can use simulated data for testing
   - The application will prompt you to use simulated data when a connection error occurs

## How It Works

The application uses the following components:

1. **AsyncHelper**: Manages asynchronous operations with the Quotex API
2. **CandlestickItem**: Renders candlestick charts using PyQtGraph
3. **TimeAxisItem**: Formats the time axis to show proper timestamps
4. **QuotexChartTester**: Main application window with UI controls

When you click "Fetch Data", the application:
1. Connects to the Quotex API using your credentials
2. Retrieves candle data for the selected asset and timeframe
3. Processes and validates the data
4. Renders the candlestick chart with proper formatting
5. Displays statistics and debugging information

## Notes

- This application is for testing purposes only
- It helps verify that you can properly retrieve and display data from the Quotex API
- The simulated data option is provided as a fallback when the API connection fails
- All chart data is displayed with proper EURUSD price values (around 1.12)

## License

This project is licensed under the MIT License.
