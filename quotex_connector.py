#!/usr/bin/env python
"""
Quotex API Connector
This script provides a standalone connection to the Quotex API
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime

# Try to import Quotex API
try:
    from quotexapi.stable_api import Quotex
except ImportError as e:
    print(f"Error importing quotexapi module: {e}")
    print("Please install it with: pip install quotexapi")
    sys.exit(1)

# Read credentials from config.ini
def read_credentials():
    """Read credentials from config.ini file"""
    email = ""
    password = ""

    try:
        # Check if config.ini exists
        if os.path.exists('config.ini'):
            print(f"Found config file: config.ini")

            # Read the file
            with open('config.ini', 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip().startswith('email='):
                        email = line.strip().replace('email=', '')
                    elif line.strip().startswith('password='):
                        password = line.strip().replace('password=', '')

            if email and password:
                print(f"Loaded credentials from config.ini for: {email}")
                return email, password
    except Exception as e:
        print(f"Error reading config.ini: {e}")

    # If no credentials found, try environment variables
    email = os.environ.get('QUOTEX_EMAIL', '')
    password = os.environ.get('QUOTEX_PASSWORD', '')

    if email and password:
        print(f"Loaded credentials from environment variables for: {email}")
        return email, password

    # If still no credentials, prompt user
    if not email:
        email = input("Enter your Quotex email: ")
    if not password:
        password = input("Enter your Quotex password: ")

    return email, password

# Connect to Quotex API
async def connect_to_quotex(email, password):
    """Connect to Quotex API and return client if successful"""
    print(f"Connecting to Quotex API with email: {email}")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Create client
        print("Creating Quotex client...")
        client = Quotex(email, password)

        # Connect
        print("Connecting to Quotex API...")
        print("If a PIN verification is required, you'll need to enter it in the console.")
        print("Please check your email for the PIN code.")

        # Set up a PIN verification handler
        def pin_handler():
            print("PIN VERIFICATION REQUIRED!")
            print("Please check your email for the PIN code.")
            pin = input("Enter PIN code from email: ")
            print(f"PIN entered: {pin}")
            return pin

        # Try to connect with PIN handler
        try:
            # First attempt without PIN handler
            connected = await client.connect()
        except Exception as pin_error:
            # Check if it's a PIN verification error
            error_str = str(pin_error).lower()
            if "pin" in error_str or "verification" in error_str or "code" in error_str or "token rejected" in error_str:
                print("PIN verification required. Retrying with PIN handler...")
                # Try again with PIN handler
                try:
                    # This is a workaround since the current API doesn't directly support PIN handlers
                    # In a real implementation, we would pass the PIN handler to the connect method
                    # For now, we'll just retry the connection and hope the user enters the PIN in the console
                    connected = await client.connect()
                except Exception as retry_error:
                    print(f"Error during PIN verification retry: {retry_error}")
                    return None
            else:
                # Not a PIN verification error
                print(f"Connection error (not PIN related): {pin_error}")
                return None

        if connected:
            print("Successfully connected to Quotex API")
            return client
        else:
            print("Failed to connect to Quotex API")
            return None
    except Exception as e:
        print(f"Error connecting to Quotex API: {e}")
        import traceback
        traceback.print_exc()
        return None

# Get available assets
async def get_assets(client):
    """Get available assets from Quotex API"""
    try:
        # Try different method names that might exist in the API
        try:
            print("Trying get_all_assets method...")
            assets = await client.get_all_assets()
        except AttributeError:
            try:
                print("Trying get_all_asset method...")
                assets = await client.get_all_asset()
            except AttributeError:
                try:
                    print("Trying get_assets method...")
                    assets = await client.get_assets()
                except AttributeError:
                    print("Trying get_asset method...")
                    assets = await client.get_asset()

        return assets
    except Exception as e:
        print(f"Error getting assets: {e}")
        return None

# Main function
async def main():
    """Main function"""
    # Read credentials
    email, password = read_credentials()

    if not email or not password:
        print("No credentials provided. Exiting.")
        result = {
            "success": False,
            "error": "No credentials provided"
        }
        print(json.dumps(result))
        return

    # Connect to Quotex API
    client = await connect_to_quotex(email, password)

    if client:
        # Get available assets
        assets = await get_assets(client)

        if assets:
            print(f"Available assets: {assets}")
            print(f"Total assets: {len(assets)}")

            # Return success result
            result = {
                "success": True,
                "assets": assets,
                "total_assets": len(assets)
            }
            print(json.dumps(result))
        else:
            # Return error result
            result = {
                "success": False,
                "error": "Failed to get assets"
            }
            print(json.dumps(result))
    else:
        # Return error result
        result = {
            "success": False,
            "error": "Failed to connect to Quotex API"
        }
        print(json.dumps(result))

# Run the main function
if __name__ == "__main__":
    # Create a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # Run the main function
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("Interrupted by user")
        result = {
            "success": False,
            "error": "Interrupted by user"
        }
        print(json.dumps(result))
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()

        # Return error result
        result = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(result))
    finally:
        # Close the event loop
        loop.close()
